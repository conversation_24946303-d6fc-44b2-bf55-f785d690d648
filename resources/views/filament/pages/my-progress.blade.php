@php
    $progressData = $this->getProgressData();
@endphp

<x-filament-panels::page>
    <div class="space-y-6">
        {{-- <PERSON> Header --}}
        <div class="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-xl p-6 border border-green-200 dark:border-green-700">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="text-3xl mr-3">📈</span>
                My Carbon Progress
            </h1>
            <p class="text-gray-600 dark:text-gray-300 mt-2">
                Track your journey towards a more sustainable future. Every action counts!
            </p>
        </div>

        {{-- Annual Progress Overview --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">🎯 Annual Carbon Reduction Goal</h2>
            
            <div class="mb-6">
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Progress: {{ $progressData['current_progress'] }} / {{ $progressData['annual_target'] }} tCO₂e
                    </span>
                    <span class="text-sm font-medium text-green-600 dark:text-green-400">
                        {{ $progressData['progress_percentage'] }}% complete
                    </span>
                </div>
                
                {{-- Progress Bar --}}
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4 mb-4">
                    <div class="bg-gradient-to-r from-green-500 to-blue-500 h-4 rounded-full transition-all duration-500" 
                         style="width: {{ $progressData['progress_percentage'] }}%"></div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        🎉 {{ $progressData['progress_percentage'] >= 50 ? 'Great progress!' : 'Keep going!' }}
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 mt-1">
                        {{ $progressData['progress_percentage'] >= 75 ? 'You\'re almost there!' : 
                           ($progressData['progress_percentage'] >= 50 ? 'You\'re ahead of schedule!' : 'You\'re on the right track!') }}
                    </p>
                </div>
            </div>
        </div>

        {{-- Monthly Progress --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📅 Monthly Progress</h2>
            
            <div class="grid grid-cols-5 gap-4">
                @foreach($progressData['monthly_data'] as $month => $data)
                    <div class="text-center">
                        <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ $month }}</div>
                        
                        {{-- Progress Circle --}}
                        <div class="relative w-16 h-16 mx-auto mb-2">
                            <svg class="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                                <path class="text-gray-200 dark:text-gray-700" stroke="currentColor" stroke-width="3" fill="none" 
                                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                <path class="{{ $data['percentage'] >= 100 ? 'text-green-500' : ($data['percentage'] >= 80 ? 'text-yellow-500' : 'text-red-500') }}" 
                                      stroke="currentColor" stroke-width="3" fill="none" stroke-linecap="round"
                                      stroke-dasharray="{{ $data['percentage'] }}, 100"
                                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                            </svg>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-xs font-semibold">{{ $data['percentage'] }}%</span>
                            </div>
                        </div>
                        
                        <div class="text-xs text-gray-600 dark:text-gray-400">
                            {{ $data['actual'] }} / {{ $data['target'] }} tCO₂e
                        </div>
                        
                        @if($data['percentage'] >= 100)
                            <div class="text-xs text-green-600 dark:text-green-400 mt-1">✅ Goal exceeded!</div>
                        @elseif($data['percentage'] >= 80)
                            <div class="text-xs text-yellow-600 dark:text-yellow-400 mt-1">⚠️ Close to goal</div>
                        @elseif($data['actual'] > 0)
                            <div class="text-xs text-red-600 dark:text-red-400 mt-1">📈 Needs improvement</div>
                        @else
                            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">⏳ In progress</div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>

        {{-- Achievements & Milestones --}}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {{-- Achievements --}}
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">🏆 My Achievements</h2>
                
                <div class="space-y-4">
                    @foreach($progressData['achievements'] as $achievement)
                        <div class="flex items-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700">
                            <div class="text-3xl mr-4">{{ $achievement['icon'] }}</div>
                            <div class="flex-1">
                                <div class="font-semibold text-gray-900 dark:text-white">{{ $achievement['title'] }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">{{ $achievement['description'] }}</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    Earned {{ \Carbon\Carbon::parse($achievement['earned_date'])->format('M j, Y') }} • {{ $achievement['points'] }} points
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            {{-- Upcoming Milestones --}}
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">🎯 Upcoming Milestones</h2>
                
                <div class="space-y-4">
                    @foreach($progressData['upcoming_milestones'] as $milestone)
                        <div class="p-3 rounded-lg border 
                            {{ $milestone['status'] === 'completed' ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700' : 
                               ($milestone['status'] === 'in_progress' ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700' : 
                                'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600') }}">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="font-semibold text-gray-900 dark:text-white flex items-center">
                                        @if($milestone['status'] === 'completed')
                                            <span class="text-green-500 mr-2">✅</span>
                                        @elseif($milestone['status'] === 'in_progress')
                                            <span class="text-blue-500 mr-2">🔄</span>
                                        @else
                                            <span class="text-gray-400 mr-2">⏳</span>
                                        @endif
                                        {{ $milestone['title'] }}
                                    </div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ $milestone['description'] }}</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-semibold">{{ $milestone['current'] }}%</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">of {{ $milestone['target'] }}%</div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        {{-- Comparison & Insights --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📊 How Am I Doing?</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                @foreach($progressData['comparison_data'] as $key => $comparison)
                    <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="text-2xl font-bold text-{{ $comparison['direction'] === 'improvement' ? 'green' : 'red' }}-600 dark:text-{{ $comparison['direction'] === 'improvement' ? 'green' : 'red' }}-400">
                            {{ $comparison['direction'] === 'improvement' ? '↗️' : '↘️' }} {{ $comparison['percentage'] }}%
                        </div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white mt-2">
                            {{ str_replace('_', ' ', ucwords($key, '_')) }}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                            {{ $comparison['message'] }}
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        {{-- Motivational Message --}}
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-700">
            <div class="flex items-center">
                <div class="text-4xl mr-4">🌟</div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Keep Up the Great Work!</h3>
                    <p class="text-gray-700 dark:text-gray-300 mt-1">
                        Your efforts are making a real difference. You've already saved {{ $progressData['current_progress'] }} tCO₂e this year - 
                        that's equivalent to planting {{ round($progressData['current_progress'] * 16) }} trees! 🌳
                    </p>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
