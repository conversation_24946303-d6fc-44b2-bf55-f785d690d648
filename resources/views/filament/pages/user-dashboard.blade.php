<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Welcome Section --}}
        <div class="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-xl p-6 border border-green-200 dark:border-green-700">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        🌱 Welcome back, {{ auth()->user()->name ?? 'User' }}!
                    </h1>
                    <p class="text-gray-600 dark:text-gray-300 mt-2">
                        Let's make a positive impact on our planet together.
                    </p>
                </div>
                <div class="hidden md:block">
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 dark:text-gray-400">Today's Impact</div>
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                            {{ number_format(rand(5, 25) / 10, 1) }} tCO₂e
                        </div>
                        <div class="text-xs text-green-600 dark:text-green-400">
                            ↓ {{ rand(5, 15) }}% vs yesterday
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- The impact overview, progress, tasks, and quick actions will be rendered by widgets below --}}

        {{-- Widgets will be rendered here automatically by Filament --}}
    </div>
</x-filament-panels::page>
