<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Welcome Section --}}
        <div class="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-xl p-6 border border-green-200 dark:border-green-700">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        🌱 Welcome back, {{ auth()->user()->name ?? 'User' }}!
                    </h1>
                    <p class="text-gray-600 dark:text-gray-300 mt-2">
                        Let's make a positive impact on our planet together.
                    </p>
                </div>
                <div class="hidden md:block">
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 dark:text-gray-400">Today's Impact</div>
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                            {{ number_format(rand(5, 25) / 10, 1) }} tCO₂e
                        </div>
                        <div class="text-xs text-green-600 dark:text-green-400">
                            ↓ {{ rand(5, 15) }}% vs yesterday
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Quick Stats Grid --}}
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            {{-- My Impact Card --}}
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">📊 My Impact</h3>
                        <div class="mt-2">
                            <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">
                                {{ number_format(rand(40, 60), 1) }} tCO₂e
                            </div>
                            <div class="text-sm text-green-600 dark:text-green-400 flex items-center mt-1">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                {{ rand(8, 15) }}% improvement
                            </div>
                        </div>
                    </div>
                    <div class="text-4xl">📈</div>
                </div>
            </div>

            {{-- My Goals Card --}}
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">🎯 My Goals</h3>
                        <div class="mt-2">
                            <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                                {{ rand(60, 80) }}% to goal
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                On track ✅
                            </div>
                        </div>
                        {{-- Progress Bar --}}
                        <div class="mt-3">
                            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: {{ rand(60, 80) }}%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="text-4xl ml-4">🎯</div>
                </div>
            </div>

            {{-- My Tasks Card --}}
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">✅ My Tasks</h3>
                        <div class="mt-2">
                            <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">
                                {{ rand(2, 5) }} pending
                            </div>
                            <div class="text-sm text-red-600 dark:text-red-400 mt-1">
                                1 due this week
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="#" class="text-sm text-blue-600 dark:text-blue-400 hover:underline">
                                View all tasks →
                            </a>
                        </div>
                    </div>
                    <div class="text-4xl">📋</div>
                </div>
            </div>
        </div>

        {{-- Quick Actions Section --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">🚀 Quick Actions</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="#" class="flex flex-col items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors group">
                    <div class="text-3xl mb-2">📝</div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white text-center">Add Emissions Data</span>
                </a>
                
                <a href="#" class="flex flex-col items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors group">
                    <div class="text-3xl mb-2">📊</div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white text-center">View My Report</span>
                </a>
                
                <a href="#" class="flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors group">
                    <div class="text-3xl mb-2">🎯</div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white text-center">Update Progress</span>
                </a>
                
                <a href="#" class="flex flex-col items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors group">
                    <div class="text-3xl mb-2">❓</div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white text-center">Get Help</span>
                </a>
            </div>
        </div>

        {{-- Recent Activity --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">📈 Recent Activity</h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center">
                        <div class="text-2xl mr-3">🚗</div>
                        <div>
                            <div class="font-medium text-gray-900 dark:text-white">Business Travel</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">250 miles • 0.12 tCO₂e</div>
                        </div>
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">2 days ago</div>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center">
                        <div class="text-2xl mr-3">⚡</div>
                        <div>
                            <div class="font-medium text-gray-900 dark:text-white">Office Energy Use</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">1,250 kWh • 0.45 tCO₂e</div>
                        </div>
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">1 week ago</div>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center">
                        <div class="text-2xl mr-3">✈️</div>
                        <div>
                            <div class="font-medium text-gray-900 dark:text-white">Air Travel</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">NYC to LA • 1.2 tCO₂e</div>
                        </div>
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">2 weeks ago</div>
                </div>
            </div>
            
            <div class="mt-4 text-center">
                <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                    View all activity →
                </a>
            </div>
        </div>

        {{-- Tips & Insights --}}
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">💡 Today's Tip</h3>
            <div class="flex items-start">
                <div class="text-2xl mr-3">🌱</div>
                <div>
                    <p class="text-gray-700 dark:text-gray-300">
                        <strong>Switch to video calls:</strong> Replacing one business trip with a video conference can save up to 2.3 tCO₂e. 
                        You've already saved <strong>4.6 tCO₂e</strong> this month by choosing virtual meetings!
                    </p>
                    <a href="#" class="text-green-600 dark:text-green-400 hover:underline text-sm mt-2 inline-block">
                        Learn more sustainability tips →
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
