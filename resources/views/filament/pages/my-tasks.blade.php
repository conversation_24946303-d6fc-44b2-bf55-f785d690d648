@php
    $tasksData = $this->getTasksData();
@endphp

<x-filament-panels::page>
    <div class="space-y-6">
        {{-- <PERSON> Header --}}
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="text-3xl mr-3">✅</span>
                My Tasks
            </h1>
            <p class="text-gray-600 dark:text-gray-300 mt-2">
                Stay on top of your carbon management responsibilities and track your progress.
            </p>
        </div>

        {{-- Task Summary --}}
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="text-2xl mr-3">📋</div>
                    <div>
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            {{ $tasksData['task_summary']['total_pending'] }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Pending Tasks</div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="text-2xl mr-3">⚠️</div>
                    <div>
                        <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                            {{ $tasksData['task_summary']['total_overdue'] }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Overdue</div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="text-2xl mr-3">✅</div>
                    <div>
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                            {{ $tasksData['task_summary']['total_completed_this_month'] }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Completed This Month</div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="text-2xl mr-3">📊</div>
                    <div>
                        <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                            {{ $tasksData['task_summary']['completion_rate'] }}%
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Completion Rate</div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Overdue Tasks (if any) --}}
        @if(!empty($tasksData['overdue_tasks']))
            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-xl p-6">
                <h2 class="text-xl font-semibold text-red-800 dark:text-red-300 mb-4 flex items-center">
                    <span class="text-2xl mr-2">🚨</span>
                    Overdue Tasks - Immediate Attention Required
                </h2>
                
                <div class="space-y-4">
                    @foreach($tasksData['overdue_tasks'] as $task)
                        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-red-300 dark:border-red-600">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900 dark:text-white">{{ $task['title'] }}</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $task['description'] }}</p>
                                    <div class="flex items-center mt-2 space-x-4 text-sm">
                                        <span class="text-red-600 dark:text-red-400">
                                            <strong>{{ $task['days_overdue'] }} days overdue</strong>
                                        </span>
                                        <span class="text-gray-500 dark:text-gray-400">Due: {{ \Carbon\Carbon::parse($task['due_date'])->format('M j, Y') }}</span>
                                        <span class="px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded-full text-xs">
                                            {{ ucfirst($task['category']) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="flex space-x-2 ml-4">
                                    <button wire:click="markTaskComplete({{ $task['id'] }})" 
                                            class="px-3 py-1 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 transition-colors">
                                        Complete
                                    </button>
                                    <button wire:click="requestExtension({{ $task['id'] }})" 
                                            class="px-3 py-1 bg-yellow-600 text-white rounded-lg text-sm hover:bg-yellow-700 transition-colors">
                                        Request Extension
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        {{-- Pending Tasks --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <span class="text-2xl mr-2">📋</span>
                Pending Tasks
            </h2>
            
            @if(!empty($tasksData['pending_tasks']))
                <div class="space-y-4">
                    @foreach($tasksData['pending_tasks'] as $task)
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <h3 class="font-semibold text-gray-900 dark:text-white">{{ $task['title'] }}</h3>
                                        <span class="ml-2 px-2 py-1 rounded-full text-xs font-medium
                                            {{ $task['priority'] === 'high' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300' : 
                                               ($task['priority'] === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300' : 
                                                'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300') }}">
                                            {{ ucfirst($task['priority']) }} Priority
                                        </span>
                                    </div>
                                    
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ $task['description'] }}</p>
                                    
                                    <div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                            </svg>
                                            Due: {{ \Carbon\Carbon::parse($task['due_date'])->format('M j, Y') }}
                                        </span>
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{ $task['estimated_time'] }}
                                        </span>
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                            </svg>
                                            Assigned by: {{ $task['assigned_by'] }}
                                        </span>
                                        <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-xs">
                                            {{ $task['category'] }}
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="flex space-x-2 ml-4">
                                    <button wire:click="markTaskComplete({{ $task['id'] }})" 
                                            class="px-4 py-2 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 transition-colors flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Complete
                                    </button>
                                    <button class="px-4 py-2 bg-gray-600 text-white rounded-lg text-sm hover:bg-gray-700 transition-colors">
                                        View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <div class="text-6xl mb-4">🎉</div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">All caught up!</h3>
                    <p class="text-gray-600 dark:text-gray-400">You have no pending tasks. Great job!</p>
                </div>
            @endif
        </div>

        {{-- Recently Completed Tasks --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <span class="text-2xl mr-2">✅</span>
                Recently Completed
            </h2>
            
            @if(!empty($tasksData['completed_tasks']))
                <div class="space-y-3">
                    @foreach($tasksData['completed_tasks'] as $task)
                        <div class="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                            <div class="flex items-center">
                                <div class="text-2xl mr-3">✅</div>
                                <div>
                                    <div class="font-medium text-gray-900 dark:text-white">{{ $task['title'] }}</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ $task['description'] }}</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-green-600 dark:text-green-400 font-medium">Completed</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ \Carbon\Carbon::parse($task['completed_date'])->format('M j, Y') }}
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-gray-600 dark:text-gray-400 text-center py-4">No recently completed tasks.</p>
            @endif
        </div>

        {{-- Quick Actions --}}
        <div class="bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">🚀 Quick Actions</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="{{ \App\Filament\Pages\AddDataPage::getUrl() }}" 
                   class="flex flex-col items-center p-4 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-700">
                    <div class="text-3xl mb-2">📝</div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white text-center">Add Data</span>
                </a>
                
                <a href="{{ \App\Filament\Pages\MyProgressPage::getUrl() }}" 
                   class="flex flex-col items-center p-4 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-700">
                    <div class="text-3xl mb-2">📊</div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white text-center">View Progress</span>
                </a>
                
                <a href="#" class="flex flex-col items-center p-4 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-700">
                    <div class="text-3xl mb-2">📋</div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white text-center">Generate Report</span>
                </a>
                
                <a href="#" class="flex flex-col items-center p-4 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-700">
                    <div class="text-3xl mb-2">❓</div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white text-center">Get Help</span>
                </a>
            </div>
        </div>
    </div>
</x-filament-panels::page>
