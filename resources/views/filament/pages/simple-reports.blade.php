@php
    $reportsData = $this->getReportsData();
@endphp

<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Page Header --}}
        <div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-700">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="text-3xl mr-3">📊</span>
                My Reports
            </h1>
            <p class="text-gray-600 dark:text-gray-300 mt-2">
                Generate and download your carbon management reports with just one click.
            </p>
        </div>

        {{-- Quick Report Generation --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <span class="text-2xl mr-2">⚡</span>
                Quick Reports
            </h2>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                Generate commonly used reports instantly. No configuration needed!
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                @foreach($reportsData['quick_reports'] as $report)
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start">
                                <div class="text-3xl mr-4">{{ $report['icon'] }}</div>
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900 dark:text-white">{{ $report['title'] }}</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $report['description'] }}</p>
                                    
                                    <div class="flex items-center mt-3 space-x-4 text-xs text-gray-500 dark:text-gray-400">
                                        <span class="flex items-center">
                                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{ $report['estimated_time'] }}
                                        </span>
                                        @if($report['last_generated'])
                                            <span class="flex items-center">
                                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                                                </svg>
                                                Last: {{ \Carbon\Carbon::parse($report['last_generated'])->format('M j') }}
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            
                            <button wire:click="generateReport('{{ $report['id'] }}')" 
                                    class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                                Generate
                            </button>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        {{-- Recent Reports --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <span class="text-2xl mr-2">📁</span>
                Recent Reports
            </h2>
            
            @if(!empty($reportsData['recent_reports']))
                <div class="space-y-3">
                    @foreach($reportsData['recent_reports'] as $report)
                        <div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            <div class="flex items-center">
                                <div class="text-2xl mr-4">📄</div>
                                <div>
                                    <h3 class="font-medium text-gray-900 dark:text-white">{{ $report['title'] }}</h3>
                                    <div class="flex items-center mt-1 space-x-4 text-sm text-gray-500 dark:text-gray-400">
                                        <span>{{ $report['type'] }}</span>
                                        <span>{{ $report['file_size'] }}</span>
                                        <span>Generated: {{ \Carbon\Carbon::parse($report['generated_date'])->format('M j, Y') }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700 transition-colors">
                                    View
                                </button>
                                <button wire:click="downloadReport('{{ $report['title'] }}')" 
                                        class="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                    Download
                                </button>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <div class="text-6xl mb-4">📊</div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">No reports yet</h3>
                    <p class="text-gray-600 dark:text-gray-400">Generate your first report using the quick reports above.</p>
                </div>
            @endif
        </div>

        {{-- Report Templates --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <span class="text-2xl mr-2">📋</span>
                Report Templates
            </h2>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                Choose from pre-designed templates for different audiences and purposes.
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                @foreach($reportsData['report_templates'] as $template)
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 text-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <div class="text-4xl mb-3">{{ $template['icon'] }}</div>
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-2">{{ $template['name'] }}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ $template['description'] }}</p>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mb-4">
                            {{ $template['pages'] }} pages
                        </div>
                        <button class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg text-sm hover:bg-purple-700 transition-colors">
                            Use Template
                        </button>
                    </div>
                @endforeach
            </div>
        </div>

        {{-- Help Section --}}
        <div class="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-xl p-6 border border-yellow-200 dark:border-yellow-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                <span class="text-2xl mr-2">💡</span>
                Report Tips
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700 dark:text-gray-300">
                <div>
                    <h4 class="font-medium mb-2">📊 Monthly Summary</h4>
                    <p>Perfect for tracking your regular progress and identifying trends in your carbon footprint.</p>
                </div>
                <div>
                    <h4 class="font-medium mb-2">📈 Progress Reports</h4>
                    <p>Great for sharing with supervisors and demonstrating your commitment to sustainability goals.</p>
                </div>
                <div>
                    <h4 class="font-medium mb-2">🔍 Activity Breakdown</h4>
                    <p>Detailed analysis to help you identify the biggest opportunities for carbon reduction.</p>
                </div>
                <div>
                    <h4 class="font-medium mb-2">⚖️ Peer Comparison</h4>
                    <p>See how you're performing relative to colleagues and learn from best practices.</p>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
