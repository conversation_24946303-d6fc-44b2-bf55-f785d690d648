@php
    $progressData = $this->getProgressData();
@endphp

<div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">🎯 Annual Carbon Reduction Goal</h2>
    
    <div class="mb-6">
        <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Progress: {{ $progressData['current_progress'] }} / {{ $progressData['annual_target'] }} tCO₂e
            </span>
            <span class="text-sm font-medium text-green-600 dark:text-green-400">
                {{ $progressData['progress_percentage'] }}% complete
            </span>
        </div>
        
        {{-- Progress Bar --}}
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4 mb-4">
            <div class="bg-gradient-to-r from-green-500 to-blue-500 h-4 rounded-full transition-all duration-500" 
                 style="width: {{ $progressData['progress_percentage'] }}%"></div>
        </div>
        
        <div class="text-center">
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                🎉 {{ $progressData['progress_percentage'] >= 50 ? 'Great progress!' : 'Keep going!' }}
            </div>
            <p class="text-gray-600 dark:text-gray-300 mt-1">
                {{ $progressData['progress_percentage'] >= 75 ? 'You\'re almost there!' : 
                   ($progressData['progress_percentage'] >= 50 ? 'You\'re ahead of schedule!' : 'You\'re on the right track!') }}
            </p>
        </div>
    </div>

    {{-- Monthly Progress --}}
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">📅 Monthly Progress</h3>
    
    <div class="grid grid-cols-5 gap-4">
        @foreach($progressData['monthly_data'] as $month => $data)
            <div class="text-center">
                <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ $month }}</div>
                
                {{-- Progress Circle --}}
                <div class="relative w-16 h-16 mx-auto mb-2">
                    <svg class="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                        <path class="text-gray-200 dark:text-gray-700" stroke="currentColor" stroke-width="3" fill="none" 
                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                        <path class="{{ $data['percentage'] >= 100 ? 'text-green-500' : ($data['percentage'] >= 80 ? 'text-yellow-500' : 'text-red-500') }}" 
                              stroke="currentColor" stroke-width="3" fill="none" stroke-linecap="round"
                              stroke-dasharray="{{ $data['percentage'] }}, 100"
                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-xs font-semibold">{{ $data['percentage'] }}%</span>
                    </div>
                </div>
                
                <div class="text-xs text-gray-600 dark:text-gray-400">
                    {{ $data['actual'] }} / {{ $data['target'] }} tCO₂e
                </div>
                
                @if($data['percentage'] >= 100)
                    <div class="text-xs text-green-600 dark:text-green-400 mt-1">✅ Goal exceeded!</div>
                @elseif($data['percentage'] >= 80)
                    <div class="text-xs text-yellow-600 dark:text-yellow-400 mt-1">⚠️ Close to goal</div>
                @elseif($data['actual'] > 0)
                    <div class="text-xs text-red-600 dark:text-red-400 mt-1">📈 Needs improvement</div>
                @else
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">⏳ In progress</div>
                @endif
            </div>
        @endforeach
    </div>

    <div class="mt-6 text-center">
        <a href="{{ \App\Filament\Pages\MyProgressPage::getUrl() }}" 
           class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 transition-colors">
            View Detailed Progress →
        </a>
    </div>
</div>
