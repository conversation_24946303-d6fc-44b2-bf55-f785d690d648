@php
    $tasksData = $this->getTasksData();
@endphp

<div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
        <span class="text-2xl mr-2">✅</span>
        My Tasks
    </h2>
    
    {{-- Task Summary --}}
    <div class="grid grid-cols-3 gap-4 mb-6">
        <div class="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {{ $tasksData['task_summary']['total_pending'] }}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Pending</div>
        </div>
        
        <div class="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                {{ $tasksData['task_summary']['total_overdue'] }}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Overdue</div>
        </div>
        
        <div class="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                {{ $tasksData['task_summary']['completion_rate'] }}%
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Completion Rate</div>
        </div>
    </div>
    
    {{-- Pending Tasks --}}
    @if(!empty($tasksData['pending_tasks']))
        <div class="space-y-3 mb-4">
            @foreach(array_slice($tasksData['pending_tasks'], 0, 3) as $task)
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center mb-1">
                                <h3 class="font-medium text-gray-900 dark:text-white text-sm">{{ $task['title'] }}</h3>
                                <span class="ml-2 px-2 py-1 rounded-full text-xs font-medium
                                    {{ $task['priority'] === 'high' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300' : 
                                       ($task['priority'] === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300' : 
                                        'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300') }}">
                                    {{ ucfirst($task['priority']) }}
                                </span>
                            </div>
                            
                            <p class="text-xs text-gray-600 dark:text-gray-400 mb-2">{{ $task['description'] }}</p>
                            
                            <div class="flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400">
                                <span>Due: {{ \Carbon\Carbon::parse($task['due_date'])->format('M j') }}</span>
                                <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full">
                                    {{ $task['category'] }}
                                </span>
                            </div>
                        </div>
                        
                        <button wire:click="markTaskComplete({{ $task['id'] }})" 
                                class="ml-3 px-3 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700 transition-colors">
                            Complete
                        </button>
                    </div>
                </div>
            @endforeach
        </div>
    @endif
    
    <div class="text-center">
        <a href="{{ \App\Filament\Pages\MyTasksPage::getUrl() }}" 
           class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors">
            View All Tasks →
        </a>
    </div>
</div>
