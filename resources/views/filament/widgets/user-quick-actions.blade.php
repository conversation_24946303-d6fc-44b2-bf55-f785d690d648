@php
    $quickActions = $this->getQuickActions();
    $recentActivity = $this->getRecentActivity();
@endphp

<div class="space-y-6">
    {{-- Quick Actions Section --}}
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">🚀 Quick Actions</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            @foreach($quickActions as $action)
                @php
                    $colorClasses = match($action['color']) {
                        'blue' => 'bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 border-blue-200 dark:border-blue-700',
                        'green' => 'bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30 border-green-200 dark:border-green-700',
                        'purple' => 'bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 border-purple-200 dark:border-purple-700',
                        'yellow' => 'bg-yellow-50 dark:bg-yellow-900/20 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 border-yellow-200 dark:border-yellow-700',
                        default => 'bg-gray-50 dark:bg-gray-900/20 hover:bg-gray-100 dark:hover:bg-gray-900/30 border-gray-200 dark:border-gray-700',
                    };
                @endphp
                <a href="{{ $action['url'] }}"
                   class="flex flex-col items-center p-4 rounded-lg transition-colors group border {{ $colorClasses }}">
                    <div class="text-3xl mb-2">{{ $action['icon'] }}</div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white text-center">{{ $action['title'] }}</span>
                    <span class="text-xs text-gray-600 dark:text-gray-400 text-center mt-1">{{ $action['description'] }}</span>
                </a>
            @endforeach
        </div>
    </div>

    {{-- Recent Activity --}}
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">📈 Recent Activity</h3>
        <div class="space-y-3">
            @foreach($recentActivity as $activity)
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center">
                        <div class="text-2xl mr-3">{{ $activity['icon'] }}</div>
                        <div>
                            <div class="font-medium text-gray-900 dark:text-white">{{ $activity['title'] }}</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">{{ $activity['description'] }}</div>
                        </div>
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ $activity['date'] }}</div>
                </div>
            @endforeach
        </div>

        <div class="mt-4 text-center">
            <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                View all activity →
            </a>
        </div>
    </div>

    {{-- Tips & Insights --}}
    <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">💡 Today's Tip</h3>
        <div class="flex items-start">
            <div class="text-2xl mr-3">🌱</div>
            <div>
                <p class="text-gray-700 dark:text-gray-300">
                    <strong>Switch to video calls:</strong> Replacing one business trip with a video conference can save up to 2.3 tCO₂e.
                    You've already saved <strong>4.6 tCO₂e</strong> this month by choosing virtual meetings!
                </p>
                <a href="#" class="text-green-600 dark:text-green-400 hover:underline text-sm mt-2 inline-block">
                    Learn more sustainability tips →
                </a>
            </div>
        </div>
    </div>
</div>
