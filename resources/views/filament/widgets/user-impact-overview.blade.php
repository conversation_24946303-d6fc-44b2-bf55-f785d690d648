@php
    $impactData = $this->getImpactData();
@endphp

<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    {{-- My Impact Card --}}
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">📊 My Impact</h3>
                <div class="mt-2">
                    <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">
                        {{ $impactData['monthly_impact'] }} tCO₂e
                    </div>
                    <div class="text-sm text-green-600 dark:text-green-400 flex items-center mt-1">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ $impactData['improvement_percentage'] }}% improvement
                    </div>
                </div>
            </div>
            <div class="text-4xl">📈</div>
        </div>
    </div>

    {{-- My Goals Card --}}
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <div class="flex-1">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">🎯 My Goals</h3>
                <div class="mt-2">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        {{ $impactData['goal_progress'] }}% to goal
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        On track ✅
                    </div>
                </div>
                {{-- Progress Bar --}}
                <div class="mt-3">
                    <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full" style="width: {{ $impactData['goal_progress'] }}%"></div>
                    </div>
                </div>
            </div>
            <div class="text-4xl ml-4">🎯</div>
        </div>
    </div>

    {{-- My Tasks Card --}}
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">✅ My Tasks</h3>
                <div class="mt-2">
                    <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">
                        {{ $impactData['pending_tasks'] }} pending
                    </div>
                    <div class="text-sm text-red-600 dark:text-red-400 mt-1">
                        1 due this week
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ \App\Filament\Pages\MyTasksPage::getUrl() }}" class="text-sm text-blue-600 dark:text-blue-400 hover:underline">
                        View all tasks →
                    </a>
                </div>
            </div>
            <div class="text-4xl">📋</div>
        </div>
    </div>
</div>
