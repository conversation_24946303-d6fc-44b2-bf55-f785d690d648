/* User-Focused Dashboard Styles */

/* Custom color scheme for sustainability theme */
:root {
    --green-50: #f0fdf4;
    --green-100: #dcfce7;
    --green-500: #22c55e;
    --green-600: #16a34a;
    --blue-50: #eff6ff;
    --blue-500: #3b82f6;
    --blue-600: #2563eb;
}

/* Simplified navigation styles */
.fi-sidebar-nav {
    @apply bg-gradient-to-b from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20;
}

.fi-sidebar-nav-item {
    @apply rounded-lg mx-2 mb-1;
}

.fi-sidebar-nav-item-active {
    @apply bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300;
}

/* Large, touch-friendly buttons */
.user-action-card {
    @apply flex flex-col items-center p-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm border-2 border-gray-200 dark:border-gray-700 hover:border-green-500 dark:hover:border-green-400 transition-all duration-200 cursor-pointer transform hover:scale-105;
}

.user-action-card:hover {
    @apply shadow-lg;
}

/* Progress indicators */
.progress-ring {
    @apply relative inline-flex items-center justify-center;
}

.progress-ring-circle {
    @apply transform -rotate-90;
}

/* Achievement badges */
.achievement-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300;
}

/* Simplified form styles */
.user-form-section {
    @apply bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700;
}

.user-form-step {
    @apply space-y-6;
}

/* Activity type cards */
.activity-type-card {
    @apply flex items-center p-4 border-2 border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors cursor-pointer bg-white dark:bg-gray-800;
}

.activity-type-card:hover {
    @apply shadow-md transform translate-y-[-2px];
}

.activity-type-card.selected {
    @apply border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20;
}

/* Metric cards */
.metric-card {
    @apply bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700;
}

.metric-card-icon {
    @apply text-4xl mb-2;
}

.metric-card-value {
    @apply text-3xl font-bold;
}

.metric-card-label {
    @apply text-sm text-gray-600 dark:text-gray-400;
}

.metric-card-trend {
    @apply text-sm font-medium flex items-center mt-1;
}

.metric-card-trend.positive {
    @apply text-green-600 dark:text-green-400;
}

.metric-card-trend.negative {
    @apply text-red-600 dark:text-red-400;
}

/* Quick action buttons */
.quick-action-btn {
    @apply inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors;
}

.quick-action-btn-secondary {
    @apply inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-lg shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors;
}

/* Welcome section */
.welcome-section {
    @apply bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-xl p-6 border border-green-200 dark:border-green-700;
}

/* Stats grid */
.stats-grid {
    @apply grid grid-cols-1 md:grid-cols-3 gap-6;
}

/* Progress bars */
.progress-bar {
    @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3;
}

.progress-bar-fill {
    @apply h-3 rounded-full transition-all duration-500;
}

.progress-bar-fill.green {
    @apply bg-gradient-to-r from-green-500 to-emerald-500;
}

.progress-bar-fill.blue {
    @apply bg-gradient-to-r from-blue-500 to-cyan-500;
}

.progress-bar-fill.yellow {
    @apply bg-gradient-to-r from-yellow-500 to-orange-500;
}

/* Notification styles */
.success-notification {
    @apply bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 text-green-800 dark:text-green-300 rounded-lg p-4;
}

.info-notification {
    @apply bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-300 rounded-lg p-4;
}

.warning-notification {
    @apply bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 text-yellow-800 dark:text-yellow-300 rounded-lg p-4;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .stats-grid {
        @apply grid-cols-1 gap-4;
    }
    
    .user-action-card {
        @apply p-4;
    }
    
    .metric-card {
        @apply p-4;
    }
    
    .welcome-section {
        @apply p-4;
    }
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover effects */
.hover-lift {
    @apply transition-transform duration-200;
}

.hover-lift:hover {
    @apply transform translate-y-[-2px];
}

/* Focus styles for accessibility */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
}
