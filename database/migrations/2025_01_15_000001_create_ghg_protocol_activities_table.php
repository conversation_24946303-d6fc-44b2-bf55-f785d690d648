<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ghg_protocol_activities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sector_id')->constrained('ghg_protocol_sectors')->onDelete('cascade');
            $table->string('code', 50)->unique(); // e.g., 'STAT_COMB_NG', 'MOB_COMB_GASOLINE'
            $table->string('name'); // e.g., 'Natural Gas Combustion', 'Gasoline Combustion'
            $table->string('display_name')->nullable();
            $table->text('description')->nullable();
            $table->string('activity_type', 50)->nullable(); // 'combustion', 'electricity', 'transport', etc.
            $table->string('fuel_type', 100)->nullable(); // For combustion activities
            $table->string('vehicle_type', 100)->nullable(); // For mobile combustion
            $table->string('equipment_type', 100)->nullable(); // For stationary combustion
            $table->json('applicable_scopes')->nullable(); // [1, 2, 3] - which scopes this applies to
            $table->json('calculation_methods')->nullable(); // Available calculation approaches
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['sector_id', 'activity_type']);
            $table->index(['fuel_type']);
            $table->index(['vehicle_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ghg_protocol_activities');
    }
};
