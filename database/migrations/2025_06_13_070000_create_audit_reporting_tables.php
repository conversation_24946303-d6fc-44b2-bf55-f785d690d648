<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Enhanced Audit Logs Table (skip if exists)
        if (!Schema::hasTable('audit_logs')) {
            Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('user_name');
            $table->string('action', 100);
            $table->string('table_name', 100);
            $table->unsignedBigInteger('record_id');
            $table->json('old_values')->nullable();
            $table->json('new_values')->nullable();
            $table->timestamp('action_timestamp');
            $table->string('ip_address', 45)->nullable();
            $table->string('user_agent')->nullable();
            $table->string('session_id')->nullable();
            $table->string('request_id')->nullable();
            $table->json('context_data')->nullable();
            $table->string('integrity_hash', 64)->nullable();
            $table->timestamps();

            $table->index(['organization_id', 'action_timestamp']);
            $table->index(['table_name', 'record_id']);
            $table->index(['user_id', 'action_timestamp']);
            $table->index('action_timestamp');
        });

        // Data Lineage Table
        Schema::create('data_lineage', function (Blueprint $table) {
            $table->id();
            $table->foreignId('activity_data_id')->constrained('activity_data')->onDelete('cascade');
            $table->string('source_system_type', 50);
            $table->string('source_system_name');
            $table->string('source_record_id')->nullable();
            $table->timestamp('ingestion_timestamp');
            $table->json('transformation_steps')->nullable();
            $table->json('validation_results')->nullable();
            $table->string('data_quality_score', 10)->nullable();
            $table->json('metadata')->nullable();
            $table->string('lineage_hash', 64)->nullable();
            $table->timestamps();

            $table->index(['source_system_type', 'ingestion_timestamp']);
            $table->index('activity_data_id');
        });
        }

        // Calculation Audit Trail Table
        if (!Schema::hasTable('calculation_audit_trails')) {
            Schema::create('calculation_audit_trails', function (Blueprint $table) {
            $table->id();
            $table->foreignId('emission_calculation_id')->constrained('calculated_emissions')->onDelete('cascade');
            $table->string('calculation_method');
            $table->string('methodology_framework')->default('GHG Protocol');
            $table->json('input_parameters');
            $table->json('calculation_steps');
            $table->json('intermediate_results')->nullable();
            $table->json('emission_factor_details');
            $table->json('unit_conversions')->nullable();
            $table->decimal('uncertainty_assessment', 8, 4)->nullable();
            $table->string('calculation_engine_version', 20)->nullable();
            $table->timestamp('calculation_timestamp');
            $table->string('calculated_by')->nullable();
            $table->string('calculation_hash', 64);
            $table->timestamps();

            $table->index(['calculation_method', 'calculation_timestamp']);
            $table->index('emission_calculation_id');
        });
        }

        // Compliance Reports Table
        Schema::create('compliance_reports', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->string('report_name');
            $table->text('description')->nullable();
            $table->enum('framework_type', ['sec_climate_rule', 'csrd', 'tcfd', 'cdp', 'ghg_protocol', 'sbti', 'issb']);
            $table->integer('reporting_year');
            $table->json('report_data');
            $table->json('audit_trail_summary')->nullable();
            $table->string('file_path')->nullable();
            $table->string('file_format', 10)->default('json');
            $table->bigInteger('file_size')->nullable();
            $table->enum('status', ['draft', 'in_review', 'approved', 'submitted', 'published'])->default('draft');
            $table->timestamp('generated_at');
            $table->string('generated_by');
            $table->timestamp('approved_at')->nullable();
            $table->string('approved_by')->nullable();
            $table->timestamp('submitted_at')->nullable();
            $table->string('submission_reference')->nullable();
            $table->json('validation_results')->nullable();
            $table->string('report_hash', 64)->nullable();
            $table->timestamps();

            $table->index(['organization_id', 'framework_type', 'reporting_year']);
            $table->index(['status', 'generated_at']);
            $table->index('framework_type');
        });

        // Report Sections Table
        Schema::create('report_sections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('compliance_report_id')->constrained()->onDelete('cascade');
            $table->string('section_name');
            $table->string('section_type', 50);
            $table->integer('section_order');
            $table->json('section_data');
            $table->json('data_sources')->nullable();
            $table->json('calculation_references')->nullable();
            $table->string('section_hash', 64)->nullable();
            $table->timestamps();

            $table->index(['compliance_report_id', 'section_order']);
            $table->index('section_type');
        });

        // Drill Down Analysis Table
        Schema::create('drill_down_analyses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->string('analysis_name');
            $table->text('description')->nullable();
            $table->integer('reporting_year');
            $table->json('corporate_total');
            $table->json('scope_breakdown');
            $table->json('facility_breakdown');
            $table->json('source_breakdown');
            $table->json('activity_detail');
            $table->json('supplier_breakdown')->nullable();
            $table->json('traceability_matrix');
            $table->json('drill_down_paths');
            $table->json('summary_statistics');
            $table->timestamp('analysis_date');
            $table->string('analyzed_by');
            $table->enum('status', ['draft', 'completed', 'archived'])->default('draft');
            $table->timestamps();

            $table->index(['organization_id', 'reporting_year']);
            $table->index(['analysis_date', 'status']);
        });

        // Framework Templates Table
        Schema::create('framework_templates', function (Blueprint $table) {
            $table->id();
            $table->string('framework_code', 50)->unique();
            $table->string('framework_name');
            $table->text('description');
            $table->string('version', 20);
            $table->json('template_structure');
            $table->json('required_sections');
            $table->json('validation_rules');
            $table->json('mapping_rules')->nullable();
            $table->boolean('is_active')->default(true);
            $table->date('effective_date');
            $table->date('expiry_date')->nullable();
            $table->timestamps();

            $table->index(['framework_code', 'is_active']);
            $table->index('effective_date');
        });

        // Report Validations Table
        Schema::create('report_validations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('compliance_report_id')->constrained()->onDelete('cascade');
            $table->string('validation_type', 50);
            $table->string('validation_rule');
            $table->enum('validation_status', ['passed', 'failed', 'warning', 'skipped']);
            $table->text('validation_message')->nullable();
            $table->json('validation_details')->nullable();
            $table->timestamp('validated_at');
            $table->string('validated_by')->nullable();
            $table->timestamps();

            $table->index(['compliance_report_id', 'validation_status']);
            $table->index('validation_type');
        });

        // Immutable Audit Entries Table (for blockchain-like audit trail)
        Schema::create('immutable_audit_entries', function (Blueprint $table) {
            $table->id();
            $table->string('entry_hash', 64)->unique();
            $table->string('previous_hash', 64)->nullable();
            $table->timestamp('entry_timestamp');
            $table->json('entry_data');
            $table->string('entry_type', 50);
            $table->foreignId('organization_id')->nullable()->constrained()->onDelete('set null');
            $table->string('merkle_root', 64)->nullable();
            $table->integer('block_number')->nullable();
            $table->string('signature', 512)->nullable();
            $table->timestamps();

            $table->index(['entry_timestamp', 'organization_id']);
            $table->index('entry_type');
            $table->index('block_number');
        });

        // Data Quality Assessments Table
        Schema::create('data_quality_assessments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('activity_data_id')->constrained('activity_data')->onDelete('cascade');
            $table->string('assessment_type', 50);
            $table->json('quality_dimensions'); // completeness, accuracy, consistency, timeliness, validity
            $table->decimal('overall_score', 5, 2);
            $table->string('quality_rating', 10); // A, B, C, D
            $table->json('quality_issues')->nullable();
            $table->json('improvement_recommendations')->nullable();
            $table->timestamp('assessed_at');
            $table->string('assessed_by')->nullable();
            $table->string('assessment_method', 50)->nullable();
            $table->timestamps();

            $table->index(['activity_data_id', 'assessed_at']);
            $table->index(['quality_rating', 'assessed_at']);
        });

        // Report Approvals Table
        Schema::create('report_approvals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('compliance_report_id')->constrained()->onDelete('cascade');
            $table->string('approval_stage', 50);
            $table->integer('approval_order');
            $table->string('approver_role', 100);
            $table->foreignId('approver_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('approver_name');
            $table->enum('approval_status', ['pending', 'approved', 'rejected', 'delegated']);
            $table->text('approval_comments')->nullable();
            $table->timestamp('approval_deadline')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->json('approval_conditions')->nullable();
            $table->string('digital_signature', 512)->nullable();
            $table->timestamps();

            $table->index(['compliance_report_id', 'approval_order']);
            $table->index(['approval_status', 'approval_deadline']);
        });

        // External Assurance Table
        Schema::create('external_assurances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('compliance_report_id')->constrained()->onDelete('cascade');
            $table->string('assurance_provider');
            $table->string('assurance_type', 50); // limited, reasonable
            $table->string('assurance_standard', 100); // ISAE 3000, ISAE 3410, AA1000AS
            $table->json('scope_of_assurance');
            $table->enum('assurance_level', ['limited', 'reasonable']);
            $table->text('assurance_opinion')->nullable();
            $table->json('material_misstatements')->nullable();
            $table->json('recommendations')->nullable();
            $table->date('assurance_date');
            $table->string('assurance_report_reference')->nullable();
            $table->string('assurance_certificate_path')->nullable();
            $table->timestamps();

            $table->index(['compliance_report_id', 'assurance_date']);
            $table->index('assurance_provider');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('external_assurances');
        Schema::dropIfExists('report_approvals');
        Schema::dropIfExists('data_quality_assessments');
        Schema::dropIfExists('immutable_audit_entries');
        Schema::dropIfExists('report_validations');
        Schema::dropIfExists('framework_templates');
        Schema::dropIfExists('drill_down_analyses');
        Schema::dropIfExists('report_sections');
        Schema::dropIfExists('compliance_reports');
        Schema::dropIfExists('calculation_audit_trails');
        Schema::dropIfExists('data_lineage');
        Schema::dropIfExists('audit_logs');
    }
};
