<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\MeasurementUnit;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Find the base tCO2e unit
        $tCO2e = MeasurementUnit::where('symbol', 'tCO2e')->first();
        
        if (!$tCO2e) {
            throw new Exception('Base unit tCO2e not found. Please run MeasurementUnitSeeder first.');
        }

        // Create lb CO₂e unit
        MeasurementUnit::updateOrCreate(
            ['symbol' => 'lb CO₂e'],
            [
                'name' => 'Pound CO2 Equivalent',
                'unit_type' => 'Emissions',
                'is_base_unit' => false,
                'si_unit_id' => $tCO2e->id,
                'conversion_factor' => 0.000453592, // 1 lb = 0.453592 kg = 0.000453592 metric tons
                'description' => 'US unit for GHG emissions (pounds)',
                'is_active' => true,
            ]
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        MeasurementUnit::where('symbol', 'lb CO₂e')->delete();
    }
};
