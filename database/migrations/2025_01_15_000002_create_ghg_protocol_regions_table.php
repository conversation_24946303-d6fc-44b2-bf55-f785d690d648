<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ghg_protocol_regions', function (Blueprint $table) {
            $table->id();
            $table->string('code', 20)->unique(); // e.g., 'US', 'US-CA', 'WECC', 'EU'
            $table->string('name'); // e.g., 'United States', 'California', 'Western Electricity Coordinating Council'
            $table->string('region_type', 50); // 'country', 'state', 'province', 'grid_region', 'economic_region'
            $table->string('parent_code', 20)->nullable(); // For hierarchical regions (e.g., 'US-CA' parent is 'US')
            $table->string('iso_country_code', 3)->nullable(); // ISO 3166-1 alpha-3
            $table->string('iso_subdivision_code', 10)->nullable(); // ISO 3166-2
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->text('description')->nullable();
            $table->json('applicable_sectors')->nullable(); // Which sectors this region applies to
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['region_type']);
            $table->index(['parent_code']);
            $table->index(['iso_country_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ghg_protocol_regions');
    }
};
