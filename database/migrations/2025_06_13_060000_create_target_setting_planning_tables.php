<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Science-Based Targets Table
        Schema::create('science_based_targets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->string('target_name');
            $table->text('description')->nullable();
            $table->enum('temperature_scenario', ['1.5C', '2C', 'well_below_2C'])->default('1.5C');
            $table->integer('base_year');
            $table->integer('target_year');
            $table->decimal('baseline_emissions', 15, 2);
            $table->decimal('target_emissions', 15, 2);
            $table->decimal('reduction_percentage', 5, 2);
            $table->json('scope_coverage'); // ['1', '2', '3']
            $table->json('yearly_milestones')->nullable();
            $table->string('sector_pathway')->nullable();
            $table->boolean('is_sbti_validated')->default(false);
            $table->date('sbti_validation_date')->nullable();
            $table->string('validation_body')->nullable();
            $table->enum('status', ['draft', 'submitted', 'validated', 'active', 'achieved'])->default('draft');
            $table->json('methodology_details')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['organization_id', 'status']);
            $table->index(['temperature_scenario', 'target_year']);
        });

        // Net-Zero Roadmaps Table
        Schema::create('net_zero_roadmaps', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->foreignId('science_based_target_id')->nullable()->constrained()->onDelete('set null');
            $table->string('roadmap_name');
            $table->text('description')->nullable();
            $table->integer('net_zero_year')->default(2050);
            $table->decimal('baseline_emissions', 15, 2);
            $table->json('phase_1_details'); // Rapid reduction phase
            $table->json('phase_2_details'); // Deep decarbonization phase
            $table->json('phase_3_details'); // Net-zero achievement phase
            $table->json('investment_requirements')->nullable();
            $table->json('key_technologies')->nullable();
            $table->json('carbon_removal_strategy')->nullable();
            $table->enum('status', ['draft', 'approved', 'active', 'achieved'])->default('draft');
            $table->date('last_updated_date');
            $table->timestamps();

            $table->index(['organization_id', 'net_zero_year']);
            $table->index('status');
        });

        // Abatement Options Table
        Schema::create('abatement_options', function (Blueprint $table) {
            $table->id();
            $table->string('option_code')->unique();
            $table->string('name');
            $table->text('description');
            $table->string('category'); // Energy Efficiency, Renewable Energy, etc.
            $table->json('applicable_sectors');
            $table->json('applicable_scopes');
            $table->decimal('capital_cost_per_tonne', 10, 2);
            $table->decimal('operational_cost_per_tonne', 10, 2);
            $table->decimal('energy_savings_per_tonne', 8, 2)->nullable();
            $table->decimal('other_savings_per_tonne', 8, 2)->nullable();
            $table->integer('implementation_complexity'); // 1-10 scale
            $table->integer('implementation_time'); // months
            $table->decimal('max_reduction_percentage', 5, 4); // 0.0000-1.0000
            $table->decimal('max_absolute_reduction', 15, 2)->nullable();
            $table->json('co_benefits')->nullable();
            $table->json('risks')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['category', 'is_active']);
            $table->index('option_code');
        });

        // Marginal Abatement Cost Curves Table
        Schema::create('macc_analyses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->string('analysis_name');
            $table->text('description')->nullable();
            $table->decimal('target_reduction_percentage', 5, 2);
            $table->integer('timeframe_years');
            $table->string('currency', 3)->default('USD');
            $table->decimal('baseline_emissions', 15, 2);
            $table->json('curve_data'); // Sorted abatement options with costs
            $table->json('optimal_portfolio'); // Selected options to meet target
            $table->decimal('total_investment_required', 15, 2);
            $table->decimal('average_cost_per_tonne', 10, 2);
            $table->date('analysis_date');
            $table->enum('status', ['draft', 'approved', 'active'])->default('draft');
            $table->timestamps();

            $table->index(['organization_id', 'analysis_date']);
            $table->index('status');
        });

        // MACC Option Selections Table
        Schema::create('macc_option_selections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('macc_analysis_id')->constrained()->onDelete('cascade');
            $table->foreignId('abatement_option_id')->constrained()->onDelete('cascade');
            $table->decimal('reduction_used', 15, 2);
            $table->decimal('cost_allocated', 15, 2);
            $table->decimal('cost_per_tonne', 10, 2);
            $table->integer('implementation_priority');
            $table->enum('implementation_status', ['planned', 'in_progress', 'completed', 'cancelled'])->default('planned');
            $table->date('planned_start_date')->nullable();
            $table->date('planned_completion_date')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->unique(['macc_analysis_id', 'abatement_option_id'], 'macc_option_unique');
            $table->index('implementation_priority');
        });

        // Scenario Analyses Table
        Schema::create('scenario_analyses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->string('analysis_name');
            $table->text('description')->nullable();
            $table->enum('scenario_type', ['business_growth', 'merger_acquisition', 'facility_closure', 'technology_adoption', 'regulatory_change', 'market_expansion']);
            $table->json('scenario_parameters');
            $table->decimal('baseline_emissions', 15, 2);
            $table->json('scenario_results');
            $table->json('comparison_analysis')->nullable();
            $table->json('recommendations')->nullable();
            $table->date('analysis_date');
            $table->enum('status', ['draft', 'approved', 'active'])->default('draft');
            $table->timestamps();

            $table->index(['organization_id', 'scenario_type']);
            $table->index(['analysis_date', 'status']);
        });

        // Scenario Projections Table
        Schema::create('scenario_projections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('scenario_analysis_id')->constrained()->onDelete('cascade');
            $table->string('scenario_name');
            $table->integer('projection_year');
            $table->decimal('projected_emissions', 15, 2);
            $table->decimal('scope_1_emissions', 15, 2)->nullable();
            $table->decimal('scope_2_emissions', 15, 2)->nullable();
            $table->decimal('scope_3_emissions', 15, 2)->nullable();
            $table->decimal('growth_factor', 8, 4)->nullable();
            $table->decimal('efficiency_factor', 8, 4)->nullable();
            $table->json('key_assumptions')->nullable();
            $table->timestamps();

            $table->unique(['scenario_analysis_id', 'scenario_name', 'projection_year'], 'scenario_projection_unique');
            $table->index('projection_year');
        });

        // Decarbonization Initiatives Table
        Schema::create('decarbonization_initiatives', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->foreignId('abatement_option_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('macc_analysis_id')->nullable()->constrained()->onDelete('set null');
            $table->string('initiative_name');
            $table->text('description');
            $table->string('category');
            $table->json('scope_coverage');
            $table->decimal('estimated_reduction', 15, 2);
            $table->decimal('estimated_cost', 15, 2);
            $table->decimal('cost_per_tonne', 10, 2);
            $table->date('planned_start_date');
            $table->date('planned_completion_date');
            $table->integer('implementation_complexity'); // 1-10 scale
            $table->enum('status', ['planned', 'approved', 'in_progress', 'completed', 'cancelled', 'on_hold'])->default('planned');
            $table->decimal('actual_reduction', 15, 2)->nullable();
            $table->decimal('actual_cost', 15, 2)->nullable();
            $table->integer('progress_percentage')->default(0);
            $table->json('milestones')->nullable();
            $table->json('risks')->nullable();
            $table->json('co_benefits')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['organization_id', 'status']);
            $table->index(['category', 'status']);
            $table->index(['planned_start_date', 'planned_completion_date'], 'decarb_initiatives_dates_index');
        });

        // Target Progress Tracking Table
        Schema::create('target_progress_tracking', function (Blueprint $table) {
            $table->id();
            $table->foreignId('science_based_target_id')->constrained()->onDelete('cascade');
            $table->integer('tracking_year');
            $table->decimal('actual_emissions', 15, 2);
            $table->decimal('target_emissions', 15, 2);
            $table->decimal('progress_percentage', 5, 2);
            $table->boolean('on_track')->default(true);
            $table->decimal('variance_from_target', 15, 2);
            $table->json('scope_breakdown')->nullable();
            $table->text('progress_notes')->nullable();
            $table->json('contributing_initiatives')->nullable();
            $table->date('reporting_date');
            $table->timestamps();

            $table->unique(['science_based_target_id', 'tracking_year'], 'target_progress_unique');
            $table->index(['tracking_year', 'on_track']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('target_progress_tracking');
        Schema::dropIfExists('decarbonization_initiatives');
        Schema::dropIfExists('scenario_projections');
        Schema::dropIfExists('scenario_analyses');
        Schema::dropIfExists('macc_option_selections');
        Schema::dropIfExists('macc_analyses');
        Schema::dropIfExists('abatement_options');
        Schema::dropIfExists('net_zero_roadmaps');
        Schema::dropIfExists('science_based_targets');
    }
};
