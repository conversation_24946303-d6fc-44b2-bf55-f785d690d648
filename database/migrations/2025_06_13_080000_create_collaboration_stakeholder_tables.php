<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Supplier Portal Access Table
        Schema::create('supplier_portal_accesses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('access_token', 64)->unique();
            $table->string('portal_url');
            $table->enum('access_level', ['basic', 'standard', 'premium'])->default('standard');
            $table->boolean('data_submission_enabled')->default(true);
            $table->boolean('dashboard_access_enabled')->default(true);
            $table->boolean('reporting_access_enabled')->default(false);
            $table->timestamp('last_login_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->json('access_permissions')->nullable();
            $table->json('dashboard_config')->nullable();
            $table->timestamps();
            
            $table->index(['supplier_id', 'is_active']);
            $table->index(['access_token', 'is_active']);
            $table->index('expires_at');
        });

        // Stakeholder Actions Table
        Schema::create('stakeholder_actions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('description');
            $table->enum('action_type', ['decarbonization', 'data_collection', 'reporting', 'compliance', 'engagement', 'training']);
            $table->string('category', 100);
            $table->foreignId('assigned_to_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('assigned_to_supplier_id')->nullable()->constrained('suppliers')->onDelete('set null');
            $table->foreignId('assigned_to_facility_id')->nullable()->constrained('facilities')->onDelete('set null');
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->date('due_date');
            $table->decimal('estimated_emissions_reduction', 12, 2)->nullable();
            $table->decimal('estimated_cost', 12, 2)->nullable();
            $table->decimal('estimated_savings', 12, 2)->nullable();
            $table->json('success_criteria')->nullable();
            $table->boolean('approval_required')->default(false);
            $table->foreignId('approver_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->enum('status', ['assigned', 'in_progress', 'pending_approval', 'approved', 'completed', 'cancelled', 'overdue'])->default('assigned');
            $table->decimal('progress_percentage', 5, 2)->default(0);
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->foreignId('created_by_user_id')->constrained('users')->onDelete('cascade');
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->index(['organization_id', 'status']);
            $table->index(['assigned_to_user_id', 'status']);
            $table->index(['due_date', 'status']);
            $table->index(['priority', 'status']);
        });

        // Action Progress Updates Table
        Schema::create('action_progress_updates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('stakeholder_action_id')->constrained()->onDelete('cascade');
            $table->date('update_date');
            $table->decimal('progress_percentage', 5, 2);
            $table->enum('status', ['assigned', 'in_progress', 'pending_approval', 'approved', 'completed', 'cancelled', 'overdue']);
            $table->text('notes')->nullable();
            $table->json('attachments')->nullable();
            $table->decimal('actual_emissions_reduction', 12, 2)->nullable();
            $table->decimal('actual_cost', 12, 2)->nullable();
            $table->decimal('actual_savings', 12, 2)->nullable();
            $table->json('metrics')->nullable();
            $table->foreignId('updated_by_user_id')->constrained('users')->onDelete('cascade');
            $table->timestamps();
            
            $table->index(['stakeholder_action_id', 'update_date']);
            $table->index('update_date');
        });

        // Stakeholder Dashboards Table
        Schema::create('stakeholder_dashboards', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('supplier_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('role_name', 50);
            $table->string('dashboard_name');
            $table->text('description')->nullable();
            $table->json('dashboard_config');
            $table->json('widget_preferences')->nullable();
            $table->json('filter_preferences')->nullable();
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_accessed_at')->nullable();
            $table->integer('access_count')->default(0);
            $table->timestamps();
            
            $table->index(['user_id', 'role_name']);
            $table->index(['supplier_id', 'is_active']);
            $table->index('role_name');
        });

        // Stakeholder Notifications Table
        Schema::create('stakeholder_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('organization_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('notification_type', 50);
            $table->string('title');
            $table->text('message');
            $table->json('data')->nullable();
            $table->string('action_url')->nullable();
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->boolean('email_sent')->default(false);
            $table->timestamp('email_sent_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'is_read']);
            $table->index(['notification_type', 'created_at']);
            $table->index('expires_at');
        });

        // Supplier Data Submissions Table
        Schema::create('supplier_data_submissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');
            $table->foreignId('submitted_by_user_id')->constrained('users')->onDelete('cascade');
            $table->string('submission_type', 50);
            $table->integer('reporting_year');
            $table->string('reporting_period', 20);
            $table->json('submission_data');
            $table->enum('data_quality_rating', ['A', 'B', 'C', 'D'])->nullable();
            $table->json('validation_results')->nullable();
            $table->enum('status', ['draft', 'submitted', 'under_review', 'approved', 'rejected'])->default('draft');
            $table->text('review_comments')->nullable();
            $table->foreignId('reviewed_by_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('reviewed_at')->nullable();
            $table->json('attachments')->nullable();
            $table->string('submission_reference', 50)->nullable();
            $table->timestamps();
            
            $table->index(['supplier_id', 'reporting_year']);
            $table->index(['status', 'created_at']);
            $table->index('submission_type');
        });

        // Collaboration Workspaces Table
        Schema::create('collaboration_workspaces', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->string('workspace_name');
            $table->text('description')->nullable();
            $table->enum('workspace_type', ['project', 'initiative', 'supplier_engagement', 'facility_group', 'cross_functional']);
            $table->json('participant_roles')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_public')->default(false);
            $table->foreignId('created_by_user_id')->constrained('users')->onDelete('cascade');
            $table->json('workspace_settings')->nullable();
            $table->timestamps();
            
            $table->index(['organization_id', 'workspace_type']);
            $table->index(['is_active', 'is_public']);
        });

        // Workspace Participants Table
        Schema::create('workspace_participants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('collaboration_workspace_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('supplier_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('participant_role', 50);
            $table->json('permissions')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('joined_at')->default(now());
            $table->timestamp('last_activity_at')->nullable();
            $table->timestamps();
            
            $table->index(['collaboration_workspace_id', 'is_active'], 'workspace_participants_workspace_active_idx');
            $table->index(['user_id', 'is_active']);
            $table->index(['supplier_id', 'is_active']);
        });

        // Engagement Metrics Table
        Schema::create('engagement_metrics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('supplier_id')->nullable()->constrained()->onDelete('set null');
            $table->string('metric_type', 50);
            $table->string('metric_name');
            $table->decimal('metric_value', 12, 4);
            $table->string('metric_unit', 20)->nullable();
            $table->date('metric_date');
            $table->string('reporting_period', 20);
            $table->json('metric_details')->nullable();
            $table->string('data_source', 100)->nullable();
            $table->timestamps();
            
            $table->index(['organization_id', 'metric_type', 'metric_date']);
            $table->index(['supplier_id', 'metric_type']);
            $table->index('metric_date');
        });

        // Action Templates Table
        Schema::create('action_templates', function (Blueprint $table) {
            $table->id();
            $table->string('template_name');
            $table->text('description');
            $table->enum('action_type', ['decarbonization', 'data_collection', 'reporting', 'compliance', 'engagement', 'training']);
            $table->string('category', 100);
            $table->json('template_data');
            $table->json('success_criteria_template')->nullable();
            $table->integer('estimated_duration_days')->nullable();
            $table->decimal('typical_cost_range_min', 12, 2)->nullable();
            $table->decimal('typical_cost_range_max', 12, 2)->nullable();
            $table->json('applicable_sectors')->nullable();
            $table->json('required_roles')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('usage_count')->default(0);
            $table->timestamps();
            
            $table->index(['action_type', 'is_active']);
            $table->index('category');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('action_templates');
        Schema::dropIfExists('engagement_metrics');
        Schema::dropIfExists('workspace_participants');
        Schema::dropIfExists('collaboration_workspaces');
        Schema::dropIfExists('supplier_data_submissions');
        Schema::dropIfExists('stakeholder_notifications');
        Schema::dropIfExists('stakeholder_dashboards');
        Schema::dropIfExists('action_progress_updates');
        Schema::dropIfExists('stakeholder_actions');
        Schema::dropIfExists('supplier_portal_accesses');
    }
};
