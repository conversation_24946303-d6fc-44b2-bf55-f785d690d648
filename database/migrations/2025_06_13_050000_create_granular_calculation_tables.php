<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Scope 3 Categories Table
        Schema::create('scope3_categories', function (Blueprint $table) {
            $table->id();
            $table->integer('category_number')->unique();
            $table->string('name');
            $table->text('description');
            $table->enum('upstream_downstream', ['upstream', 'downstream']);
            $table->json('calculation_approaches')->nullable();
            $table->json('data_requirements')->nullable();
            $table->json('typical_activities')->nullable();
            $table->boolean('is_active')->default(true);
            $table->text('guidance_notes')->nullable();
            $table->timestamps();

            $table->index(['category_number', 'is_active']);
            $table->index('upstream_downstream');
        });

        // Suppliers Table
        Schema::create('suppliers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('supplier_code')->unique()->nullable();
            $table->string('contact_email')->nullable();
            $table->string('contact_phone')->nullable();
            $table->text('address')->nullable();
            $table->string('country', 2)->nullable();
            $table->string('region')->nullable();
            $table->string('industry_sector')->nullable();
            $table->string('supplier_type')->nullable(); // direct, indirect, service
            $table->decimal('annual_spend', 15, 2)->nullable();
            $table->string('currency', 3)->default('USD');
            $table->enum('data_quality_rating', ['A', 'B', 'C', 'D'])->nullable();
            $table->enum('carbon_disclosure_level', ['CDP', 'GRI', 'TCFD', 'Custom', 'None'])->default('None');
            $table->json('sustainability_certifications')->nullable();
            $table->json('scope3_categories')->nullable();
            $table->json('primary_products_services')->nullable();
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['industry_sector', 'is_active']);
            $table->index(['country', 'region']);
            $table->index('data_quality_rating');
            $table->index('carbon_disclosure_level');
        });

        // Supplier Emissions Table
        Schema::create('supplier_emissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');
            $table->foreignId('scope3_category_id')->constrained()->onDelete('cascade');
            $table->date('reporting_period');
            $table->decimal('emissions_value', 15, 6);
            $table->string('unit', 20)->default('tCO2e');
            $table->enum('data_quality', ['A', 'B', 'C', 'D'])->default('C');
            $table->string('calculation_method')->nullable();
            $table->enum('verification_status', ['verified', 'unverified', 'third_party'])->default('unverified');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->unique(['supplier_id', 'scope3_category_id', 'reporting_period'], 'supplier_emissions_unique');
            $table->index(['reporting_period', 'data_quality']);
        });

        // Supplier Spend Data Table
        Schema::create('supplier_spend', function (Blueprint $table) {
            $table->id();
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');
            $table->foreignId('scope3_category_id')->constrained()->onDelete('cascade');
            $table->date('period_start');
            $table->date('period_end');
            $table->decimal('spend_amount', 15, 2);
            $table->string('currency', 3)->default('USD');
            $table->string('spend_category')->nullable();
            $table->text('description')->nullable();
            $table->timestamps();

            $table->index(['supplier_id', 'period_start', 'period_end']);
            $table->index(['scope3_category_id', 'period_start']);
        });

        // Scope 3 Category Factors Junction Table
        Schema::create('scope3_category_factors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained('scope3_categories')->onDelete('cascade');
            $table->foreignId('factor_id')->constrained('ghg_protocol_emission_factors')->onDelete('cascade');
            $table->text('applicability_notes')->nullable();
            $table->integer('priority')->default(500);
            $table->timestamps();

            $table->unique(['category_id', 'factor_id']);
            $table->index('priority');
        });

        // Supplier Scope 3 Categories Junction Table
        Schema::create('supplier_scope3_categories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('supplier_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->constrained('scope3_categories')->onDelete('cascade');
            $table->string('calculation_method')->default('spend_based');
            $table->enum('data_quality', ['A', 'B', 'C', 'D'])->default('C');
            $table->decimal('annual_emissions', 15, 6)->nullable();
            $table->timestamps();

            $table->unique(['supplier_id', 'category_id'], 'supplier_scope3_categories_unique');
            $table->index('calculation_method');
        });

        // Spend Emission Factors Table
        Schema::create('spend_emission_factors', function (Blueprint $table) {
            $table->id();
            $table->string('industry_sector');
            $table->string('spend_category')->nullable();
            $table->string('region', 10)->default('global');
            $table->decimal('factor_value', 15, 8); // tCO2e per currency unit
            $table->string('currency', 3)->default('USD');
            $table->string('unit', 50)->default('tCO2e/USD');
            $table->string('data_source')->nullable();
            $table->integer('vintage_year')->nullable();
            $table->enum('data_quality_rating', ['High', 'Medium', 'Low'])->default('Medium');
            $table->integer('priority')->default(500);
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['industry_sector', 'region', 'is_active']);
            $table->index(['spend_category', 'currency']);
            $table->index('priority');
        });

        // Calculation Methodologies Table
        Schema::create('calculation_methodologies', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->string('name');
            $table->text('description');
            $table->string('version')->nullable();
            $table->json('supported_scopes');
            $table->json('default_options')->nullable();
            $table->json('required_data')->nullable();
            $table->boolean('is_active')->default(true);
            $table->text('guidance_notes')->nullable();
            $table->timestamps();

            $table->index(['code', 'is_active']);
        });

        // Calculation Results Table
        Schema::create('calculation_results', function (Blueprint $table) {
            $table->id();
            $table->foreignId('activity_data_id')->constrained()->onDelete('cascade');
            $table->string('methodology_code');
            $table->string('scope', 10);
            $table->decimal('total_co2e', 15, 6);
            $table->json('by_gas')->nullable(); // CO2, CH4, N2O breakdown
            $table->json('calculation_details');
            $table->json('methodology_details')->nullable();
            $table->json('uncertainty_assessment')->nullable();
            $table->json('data_quality_assessment')->nullable();
            $table->enum('calculation_status', ['success', 'warning', 'error'])->default('success');
            $table->text('notes')->nullable();
            $table->timestamp('calculated_at');
            $table->timestamps();

            $table->index(['activity_data_id', 'methodology_code']);
            $table->index(['scope', 'calculated_at']);
            $table->index('calculation_status');
            $table->foreign('methodology_code')->references('code')->on('calculation_methodologies');
        });

        // Fugitive Emissions Table
        Schema::create('fugitive_emissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('activity_data_id')->constrained()->onDelete('cascade');
            $table->string('fugitive_type'); // refrigerant, natural_gas_leak, etc.
            $table->string('substance')->nullable(); // R-410A, CH4, etc.
            $table->decimal('quantity', 15, 6);
            $table->string('unit', 20);
            $table->decimal('leakage_rate', 8, 6)->nullable();
            $table->integer('gwp_value')->nullable();
            $table->decimal('emissions_co2e', 15, 6);
            $table->json('calculation_parameters')->nullable();
            $table->timestamps();

            $table->index(['fugitive_type', 'substance']);
            $table->index('activity_data_id');
        });

        // Electricity Grid Factors Table (for Scope 2)
        Schema::create('electricity_grid_factors', function (Blueprint $table) {
            $table->id();
            $table->string('grid_region');
            $table->string('country', 2);
            $table->string('state_province')->nullable();
            $table->decimal('location_based_factor', 15, 8); // kg CO2e/kWh
            $table->decimal('residual_mix_factor', 15, 8)->nullable(); // for market-based
            $table->integer('vintage_year');
            $table->string('data_source')->default('eGRID');
            $table->enum('data_quality', ['High', 'Medium', 'Low'])->default('Medium');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['grid_region', 'vintage_year', 'is_active']);
            $table->index(['country', 'state_province']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('electricity_grid_factors');
        Schema::dropIfExists('fugitive_emissions');
        Schema::dropIfExists('calculation_results');
        Schema::dropIfExists('calculation_methodologies');
        Schema::dropIfExists('spend_emission_factors');
        Schema::dropIfExists('supplier_scope3_categories');
        Schema::dropIfExists('scope3_category_factors');
        Schema::dropIfExists('supplier_spend');
        Schema::dropIfExists('supplier_emissions');
        Schema::dropIfExists('suppliers');
        Schema::dropIfExists('scope3_categories');
    }
};
