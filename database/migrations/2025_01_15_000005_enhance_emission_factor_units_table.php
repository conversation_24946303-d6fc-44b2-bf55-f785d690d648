<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if table exists and enhance it, or create if it doesn't
        if (Schema::hasTable('emission_factor_units')) {
            Schema::table('emission_factor_units', function (Blueprint $table) {
                // Add new columns if they don't exist
                if (!Schema::hasColumn('emission_factor_units', 'ghg_protocol_factor_id')) {
                    $table->foreignId('ghg_protocol_factor_id')->nullable()->constrained('ghg_protocol_emission_factors')->onDelete('cascade');
                }
                if (!Schema::hasColumn('emission_factor_units', 'unit_role')) {
                    $table->string('unit_role', 50)->default('input'); // 'input', 'output', 'per_unit', 'density'
                }
                if (!Schema::hasColumn('emission_factor_units', 'conversion_context')) {
                    $table->string('conversion_context', 100)->nullable(); // Context for when conversion applies
                }
                if (!Schema::hasColumn('emission_factor_units', 'is_primary')) {
                    $table->boolean('is_primary')->default(false); // Primary unit for this role
                }
            });
        } else {
            Schema::create('emission_factor_units', function (Blueprint $table) {
                $table->id();
                
                // Can link to either old or new emission factor tables
                $table->foreignId('emission_factor_id')->nullable()->constrained('emission_factors')->onDelete('cascade');
                $table->foreignId('ghg_protocol_factor_id')->nullable()->constrained('ghg_protocol_emission_factors')->onDelete('cascade');
                
                $table->string('unit_role', 50); // 'input', 'output', 'per_unit', 'density', 'heating_value'
                $table->foreignId('measurement_unit_id')->constrained('measurement_units')->onDelete('cascade');
                $table->decimal('conversion_factor', 18, 8)->default(1.0);
                $table->string('conversion_context', 100)->nullable(); // When this conversion applies
                $table->boolean('is_primary')->default(false); // Primary unit for this role
                $table->text('notes')->nullable();
                $table->timestamps();

                // Ensure we have a link to one of the factor tables
                $table->check('emission_factor_id IS NOT NULL OR ghg_protocol_factor_id IS NOT NULL');
                
                $table->index(['unit_role']);
                $table->index(['is_primary']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('emission_factor_units')) {
            Schema::table('emission_factor_units', function (Blueprint $table) {
                if (Schema::hasColumn('emission_factor_units', 'ghg_protocol_factor_id')) {
                    $table->dropForeign(['ghg_protocol_factor_id']);
                    $table->dropColumn('ghg_protocol_factor_id');
                }
                if (Schema::hasColumn('emission_factor_units', 'unit_role')) {
                    $table->dropColumn('unit_role');
                }
                if (Schema::hasColumn('emission_factor_units', 'conversion_context')) {
                    $table->dropColumn('conversion_context');
                }
                if (Schema::hasColumn('emission_factor_units', 'is_primary')) {
                    $table->dropColumn('is_primary');
                }
            });
        }
    }
};
