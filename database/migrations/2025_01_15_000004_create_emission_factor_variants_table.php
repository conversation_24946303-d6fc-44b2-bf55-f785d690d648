<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('emission_factor_variants', function (Blueprint $table) {
            $table->id();
            
            // Link to base emission factor
            $table->foreignId('base_factor_id')->constrained('ghg_protocol_emission_factors')->onDelete('cascade');
            
            // Variant identification
            $table->string('variant_type', 50); // 'regional', 'temporal', 'methodological', 'fuel_grade'
            $table->string('variant_code', 100); // Unique within the base factor
            $table->string('variant_name');
            $table->text('variant_description')->nullable();
            
            // Override values (only specify what's different from base factor)
            $table->foreignId('region_id')->nullable()->constrained('ghg_protocol_regions')->onDelete('set null');
            $table->decimal('factor_value', 20, 10)->nullable();
            $table->decimal('co2_factor', 20, 10)->nullable();
            $table->decimal('ch4_factor', 20, 10)->nullable();
            $table->decimal('n2o_factor', 20, 10)->nullable();
            $table->decimal('carbon_content', 15, 8)->nullable();
            $table->decimal('oxidation_factor', 8, 6)->nullable();
            $table->decimal('heating_value', 15, 6)->nullable();
            
            // Override units if different
            $table->foreignId('input_unit_id')->nullable()->constrained('measurement_units')->onDelete('set null');
            $table->foreignId('output_unit_id')->nullable()->constrained('measurement_units')->onDelete('set null');
            
            // Override quality metrics
            $table->string('data_quality_rating', 10)->nullable();
            $table->decimal('uncertainty_percentage', 8, 4)->nullable();
            $table->decimal('uncertainty_lower', 8, 4)->nullable();
            $table->decimal('uncertainty_upper', 8, 4)->nullable();
            
            // Override temporal validity
            $table->integer('vintage_year')->nullable();
            $table->date('valid_from')->nullable();
            $table->date('valid_until')->nullable();
            
            // Variant-specific metadata
            $table->json('variant_conditions')->nullable(); // Conditions when this variant applies
            $table->text('notes')->nullable();
            $table->integer('priority')->default(0); // Priority within variants of same type
            $table->boolean('is_active')->default(true);
            
            $table->timestamps();

            // Ensure unique variant codes within a base factor
            $table->unique(['base_factor_id', 'variant_code']);
            
            // Indexes
            $table->index(['variant_type']);
            $table->index(['region_id']);
            $table->index(['vintage_year']);
            $table->index(['is_active', 'priority']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('emission_factor_variants');
    }
};
