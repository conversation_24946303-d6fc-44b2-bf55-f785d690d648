<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('activity_data', function (Blueprint $table) {
            // Add GHG Protocol fields
            $table->unsignedBigInteger('ghg_protocol_sector_id')->nullable()->after('emission_factor_id');
            $table->unsignedBigInteger('ghg_protocol_activity_id')->nullable()->after('ghg_protocol_sector_id');
            $table->boolean('use_ghg_protocol')->default(false)->after('ghg_protocol_activity_id');

            // Add foreign key constraints
            $table->foreign('ghg_protocol_sector_id')->references('id')->on('ghg_protocol_sectors')->onDelete('set null');
            $table->foreign('ghg_protocol_activity_id')->references('id')->on('ghg_protocol_activities')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('activity_data', function (Blueprint $table) {
            // Drop foreign key constraints first
            $table->dropForeign(['ghg_protocol_sector_id']);
            $table->dropForeign(['ghg_protocol_activity_id']);

            // Drop columns
            $table->dropColumn(['ghg_protocol_sector_id', 'ghg_protocol_activity_id', 'use_ghg_protocol']);
        });
    }
};
