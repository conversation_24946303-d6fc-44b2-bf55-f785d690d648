<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Data Lake Entries Table
        Schema::create('data_lake_entries', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('source', 100)->index();
            $table->string('data_type', 100)->index();
            $table->unsignedBigInteger('facility_id')->nullable()->index();
            $table->unsignedBigInteger('organization_id')->nullable()->index();
            $table->longText('raw_data')->nullable();
            $table->json('metadata')->nullable();
            $table->string('file_path')->nullable();
            $table->bigInteger('file_size')->nullable();
            $table->string('compression', 20)->default('none');
            $table->string('checksum', 32)->nullable();
            $table->integer('version')->default(1);
            $table->timestamp('created_at');
            $table->timestamp('updated_at');
            $table->timestamp('archived_at')->nullable()->index();
            $table->timestamp('retention_until')->nullable()->index();
            
            $table->index(['source', 'data_type']);
            $table->index(['facility_id', 'created_at']);
            $table->index(['organization_id', 'created_at']);
            $table->index(['created_at', 'archived_at']);
        });

        // Data Lake Versions Table
        Schema::create('data_lake_versions', function (Blueprint $table) {
            $table->id();
            $table->string('original_id');
            $table->string('version_id');
            $table->integer('version_number');
            $table->json('changes_summary')->nullable();
            $table->timestamp('created_at');
            
            $table->index(['original_id', 'version_number']);
            $table->foreign('original_id')->references('id')->on('data_lake_entries')->onDelete('cascade');
            $table->foreign('version_id')->references('id')->on('data_lake_entries')->onDelete('cascade');
        });

        // IoT Sensors Table
        Schema::create('iot_sensors', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('name');
            $table->string('type', 50)->index();
            $table->unsignedBigInteger('facility_id')->index();
            $table->string('location')->nullable();
            $table->string('endpoint')->nullable();
            $table->string('api_key')->nullable();
            $table->string('protocol', 20)->default('http');
            $table->string('data_format', 20)->default('json');
            $table->integer('polling_interval')->default(300);
            $table->string('unit', 20)->nullable();
            $table->decimal('calibration_factor', 10, 6)->default(1.0);
            $table->string('status', 20)->default('active')->index();
            $table->timestamp('registered_at');
            $table->timestamp('last_reading_at')->nullable();
            
            $table->index(['facility_id', 'type']);
            $table->index(['status', 'type']);
            $table->foreign('facility_id')->references('id')->on('facilities')->onDelete('cascade');
        });

        // Supplier Connections Table
        Schema::create('supplier_connections', function (Blueprint $table) {
            $table->id();
            $table->string('supplier_id')->unique();
            $table->string('portal_type', 50)->index();
            $table->string('endpoint');
            $table->json('credentials');
            $table->json('data_mapping')->nullable();
            $table->string('sync_frequency', 20)->default('monthly');
            $table->timestamp('last_sync')->nullable();
            $table->string('status', 20)->default('active')->index();
            $table->timestamp('connected_at');
            $table->timestamps();
            
            $table->index(['portal_type', 'status']);
        });

        // ERP Connections Table
        Schema::create('erp_connections', function (Blueprint $table) {
            $table->id();
            $table->string('system_type', 50); // sap, oracle, etc.
            $table->string('endpoint');
            $table->json('credentials');
            $table->json('configuration')->nullable();
            $table->string('status', 20)->default('active')->index();
            $table->timestamp('connected_at');
            $table->timestamp('last_sync')->nullable();
            $table->timestamps();
            
            $table->index(['system_type', 'status']);
        });

        // Data Integration Jobs Table
        Schema::create('data_integration_jobs', function (Blueprint $table) {
            $table->id();
            $table->string('job_type', 50); // erp_sync, iot_poll, supplier_sync
            $table->string('source_id')->nullable(); // sensor_id, supplier_id, etc.
            $table->string('status', 20)->default('pending')->index();
            $table->json('parameters')->nullable();
            $table->json('results')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
            
            $table->index(['job_type', 'status']);
            $table->index(['created_at', 'status']);
        });

        // Data Quality Metrics Table
        Schema::create('data_quality_metrics', function (Blueprint $table) {
            $table->id();
            $table->string('data_source', 100)->index();
            $table->string('metric_type', 50); // completeness, accuracy, timeliness
            $table->decimal('metric_value', 5, 2); // percentage or score
            $table->json('details')->nullable();
            $table->date('measurement_date')->index();
            $table->timestamps();
            
            $table->index(['data_source', 'measurement_date']);
            $table->index(['metric_type', 'measurement_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('data_quality_metrics');
        Schema::dropIfExists('data_integration_jobs');
        Schema::dropIfExists('erp_connections');
        Schema::dropIfExists('supplier_connections');
        Schema::dropIfExists('iot_sensors');
        Schema::dropIfExists('data_lake_versions');
        Schema::dropIfExists('data_lake_entries');
    }
};
