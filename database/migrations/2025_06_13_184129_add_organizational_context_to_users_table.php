<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Organizational context
            $table->foreignId('organization_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('organizational_unit_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('facility_id')->nullable()->constrained()->onDelete('set null');

            // User profile
            $table->string('job_title')->nullable();
            $table->string('department')->nullable();
            $table->enum('user_type', [
                'facility_operator',
                'sustainability_manager',
                'department_manager',
                'executive',
                'auditor',
                'consultant'
            ])->default('facility_operator');
            $table->enum('experience_level', ['novice', 'intermediate', 'expert'])->default('novice');
            $table->enum('preferred_interface', ['simplified', 'advanced', 'auto'])->default('auto');

            // Default settings
            $table->enum('default_scope', ['scope_1', 'scope_2', 'scope_3'])->nullable();
            $table->boolean('is_active')->default(true);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['organization_id']);
            $table->dropForeign(['organizational_unit_id']);
            $table->dropForeign(['facility_id']);

            $table->dropColumn([
                'organization_id',
                'organizational_unit_id',
                'facility_id',
                'job_title',
                'department',
                'user_type',
                'experience_level',
                'preferred_interface',
                'default_scope',
                'is_active',
            ]);
        });
    }
};
