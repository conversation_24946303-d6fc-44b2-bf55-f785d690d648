<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ghg_protocol_emission_factors', function (Blueprint $table) {
            $table->id();

            // Core identification
            $table->string('code', 100)->unique(); // Unique identifier for this factor
            $table->string('name');
            $table->text('description')->nullable();

            // GHG Protocol classification
            $table->foreignId('sector_id')->constrained('ghg_protocol_sectors')->onDelete('cascade');
            $table->foreignId('activity_id')->constrained('ghg_protocol_activities')->onDelete('cascade');
            $table->foreignId('region_id')->nullable()->constrained('ghg_protocol_regions')->onDelete('set null');

            // Gas and calculation details
            $table->foreignId('gas_id')->constrained('greenhouse_gases')->onDelete('cascade');
            $table->string('calculation_method', 100); // 'tier1', 'tier2', 'tier3', 'country_specific', etc.
            $table->string('methodology_reference', 500)->nullable();

            // Factor values - supporting multiple calculation approaches
            $table->decimal('factor_value', 20, 10); // Primary emission factor
            $table->decimal('co2_factor', 20, 10)->nullable(); // CO2 component
            $table->decimal('ch4_factor', 20, 10)->nullable(); // CH4 component
            $table->decimal('n2o_factor', 20, 10)->nullable(); // N2O component

            // Combustion-specific factors
            $table->decimal('carbon_content', 15, 8)->nullable(); // tC/TJ or similar
            $table->decimal('oxidation_factor', 8, 6)->nullable(); // Fraction oxidized
            $table->decimal('heating_value', 15, 6)->nullable(); // Net calorific value
            $table->string('heating_value_type', 20)->nullable(); // 'net', 'gross'

            // Units
            $table->foreignId('input_unit_id')->constrained('measurement_units')->onDelete('cascade');
            $table->foreignId('output_unit_id')->constrained('measurement_units')->onDelete('cascade');

            // Quality and uncertainty
            $table->string('data_quality_rating', 10)->nullable(); // A, B, C, D
            $table->decimal('uncertainty_percentage', 8, 4)->nullable();
            $table->decimal('uncertainty_lower', 8, 4)->nullable();
            $table->decimal('uncertainty_upper', 8, 4)->nullable();
            $table->string('uncertainty_type', 30)->nullable(); // 'percentage', 'absolute', 'range'

            // Temporal validity
            $table->integer('vintage_year')->nullable(); // Year the data represents
            $table->date('valid_from')->nullable();
            $table->date('valid_until')->nullable();

            // Source and metadata
            $table->string('data_source', 200)->nullable();
            $table->text('data_source_detail')->nullable();
            $table->string('reference_link', 500)->nullable();
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // Additional flexible metadata

            // Flags
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('tier_level')->nullable(); // IPCC tier level
            $table->integer('priority')->default(0); // For factor selection priority

            $table->timestamps();

            // Indexes for efficient lookups
            $table->index(['sector_id', 'activity_id', 'region_id', 'gas_id'], 'ghg_factors_lookup_idx');
            $table->index(['calculation_method'], 'ghg_factors_method_idx');
            $table->index(['vintage_year'], 'ghg_factors_vintage_idx');
            $table->index(['is_default', 'is_active'], 'ghg_factors_status_idx');
            $table->index(['data_quality_rating'], 'ghg_factors_quality_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ghg_protocol_emission_factors');
    }
};
