<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Compliance Reports Table
        if (!Schema::hasTable('compliance_reports')) {
            Schema::create('compliance_reports', function (Blueprint $table) {
                $table->id();
                $table->foreignId('organization_id')->constrained()->onDelete('cascade');
                $table->string('report_name');
                $table->text('description')->nullable();
                $table->enum('framework_type', ['sec_climate_rule', 'csrd', 'tcfd', 'cdp', 'ghg_protocol', 'sbti', 'issb']);
                $table->integer('reporting_year');
                $table->json('report_data');
                $table->json('audit_trail_summary')->nullable();
                $table->string('file_path')->nullable();
                $table->string('file_format', 10)->default('json');
                $table->bigInteger('file_size')->nullable();
                $table->enum('status', ['draft', 'in_review', 'approved', 'submitted', 'published'])->default('draft');
                $table->timestamp('generated_at');
                $table->string('generated_by');
                $table->timestamp('approved_at')->nullable();
                $table->string('approved_by')->nullable();
                $table->timestamp('submitted_at')->nullable();
                $table->string('submission_reference')->nullable();
                $table->json('validation_results')->nullable();
                $table->string('report_hash', 64)->nullable();
                $table->timestamps();

                $table->index(['organization_id', 'framework_type', 'reporting_year'], 'compliance_reports_org_framework_year_idx');
                $table->index(['status', 'generated_at'], 'compliance_reports_status_date_idx');
                $table->index('framework_type');
            });
        }

        // Report Sections Table
        if (!Schema::hasTable('report_sections')) {
            Schema::create('report_sections', function (Blueprint $table) {
                $table->id();
                $table->foreignId('compliance_report_id')->constrained()->onDelete('cascade');
                $table->string('section_name');
                $table->string('section_type', 50);
                $table->integer('section_order');
                $table->json('section_data');
                $table->json('data_sources')->nullable();
                $table->json('calculation_references')->nullable();
                $table->string('section_hash', 64)->nullable();
                $table->timestamps();

                $table->index(['compliance_report_id', 'section_order']);
                $table->index('section_type');
            });
        }

        // Drill Down Analysis Table
        if (!Schema::hasTable('drill_down_analyses')) {
            Schema::create('drill_down_analyses', function (Blueprint $table) {
                $table->id();
                $table->foreignId('organization_id')->constrained()->onDelete('cascade');
                $table->string('analysis_name');
                $table->text('description')->nullable();
                $table->integer('reporting_year');
                $table->json('corporate_total');
                $table->json('scope_breakdown');
                $table->json('facility_breakdown');
                $table->json('source_breakdown');
                $table->json('activity_detail');
                $table->json('supplier_breakdown')->nullable();
                $table->json('traceability_matrix');
                $table->json('drill_down_paths');
                $table->json('summary_statistics');
                $table->timestamp('analysis_date');
                $table->string('analyzed_by');
                $table->enum('status', ['draft', 'completed', 'archived'])->default('draft');
                $table->timestamps();

                $table->index(['organization_id', 'reporting_year']);
                $table->index(['analysis_date', 'status']);
            });
        }

        // Framework Templates Table
        if (!Schema::hasTable('framework_templates')) {
            Schema::create('framework_templates', function (Blueprint $table) {
                $table->id();
                $table->string('framework_code', 50)->unique();
                $table->string('framework_name');
                $table->text('description');
                $table->string('version', 20);
                $table->json('template_structure');
                $table->json('required_sections');
                $table->json('validation_rules');
                $table->json('mapping_rules')->nullable();
                $table->boolean('is_active')->default(true);
                $table->date('effective_date');
                $table->date('expiry_date')->nullable();
                $table->timestamps();

                $table->index(['framework_code', 'is_active']);
                $table->index('effective_date');
            });
        }

        // Report Validations Table
        if (!Schema::hasTable('report_validations')) {
            Schema::create('report_validations', function (Blueprint $table) {
                $table->id();
                $table->foreignId('compliance_report_id')->constrained()->onDelete('cascade');
                $table->string('validation_type', 50);
                $table->string('validation_rule');
                $table->enum('validation_status', ['passed', 'failed', 'warning', 'skipped']);
                $table->text('validation_message')->nullable();
                $table->json('validation_details')->nullable();
                $table->timestamp('validated_at');
                $table->string('validated_by')->nullable();
                $table->timestamps();

                $table->index(['compliance_report_id', 'validation_status']);
                $table->index('validation_type');
            });
        }

        // Immutable Audit Entries Table
        if (!Schema::hasTable('immutable_audit_entries')) {
            Schema::create('immutable_audit_entries', function (Blueprint $table) {
                $table->id();
                $table->string('entry_hash', 64)->unique();
                $table->string('previous_hash', 64)->nullable();
                $table->timestamp('entry_timestamp');
                $table->json('entry_data');
                $table->string('entry_type', 50);
                $table->foreignId('organization_id')->nullable()->constrained()->onDelete('set null');
                $table->string('merkle_root', 64)->nullable();
                $table->integer('block_number')->nullable();
                $table->string('signature', 512)->nullable();
                $table->timestamps();

                $table->index(['entry_timestamp', 'organization_id']);
                $table->index('entry_type');
                $table->index('block_number');
            });
        }

        // Data Quality Assessments Table
        if (!Schema::hasTable('data_quality_assessments')) {
            Schema::create('data_quality_assessments', function (Blueprint $table) {
                $table->id();
                $table->foreignId('activity_data_id')->constrained('activity_data')->onDelete('cascade');
                $table->string('assessment_type', 50);
                $table->json('quality_dimensions');
                $table->decimal('overall_score', 5, 2);
                $table->string('quality_rating', 10);
                $table->json('quality_issues')->nullable();
                $table->json('improvement_recommendations')->nullable();
                $table->timestamp('assessed_at');
                $table->string('assessed_by')->nullable();
                $table->string('assessment_method', 50)->nullable();
                $table->timestamps();

                $table->index(['activity_data_id', 'assessed_at']);
                $table->index(['quality_rating', 'assessed_at']);
            });
        }

        // Report Approvals Table
        if (!Schema::hasTable('report_approvals')) {
            Schema::create('report_approvals', function (Blueprint $table) {
                $table->id();
                $table->foreignId('compliance_report_id')->constrained()->onDelete('cascade');
                $table->string('approval_stage', 50);
                $table->integer('approval_order');
                $table->string('approver_role', 100);
                $table->foreignId('approver_user_id')->nullable()->constrained('users')->onDelete('set null');
                $table->string('approver_name');
                $table->enum('approval_status', ['pending', 'approved', 'rejected', 'delegated']);
                $table->text('approval_comments')->nullable();
                $table->timestamp('approval_deadline')->nullable();
                $table->timestamp('approved_at')->nullable();
                $table->json('approval_conditions')->nullable();
                $table->string('digital_signature', 512)->nullable();
                $table->timestamps();

                $table->index(['compliance_report_id', 'approval_order']);
                $table->index(['approval_status', 'approval_deadline']);
            });
        }

        // External Assurance Table
        if (!Schema::hasTable('external_assurances')) {
            Schema::create('external_assurances', function (Blueprint $table) {
                $table->id();
                $table->foreignId('compliance_report_id')->constrained()->onDelete('cascade');
                $table->string('assurance_provider');
                $table->string('assurance_type', 50);
                $table->string('assurance_standard', 100);
                $table->json('scope_of_assurance');
                $table->enum('assurance_level', ['limited', 'reasonable']);
                $table->text('assurance_opinion')->nullable();
                $table->json('material_misstatements')->nullable();
                $table->json('recommendations')->nullable();
                $table->date('assurance_date');
                $table->string('assurance_report_reference')->nullable();
                $table->string('assurance_certificate_path')->nullable();
                $table->timestamps();

                $table->index(['compliance_report_id', 'assurance_date']);
                $table->index('assurance_provider');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('external_assurances');
        Schema::dropIfExists('report_approvals');
        Schema::dropIfExists('data_quality_assessments');
        Schema::dropIfExists('immutable_audit_entries');
        Schema::dropIfExists('report_validations');
        Schema::dropIfExists('framework_templates');
        Schema::dropIfExists('drill_down_analyses');
        Schema::dropIfExists('report_sections');
        Schema::dropIfExists('compliance_reports');
    }
};
