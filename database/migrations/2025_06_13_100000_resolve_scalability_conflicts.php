<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // System Modules Table - only create if not exists
        if (!Schema::hasTable('system_modules')) {
            Schema::create('system_modules', function (Blueprint $table) {
                $table->id();
                $table->string('module_name', 100)->unique();
                $table->string('module_class');
                $table->string('module_version', 20);
                $table->text('module_description');
                $table->json('module_dependencies')->nullable();
                $table->boolean('is_core_module')->default(false);
                $table->boolean('is_active')->default(false);
                $table->json('configuration')->nullable();
                $table->timestamp('installed_at')->nullable();
                $table->timestamp('activated_at')->nullable();
                $table->timestamp('deactivated_at')->nullable();
                $table->timestamps();

                $table->index(['is_active', 'is_core_module']);
                $table->index('module_name');
            });
        }

        // Module Configurations Table - only create if not exists
        if (!Schema::hasTable('module_configurations')) {
            Schema::create('module_configurations', function (Blueprint $table) {
                $table->id();
                $table->string('module_name', 100);
                $table->json('configuration');
                $table->json('schema')->nullable();
                $table->boolean('is_valid')->default(true);
                $table->json('validation_errors')->nullable();
                $table->timestamps();

                $table->unique('module_name');
                $table->index('is_valid');
            });
        }

        // Regional Configurations Table - only create if not exists
        if (!Schema::hasTable('regional_configurations')) {
            Schema::create('regional_configurations', function (Blueprint $table) {
                $table->id();
                $table->string('region_code', 10);
                $table->string('region_name');
                $table->string('region_type', 50); // country, state, province, grid_region
                $table->string('parent_region_code', 10)->nullable();
                $table->string('iso_country_code', 3)->nullable();
                $table->string('iso_subdivision_code', 10)->nullable();
                $table->string('currency_code', 3)->nullable();
                $table->string('language_code', 5)->nullable();
                $table->string('timezone', 50)->nullable();
                $table->json('regulatory_frameworks')->nullable();
                $table->json('compliance_requirements')->nullable();
                $table->json('localization_settings')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();

                $table->unique('region_code');
                $table->index(['region_type', 'is_active']);
                $table->index('iso_country_code');
            });
        }

        // Add foreign key for regional configurations if table was created
        if (Schema::hasTable('regional_configurations')) {
            try {
                Schema::table('regional_configurations', function (Blueprint $table) {
                    $table->foreign('parent_region_code')->references('region_code')->on('regional_configurations')->onDelete('set null');
                });
            } catch (\Exception $e) {
                // Foreign key might already exist, ignore error
            }
        }

        // Regulatory Frameworks Table - only create if not exists
        if (!Schema::hasTable('regulatory_frameworks')) {
            Schema::create('regulatory_frameworks', function (Blueprint $table) {
                $table->id();
                $table->string('framework_code', 50)->unique();
                $table->string('framework_name');
                $table->text('description');
                $table->string('framework_type', 50); // cap_and_trade, carbon_tax, ets, voluntary
                $table->string('jurisdiction', 100);
                $table->json('applicable_regions')->nullable();
                $table->json('applicable_sectors')->nullable();
                $table->json('compliance_rules')->nullable();
                $table->json('reporting_requirements')->nullable();
                $table->json('pricing_mechanisms')->nullable();
                $table->date('effective_date')->nullable();
                $table->date('expiry_date')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();

                $table->index(['framework_type', 'is_active']);
                $table->index('jurisdiction');
            });
        }

        // AI/ML Models Table - only create if not exists
        if (!Schema::hasTable('ml_models')) {
            Schema::create('ml_models', function (Blueprint $table) {
                $table->id();
                $table->string('model_name', 100);
                $table->string('model_type', 50); // forecasting, classification, regression, clustering
                $table->string('model_purpose', 100); // emissions_forecasting, anomaly_detection, optimization
                $table->string('model_version', 20);
                $table->text('model_description');
                $table->string('model_framework', 50); // tensorflow, pytorch, scikit-learn, prophet
                $table->json('model_parameters')->nullable();
                $table->json('training_data_sources')->nullable();
                $table->json('feature_columns')->nullable();
                $table->json('target_columns')->nullable();
                $table->json('performance_metrics')->nullable();
                $table->string('model_file_path')->nullable();
                $table->string('status', 20)->default('training'); // training, trained, deployed, deprecated
                $table->timestamp('trained_at')->nullable();
                $table->timestamp('deployed_at')->nullable();
                $table->timestamp('last_prediction_at')->nullable();
                $table->timestamps();

                $table->index(['model_type', 'status']);
                $table->index(['model_purpose', 'status']);
            });
        }

        // ML Predictions Table - only create if not exists
        if (!Schema::hasTable('ml_predictions')) {
            Schema::create('ml_predictions', function (Blueprint $table) {
                $table->id();
                $table->foreignId('ml_model_id')->constrained()->onDelete('cascade');
                $table->foreignId('organization_id')->nullable()->constrained()->onDelete('cascade');
                $table->foreignId('facility_id')->nullable()->constrained()->onDelete('cascade');
                $table->string('prediction_type', 50); // emissions_forecast, growth_scenario, optimization
                $table->json('input_data');
                $table->json('prediction_results');
                $table->json('confidence_intervals')->nullable();
                $table->decimal('confidence_score', 5, 4)->nullable();
                $table->date('prediction_date');
                $table->date('forecast_start_date')->nullable();
                $table->date('forecast_end_date')->nullable();
                $table->json('scenario_parameters')->nullable();
                $table->json('metadata')->nullable();
                $table->timestamps();

                $table->index(['ml_model_id', 'prediction_date']);
                $table->index(['organization_id', 'prediction_type']);
                $table->index(['prediction_type', 'prediction_date']);
            });
        }

        // Localization Strings Table - only create if not exists
        if (!Schema::hasTable('localization_strings')) {
            Schema::create('localization_strings', function (Blueprint $table) {
                $table->id();
                $table->string('string_key', 200);
                $table->string('language_code', 5);
                $table->text('string_value');
                $table->string('context', 100)->nullable();
                $table->string('module_name', 100)->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();

                $table->unique(['string_key', 'language_code']);
                $table->index(['language_code', 'module_name']);
                $table->index('context');
            });
        }

        // Currency Exchange Rates Table - check if exists and has correct structure
        if (!Schema::hasTable('currency_exchange_rates')) {
            Schema::create('currency_exchange_rates', function (Blueprint $table) {
                $table->id();
                $table->string('base_currency', 3);
                $table->string('target_currency', 3);
                $table->decimal('exchange_rate', 12, 6);
                $table->date('rate_date');
                $table->string('data_source', 50)->nullable();
                $table->timestamp('last_updated_at');
                $table->timestamps();

                $table->unique(['base_currency', 'target_currency', 'rate_date']);
                $table->index(['base_currency', 'rate_date']);
                $table->index('rate_date');
            });
        }

        // System Extensions Table - only create if not exists
        if (!Schema::hasTable('system_extensions')) {
            Schema::create('system_extensions', function (Blueprint $table) {
                $table->id();
                $table->string('extension_name', 100);
                $table->string('extension_type', 50); // api_integration, data_connector, calculation_engine
                $table->string('extension_version', 20);
                $table->text('extension_description');
                $table->json('extension_config')->nullable();
                $table->string('endpoint_url')->nullable();
                $table->json('authentication_config')->nullable();
                $table->json('data_mapping')->nullable();
                $table->boolean('is_active')->default(false);
                $table->timestamp('last_sync_at')->nullable();
                $table->json('sync_status')->nullable();
                $table->timestamps();

                $table->index(['extension_type', 'is_active']);
                $table->index('extension_name');
            });
        }

        // Feature Flags Table - only create if not exists
        if (!Schema::hasTable('feature_flags')) {
            Schema::create('feature_flags', function (Blueprint $table) {
                $table->id();
                $table->string('flag_name', 100)->unique();
                $table->string('flag_description');
                $table->boolean('is_enabled')->default(false);
                $table->json('conditions')->nullable(); // user_roles, organizations, regions
                $table->decimal('rollout_percentage', 5, 2)->default(0); // 0-100%
                $table->string('flag_type', 50)->default('boolean'); // boolean, string, number
                $table->text('flag_value')->nullable();
                $table->timestamp('enabled_at')->nullable();
                $table->timestamp('disabled_at')->nullable();
                $table->timestamps();

                $table->index('is_enabled');
                $table->index('flag_type');
            });
        }

        // API Rate Limits Table - only create if not exists
        if (!Schema::hasTable('api_rate_limits')) {
            Schema::create('api_rate_limits', function (Blueprint $table) {
                $table->id();
                $table->string('identifier', 100); // user_id, ip_address, api_key
                $table->string('identifier_type', 20); // user, ip, api_key
                $table->string('endpoint', 200);
                $table->integer('requests_count')->default(0);
                $table->integer('requests_limit');
                $table->timestamp('window_start');
                $table->timestamp('window_end');
                $table->timestamp('last_request_at');
                $table->timestamps();

                $table->unique(['identifier', 'endpoint', 'window_start']);
                $table->index(['identifier_type', 'window_start']);
                $table->index('endpoint');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_rate_limits');
        Schema::dropIfExists('feature_flags');
        Schema::dropIfExists('system_extensions');
        Schema::dropIfExists('localization_strings');
        Schema::dropIfExists('ml_predictions');
        Schema::dropIfExists('ml_models');
        Schema::dropIfExists('regulatory_frameworks');
        Schema::dropIfExists('regional_configurations');
        Schema::dropIfExists('module_configurations');
        Schema::dropIfExists('system_modules');
    }


};
