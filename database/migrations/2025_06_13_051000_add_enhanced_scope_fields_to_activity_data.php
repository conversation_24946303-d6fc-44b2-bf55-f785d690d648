<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('activity_data', function (Blueprint $table) {
            // Enhanced scope fields
            $table->foreignId('scope3_category_id')->nullable()->after('use_ghg_protocol');
            $table->foreignId('supplier_id')->nullable()->after('scope3_category_id');
            $table->string('calculation_methodology')->nullable()->after('supplier_id');
            $table->string('emission_type')->nullable()->after('calculation_methodology'); // combustion, fugitive, process
            $table->string('scope2_method')->nullable()->after('emission_type'); // location_based, market_based
            $table->decimal('contractual_factor', 15, 8)->nullable()->after('scope2_method');
            $table->decimal('supplier_factor', 15, 8)->nullable()->after('contractual_factor');
            
            // Add foreign key constraints
            $table->foreign('scope3_category_id')->references('id')->on('scope3_categories')->onDelete('set null');
            $table->foreign('supplier_id')->references('id')->on('suppliers')->onDelete('set null');
            
            // Add indexes for performance
            $table->index(['scope', 'scope3_category_id']);
            $table->index(['supplier_id', 'scope3_category_id']);
            $table->index('calculation_methodology');
            $table->index('emission_type');
            $table->index('scope2_method');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('activity_data', function (Blueprint $table) {
            // Drop foreign key constraints first
            $table->dropForeign(['scope3_category_id']);
            $table->dropForeign(['supplier_id']);
            
            // Drop indexes
            $table->dropIndex(['scope', 'scope3_category_id']);
            $table->dropIndex(['supplier_id', 'scope3_category_id']);
            $table->dropIndex(['calculation_methodology']);
            $table->dropIndex(['emission_type']);
            $table->dropIndex(['scope2_method']);
            
            // Drop columns
            $table->dropColumn([
                'scope3_category_id',
                'supplier_id',
                'calculation_methodology',
                'emission_type',
                'scope2_method',
                'contractual_factor',
                'supplier_factor',
            ]);
        });
    }
};
