/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
DROP TABLE IF EXISTS `abatement_options`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `abatement_options` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `option_code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `applicable_sectors` json NOT NULL,
  `applicable_scopes` json NOT NULL,
  `capital_cost_per_tonne` decimal(10,2) NOT NULL,
  `operational_cost_per_tonne` decimal(10,2) NOT NULL,
  `energy_savings_per_tonne` decimal(8,2) DEFAULT NULL,
  `other_savings_per_tonne` decimal(8,2) DEFAULT NULL,
  `implementation_complexity` int NOT NULL,
  `implementation_time` int NOT NULL,
  `max_reduction_percentage` decimal(5,4) NOT NULL,
  `max_absolute_reduction` decimal(15,2) DEFAULT NULL,
  `co_benefits` json DEFAULT NULL,
  `risks` json DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `abatement_options_option_code_unique` (`option_code`),
  KEY `abatement_options_category_is_active_index` (`category`,`is_active`),
  KEY `abatement_options_option_code_index` (`option_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `action_progress_updates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `action_progress_updates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `stakeholder_action_id` bigint unsigned NOT NULL,
  `update_date` date NOT NULL,
  `progress_percentage` decimal(5,2) NOT NULL,
  `status` enum('assigned','in_progress','pending_approval','approved','completed','cancelled','overdue') COLLATE utf8mb4_unicode_ci NOT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `attachments` json DEFAULT NULL,
  `actual_emissions_reduction` decimal(12,2) DEFAULT NULL,
  `actual_cost` decimal(12,2) DEFAULT NULL,
  `actual_savings` decimal(12,2) DEFAULT NULL,
  `metrics` json DEFAULT NULL,
  `updated_by_user_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `action_progress_updates_updated_by_user_id_foreign` (`updated_by_user_id`),
  KEY `action_progress_updates_stakeholder_action_id_update_date_index` (`stakeholder_action_id`,`update_date`),
  KEY `action_progress_updates_update_date_index` (`update_date`),
  CONSTRAINT `action_progress_updates_stakeholder_action_id_foreign` FOREIGN KEY (`stakeholder_action_id`) REFERENCES `stakeholder_actions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `action_progress_updates_updated_by_user_id_foreign` FOREIGN KEY (`updated_by_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `action_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `action_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `action_type` enum('decarbonization','data_collection','reporting','compliance','engagement','training') COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `template_data` json NOT NULL,
  `success_criteria_template` json DEFAULT NULL,
  `estimated_duration_days` int DEFAULT NULL,
  `typical_cost_range_min` decimal(12,2) DEFAULT NULL,
  `typical_cost_range_max` decimal(12,2) DEFAULT NULL,
  `applicable_sectors` json DEFAULT NULL,
  `required_roles` json DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `usage_count` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `action_templates_action_type_is_active_index` (`action_type`,`is_active`),
  KEY `action_templates_category_index` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `activity_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `activity_data` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned DEFAULT NULL,
  `facility_id` bigint unsigned NOT NULL,
  `source_id` bigint unsigned NOT NULL,
  `emission_factor_id` bigint unsigned DEFAULT NULL,
  `ghg_protocol_sector_id` bigint unsigned DEFAULT NULL,
  `ghg_protocol_activity_id` bigint unsigned DEFAULT NULL,
  `use_ghg_protocol` tinyint(1) NOT NULL DEFAULT '0',
  `scope3_category_id` bigint unsigned DEFAULT NULL,
  `supplier_id` bigint unsigned DEFAULT NULL,
  `calculation_methodology` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `emission_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `scope2_method` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contractual_factor` decimal(15,8) DEFAULT NULL,
  `supplier_factor` decimal(15,8) DEFAULT NULL,
  `category_id` bigint unsigned NOT NULL,
  `quantity` decimal(18,4) NOT NULL,
  `activity_unit_id` bigint unsigned NOT NULL,
  `date_recorded` date NOT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `data_quality` int DEFAULT NULL,
  `is_estimated` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `date_start` date DEFAULT NULL COMMENT 'Activity period start date',
  `date_end` date DEFAULT NULL COMMENT 'Activity period end date',
  `scope` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'GHG scope (1, 2, or 3)',
  `material_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Fuel or material type',
  `evidence` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Supporting documentation file paths',
  PRIMARY KEY (`id`),
  KEY `activity_data_facility_id_foreign` (`facility_id`),
  KEY `activity_data_source_id_foreign` (`source_id`),
  KEY `activity_data_category_id_foreign` (`category_id`),
  KEY `activity_data_unit_id_foreign` (`activity_unit_id`),
  KEY `activity_data_organization_id_foreign` (`organization_id`),
  KEY `activity_data_emission_factor_id_foreign` (`emission_factor_id`),
  KEY `activity_data_ghg_protocol_sector_id_foreign` (`ghg_protocol_sector_id`),
  KEY `activity_data_ghg_protocol_activity_id_foreign` (`ghg_protocol_activity_id`),
  KEY `activity_data_scope3_category_id_foreign` (`scope3_category_id`),
  KEY `activity_data_scope_scope3_category_id_index` (`scope`,`scope3_category_id`),
  KEY `activity_data_supplier_id_scope3_category_id_index` (`supplier_id`,`scope3_category_id`),
  KEY `activity_data_calculation_methodology_index` (`calculation_methodology`),
  KEY `activity_data_emission_type_index` (`emission_type`),
  KEY `activity_data_scope2_method_index` (`scope2_method`),
  CONSTRAINT `activity_data_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `emission_categories` (`id`),
  CONSTRAINT `activity_data_emission_factor_id_foreign` FOREIGN KEY (`emission_factor_id`) REFERENCES `emission_factors` (`id`),
  CONSTRAINT `activity_data_facility_id_foreign` FOREIGN KEY (`facility_id`) REFERENCES `facilities` (`id`),
  CONSTRAINT `activity_data_ghg_protocol_activity_id_foreign` FOREIGN KEY (`ghg_protocol_activity_id`) REFERENCES `ghg_protocol_activities` (`id`) ON DELETE SET NULL,
  CONSTRAINT `activity_data_ghg_protocol_sector_id_foreign` FOREIGN KEY (`ghg_protocol_sector_id`) REFERENCES `ghg_protocol_sectors` (`id`) ON DELETE SET NULL,
  CONSTRAINT `activity_data_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`),
  CONSTRAINT `activity_data_scope3_category_id_foreign` FOREIGN KEY (`scope3_category_id`) REFERENCES `scope3_categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `activity_data_source_id_foreign` FOREIGN KEY (`source_id`) REFERENCES `emission_sources` (`id`),
  CONSTRAINT `activity_data_supplier_id_foreign` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `activity_data_unit_id_foreign` FOREIGN KEY (`activity_unit_id`) REFERENCES `measurement_units` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `audit_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audit_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned DEFAULT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `user_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `action` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `table_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `record_id` bigint unsigned NOT NULL,
  `old_values` json DEFAULT NULL,
  `new_values` json DEFAULT NULL,
  `action_timestamp` timestamp NOT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `session_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `request_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `context_data` json DEFAULT NULL,
  `integrity_hash` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `audit_logs_organization_id_action_timestamp_index` (`organization_id`,`action_timestamp`),
  KEY `audit_logs_table_name_record_id_index` (`table_name`,`record_id`),
  KEY `audit_logs_user_id_action_timestamp_index` (`user_id`,`action_timestamp`),
  KEY `audit_logs_action_timestamp_index` (`action_timestamp`),
  CONSTRAINT `audit_logs_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE SET NULL,
  CONSTRAINT `audit_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `business_units`;
/*!50001 DROP VIEW IF EXISTS `business_units`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `business_units` AS SELECT 
 1 AS `id`,
 1 AS `name`,
 1 AS `type`,
 1 AS `code`,
 1 AS `description`,
 1 AS `parent_id`,
 1 AS `organization_id`,
 1 AS `is_active`,
 1 AS `created_at`,
 1 AS `updated_at`*/;
SET character_set_client = @saved_cs_client;
DROP TABLE IF EXISTS `cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cache` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cache_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cache_locks` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `calculated_emissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `calculated_emissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `activity_data_id` bigint unsigned NOT NULL,
  `emission_factor_id` bigint unsigned NOT NULL,
  `gas_id` bigint unsigned NOT NULL,
  `amount` decimal(18,6) NOT NULL,
  `emission_unit_id` bigint unsigned NOT NULL,
  `co2e_amount` decimal(18,6) NOT NULL,
  `calculation_method` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `verified` tinyint(1) NOT NULL DEFAULT '0',
  `verification_date` date DEFAULT NULL,
  `verified_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `calculated_emissions_activity_data_id_foreign` (`activity_data_id`),
  KEY `calculated_emissions_emission_factor_id_foreign` (`emission_factor_id`),
  KEY `calculated_emissions_gas_id_foreign` (`gas_id`),
  KEY `calculated_emissions_unit_id_foreign` (`emission_unit_id`),
  CONSTRAINT `calculated_emissions_activity_data_id_foreign` FOREIGN KEY (`activity_data_id`) REFERENCES `activity_data` (`id`),
  CONSTRAINT `calculated_emissions_emission_factor_id_foreign` FOREIGN KEY (`emission_factor_id`) REFERENCES `emission_factors` (`id`),
  CONSTRAINT `calculated_emissions_gas_id_foreign` FOREIGN KEY (`gas_id`) REFERENCES `greenhouse_gases` (`id`),
  CONSTRAINT `calculated_emissions_unit_id_foreign` FOREIGN KEY (`emission_unit_id`) REFERENCES `measurement_units` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `calculation_audit_trails`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `calculation_audit_trails` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `emission_calculation_id` bigint unsigned NOT NULL,
  `calculation_method` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `methodology_framework` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'GHG Protocol',
  `input_parameters` json NOT NULL,
  `calculation_steps` json NOT NULL,
  `intermediate_results` json DEFAULT NULL,
  `emission_factor_details` json NOT NULL,
  `unit_conversions` json DEFAULT NULL,
  `uncertainty_assessment` decimal(8,4) DEFAULT NULL,
  `calculation_engine_version` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `calculation_timestamp` timestamp NOT NULL,
  `calculated_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `calculation_hash` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `carbon_offsets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `carbon_offsets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `project_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `offset_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(18,6) NOT NULL,
  `offset_unit_id` bigint unsigned NOT NULL,
  `purchase_date` date NOT NULL,
  `retirement_date` date DEFAULT NULL,
  `registry` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `certificate_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `verification_body` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `project_location` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `cost` decimal(15,2) DEFAULT NULL,
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `amount_tons_co2e` decimal(18,6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `carbon_offsets_organization_id_foreign` (`organization_id`),
  KEY `carbon_offsets_unit_id_foreign` (`offset_unit_id`),
  CONSTRAINT `carbon_offsets_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`),
  CONSTRAINT `carbon_offsets_unit_id_foreign` FOREIGN KEY (`offset_unit_id`) REFERENCES `measurement_units` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `collaboration_workspaces`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `collaboration_workspaces` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `workspace_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `workspace_type` enum('project','initiative','supplier_engagement','facility_group','cross_functional') COLLATE utf8mb4_unicode_ci NOT NULL,
  `participant_roles` json DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `created_by_user_id` bigint unsigned NOT NULL,
  `workspace_settings` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `collaboration_workspaces_created_by_user_id_foreign` (`created_by_user_id`),
  KEY `collaboration_workspaces_organization_id_workspace_type_index` (`organization_id`,`workspace_type`),
  KEY `collaboration_workspaces_is_active_is_public_index` (`is_active`,`is_public`),
  CONSTRAINT `collaboration_workspaces_created_by_user_id_foreign` FOREIGN KEY (`created_by_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `collaboration_workspaces_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `compliance_reports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `compliance_reports` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `report_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `framework_type` enum('sec_climate_rule','csrd','tcfd','cdp','ghg_protocol','sbti','issb') COLLATE utf8mb4_unicode_ci NOT NULL,
  `reporting_year` int NOT NULL,
  `report_data` json NOT NULL,
  `audit_trail_summary` json DEFAULT NULL,
  `file_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `file_format` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'json',
  `file_size` bigint DEFAULT NULL,
  `status` enum('draft','in_review','approved','submitted','published') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft',
  `generated_at` timestamp NOT NULL,
  `generated_by` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `approved_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `submitted_at` timestamp NULL DEFAULT NULL,
  `submission_reference` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `validation_results` json DEFAULT NULL,
  `report_hash` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `compliance_reports_organization_id_foreign` (`organization_id`),
  CONSTRAINT `compliance_reports_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `currency_exchange_rates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `currency_exchange_rates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `base_currency` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL,
  `target_currency` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL,
  `exchange_rate` decimal(12,6) NOT NULL,
  `rate_date` date NOT NULL,
  `data_source` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_updated_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `data_integration_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `data_integration_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `job_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `source_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `parameters` json DEFAULT NULL,
  `results` json DEFAULT NULL,
  `error_message` text COLLATE utf8mb4_unicode_ci,
  `started_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `data_integration_jobs_job_type_status_index` (`job_type`,`status`),
  KEY `data_integration_jobs_created_at_status_index` (`created_at`,`status`),
  KEY `data_integration_jobs_status_index` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `data_lake_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `data_lake_entries` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `source` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `data_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `facility_id` bigint unsigned DEFAULT NULL,
  `organization_id` bigint unsigned DEFAULT NULL,
  `raw_data` longtext COLLATE utf8mb4_unicode_ci,
  `metadata` json DEFAULT NULL,
  `file_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `file_size` bigint DEFAULT NULL,
  `compression` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'none',
  `checksum` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `version` int NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL,
  `updated_at` timestamp NOT NULL,
  `archived_at` timestamp NULL DEFAULT NULL,
  `retention_until` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `data_lake_entries_source_data_type_index` (`source`,`data_type`),
  KEY `data_lake_entries_facility_id_created_at_index` (`facility_id`,`created_at`),
  KEY `data_lake_entries_organization_id_created_at_index` (`organization_id`,`created_at`),
  KEY `data_lake_entries_created_at_archived_at_index` (`created_at`,`archived_at`),
  KEY `data_lake_entries_source_index` (`source`),
  KEY `data_lake_entries_data_type_index` (`data_type`),
  KEY `data_lake_entries_facility_id_index` (`facility_id`),
  KEY `data_lake_entries_organization_id_index` (`organization_id`),
  KEY `data_lake_entries_archived_at_index` (`archived_at`),
  KEY `data_lake_entries_retention_until_index` (`retention_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `data_lake_versions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `data_lake_versions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `original_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `version_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `version_number` int NOT NULL,
  `changes_summary` json DEFAULT NULL,
  `created_at` timestamp NOT NULL,
  PRIMARY KEY (`id`),
  KEY `data_lake_versions_original_id_version_number_index` (`original_id`,`version_number`),
  KEY `data_lake_versions_version_id_foreign` (`version_id`),
  CONSTRAINT `data_lake_versions_original_id_foreign` FOREIGN KEY (`original_id`) REFERENCES `data_lake_entries` (`id`) ON DELETE CASCADE,
  CONSTRAINT `data_lake_versions_version_id_foreign` FOREIGN KEY (`version_id`) REFERENCES `data_lake_entries` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `data_lineage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `data_lineage` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `activity_data_id` bigint unsigned NOT NULL,
  `source_system_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `source_system_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `source_record_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ingestion_timestamp` timestamp NOT NULL,
  `transformation_steps` json DEFAULT NULL,
  `validation_results` json DEFAULT NULL,
  `data_quality_score` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `lineage_hash` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `data_quality_assessments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `data_quality_assessments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `activity_data_id` bigint unsigned NOT NULL,
  `assessment_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `quality_dimensions` json NOT NULL,
  `overall_score` decimal(5,2) NOT NULL,
  `quality_rating` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `quality_issues` json DEFAULT NULL,
  `improvement_recommendations` json DEFAULT NULL,
  `assessed_at` timestamp NOT NULL,
  `assessed_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `assessment_method` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `data_quality_assessments_activity_data_id_assessed_at_index` (`activity_data_id`,`assessed_at`),
  KEY `data_quality_assessments_quality_rating_assessed_at_index` (`quality_rating`,`assessed_at`),
  CONSTRAINT `data_quality_assessments_activity_data_id_foreign` FOREIGN KEY (`activity_data_id`) REFERENCES `activity_data` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `data_quality_metrics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `data_quality_metrics` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `data_source` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `metric_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `metric_value` decimal(5,2) NOT NULL,
  `details` json DEFAULT NULL,
  `measurement_date` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `data_quality_metrics_data_source_measurement_date_index` (`data_source`,`measurement_date`),
  KEY `data_quality_metrics_metric_type_measurement_date_index` (`metric_type`,`measurement_date`),
  KEY `data_quality_metrics_data_source_index` (`data_source`),
  KEY `data_quality_metrics_measurement_date_index` (`measurement_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `decarbonization_initiatives`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `decarbonization_initiatives` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `abatement_option_id` bigint unsigned DEFAULT NULL,
  `macc_analysis_id` bigint unsigned DEFAULT NULL,
  `initiative_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `scope_coverage` json NOT NULL,
  `estimated_reduction` decimal(15,2) NOT NULL,
  `estimated_cost` decimal(15,2) NOT NULL,
  `cost_per_tonne` decimal(10,2) NOT NULL,
  `planned_start_date` date NOT NULL,
  `planned_completion_date` date NOT NULL,
  `implementation_complexity` int NOT NULL,
  `status` enum('planned','approved','in_progress','completed','cancelled','on_hold') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'planned',
  `actual_reduction` decimal(15,2) DEFAULT NULL,
  `actual_cost` decimal(15,2) DEFAULT NULL,
  `progress_percentage` int NOT NULL DEFAULT '0',
  `milestones` json DEFAULT NULL,
  `risks` json DEFAULT NULL,
  `co_benefits` json DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `decarbonization_initiatives_abatement_option_id_foreign` (`abatement_option_id`),
  KEY `decarbonization_initiatives_macc_analysis_id_foreign` (`macc_analysis_id`),
  KEY `decarbonization_initiatives_organization_id_status_index` (`organization_id`,`status`),
  KEY `decarbonization_initiatives_category_status_index` (`category`,`status`),
  CONSTRAINT `decarbonization_initiatives_abatement_option_id_foreign` FOREIGN KEY (`abatement_option_id`) REFERENCES `abatement_options` (`id`) ON DELETE SET NULL,
  CONSTRAINT `decarbonization_initiatives_macc_analysis_id_foreign` FOREIGN KEY (`macc_analysis_id`) REFERENCES `macc_analyses` (`id`) ON DELETE SET NULL,
  CONSTRAINT `decarbonization_initiatives_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `drill_down_analyses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `drill_down_analyses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `analysis_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `reporting_year` int NOT NULL,
  `corporate_total` json NOT NULL,
  `scope_breakdown` json NOT NULL,
  `facility_breakdown` json NOT NULL,
  `source_breakdown` json NOT NULL,
  `activity_detail` json NOT NULL,
  `supplier_breakdown` json DEFAULT NULL,
  `traceability_matrix` json NOT NULL,
  `drill_down_paths` json NOT NULL,
  `summary_statistics` json NOT NULL,
  `analysis_date` timestamp NOT NULL,
  `analyzed_by` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('draft','completed','archived') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `drill_down_analyses_organization_id_reporting_year_index` (`organization_id`,`reporting_year`),
  KEY `drill_down_analyses_analysis_date_status_index` (`analysis_date`,`status`),
  CONSTRAINT `drill_down_analyses_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `emission_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `emission_categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `scope_id` bigint unsigned DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `emission_categories_scope_id_foreign` (`scope_id`),
  CONSTRAINT `emission_categories_scope_id_foreign` FOREIGN KEY (`scope_id`) REFERENCES `scopes` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `emission_factor_units`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `emission_factor_units` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `emission_factor_id` bigint unsigned NOT NULL,
  `unit_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `measurement_unit_id` bigint unsigned NOT NULL,
  `conversion_factor` decimal(18,8) NOT NULL DEFAULT '1.00000000',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `ghg_protocol_factor_id` bigint unsigned DEFAULT NULL,
  `unit_role` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'input',
  `conversion_context` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_primary` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ef_unit_type_unique` (`emission_factor_id`,`unit_type`,`measurement_unit_id`),
  KEY `emission_factor_units_measurement_unit_id_foreign` (`measurement_unit_id`),
  KEY `emission_factor_units_emission_factor_id_unit_type_index` (`emission_factor_id`,`unit_type`),
  KEY `emission_factor_units_ghg_protocol_factor_id_foreign` (`ghg_protocol_factor_id`),
  CONSTRAINT `emission_factor_units_emission_factor_id_foreign` FOREIGN KEY (`emission_factor_id`) REFERENCES `emission_factors` (`id`) ON DELETE CASCADE,
  CONSTRAINT `emission_factor_units_ghg_protocol_factor_id_foreign` FOREIGN KEY (`ghg_protocol_factor_id`) REFERENCES `ghg_protocol_emission_factors` (`id`) ON DELETE CASCADE,
  CONSTRAINT `emission_factor_units_measurement_unit_id_foreign` FOREIGN KEY (`measurement_unit_id`) REFERENCES `measurement_units` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `emission_factor_variants`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `emission_factor_variants` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `base_factor_id` bigint unsigned NOT NULL,
  `variant_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `variant_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `variant_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `variant_description` text COLLATE utf8mb4_unicode_ci,
  `region_id` bigint unsigned DEFAULT NULL,
  `factor_value` decimal(20,10) DEFAULT NULL,
  `co2_factor` decimal(20,10) DEFAULT NULL,
  `ch4_factor` decimal(20,10) DEFAULT NULL,
  `n2o_factor` decimal(20,10) DEFAULT NULL,
  `carbon_content` decimal(15,8) DEFAULT NULL,
  `oxidation_factor` decimal(8,6) DEFAULT NULL,
  `heating_value` decimal(15,6) DEFAULT NULL,
  `input_unit_id` bigint unsigned DEFAULT NULL,
  `output_unit_id` bigint unsigned DEFAULT NULL,
  `data_quality_rating` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `uncertainty_percentage` decimal(8,4) DEFAULT NULL,
  `uncertainty_lower` decimal(8,4) DEFAULT NULL,
  `uncertainty_upper` decimal(8,4) DEFAULT NULL,
  `vintage_year` int DEFAULT NULL,
  `valid_from` date DEFAULT NULL,
  `valid_until` date DEFAULT NULL,
  `variant_conditions` json DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `priority` int NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `emission_factor_variants_base_factor_id_variant_code_unique` (`base_factor_id`,`variant_code`),
  KEY `emission_factor_variants_input_unit_id_foreign` (`input_unit_id`),
  KEY `emission_factor_variants_output_unit_id_foreign` (`output_unit_id`),
  KEY `emission_factor_variants_variant_type_index` (`variant_type`),
  KEY `emission_factor_variants_region_id_index` (`region_id`),
  KEY `emission_factor_variants_vintage_year_index` (`vintage_year`),
  KEY `emission_factor_variants_is_active_priority_index` (`is_active`,`priority`),
  CONSTRAINT `emission_factor_variants_base_factor_id_foreign` FOREIGN KEY (`base_factor_id`) REFERENCES `ghg_protocol_emission_factors` (`id`) ON DELETE CASCADE,
  CONSTRAINT `emission_factor_variants_input_unit_id_foreign` FOREIGN KEY (`input_unit_id`) REFERENCES `measurement_units` (`id`) ON DELETE SET NULL,
  CONSTRAINT `emission_factor_variants_output_unit_id_foreign` FOREIGN KEY (`output_unit_id`) REFERENCES `measurement_units` (`id`) ON DELETE SET NULL,
  CONSTRAINT `emission_factor_variants_region_id_foreign` FOREIGN KEY (`region_id`) REFERENCES `ghg_protocol_regions` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `emission_factors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `emission_factors` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `factor_value` decimal(10,4) DEFAULT NULL,
  `co2_ef` decimal(10,4) DEFAULT NULL,
  `ch4_ef` decimal(10,4) DEFAULT NULL,
  `n2o_ef` decimal(10,4) DEFAULT NULL,
  `uncertainty` decimal(8,2) DEFAULT NULL,
  `year` int DEFAULT NULL,
  `region` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category_id` bigint unsigned DEFAULT NULL,
  `gas_id` bigint unsigned NOT NULL,
  `emission_unit_id` bigint unsigned NOT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `source_id` bigint unsigned DEFAULT NULL,
  `input_unit_id` bigint unsigned DEFAULT NULL,
  `output_unit_id` bigint unsigned DEFAULT NULL,
  `gwp` decimal(15,6) DEFAULT NULL,
  `valid_from` date DEFAULT NULL,
  `valid_until` date DEFAULT NULL,
  `uncertainty_percentage` decimal(8,2) DEFAULT NULL,
  `reference_link` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `calculation_methodology` text COLLATE utf8mb4_unicode_ci,
  `ghg_protocol_sector` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fuel_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fuel_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `lhv_tj_per_gg` decimal(10,2) DEFAULT NULL,
  `energy_basis_kg_co2_per_tj` decimal(10,2) DEFAULT NULL,
  `mass_basis_kg_co2_per_tonne` decimal(10,2) DEFAULT NULL,
  `density_kg_per_unit` decimal(10,2) DEFAULT NULL,
  `liquid_basis_kg_co2_per_liter` decimal(10,3) DEFAULT NULL,
  `gas_basis_kg_co2_per_m3` decimal(10,3) DEFAULT NULL,
  `ghg_protocol_subsector` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ghg_protocol_activity` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ghg_protocol_fuel` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `gas_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `region_detail` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `vintage_year` int DEFAULT NULL,
  `data_quality_rating` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `calculation_approach` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `carbon_content` decimal(10,4) DEFAULT NULL,
  `oxidation_factor` decimal(6,4) DEFAULT NULL,
  `heating_value` decimal(10,2) DEFAULT NULL,
  `heating_value_type` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `uncertainty_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `uncertainty_lower` decimal(18,8) DEFAULT NULL,
  `uncertainty_upper` decimal(18,8) DEFAULT NULL,
  `data_source_detail` text COLLATE utf8mb4_unicode_ci,
  `methodology_reference` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `info` text COLLATE utf8mb4_unicode_ci,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `is_ghg_protocol` tinyint(1) NOT NULL DEFAULT '0',
  `tier_level` int DEFAULT NULL,
  `ghg_protocol_sector_id` bigint unsigned DEFAULT NULL,
  `vehicle_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `size` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fuel` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `effective_from` date DEFAULT NULL,
  `effective_to` date DEFAULT NULL,
  `source` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `emission_factors_category_id_foreign` (`category_id`),
  KEY `emission_factors_gas_id_foreign` (`gas_id`),
  KEY `emission_factors_unit_id_foreign` (`emission_unit_id`),
  KEY `emission_factors_source_id_foreign` (`source_id`),
  KEY `emission_factors_input_unit_id_foreign` (`input_unit_id`),
  KEY `emission_factors_output_unit_id_foreign` (`output_unit_id`),
  KEY `emission_factors_ghg_protocol_sector_id_foreign` (`ghg_protocol_sector_id`),
  CONSTRAINT `emission_factors_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `emission_categories` (`id`),
  CONSTRAINT `emission_factors_gas_id_foreign` FOREIGN KEY (`gas_id`) REFERENCES `greenhouse_gases` (`id`),
  CONSTRAINT `emission_factors_ghg_protocol_sector_id_foreign` FOREIGN KEY (`ghg_protocol_sector_id`) REFERENCES `ghg_protocol_sectors` (`id`) ON DELETE SET NULL,
  CONSTRAINT `emission_factors_input_unit_id_foreign` FOREIGN KEY (`input_unit_id`) REFERENCES `measurement_units` (`id`) ON DELETE SET NULL,
  CONSTRAINT `emission_factors_output_unit_id_foreign` FOREIGN KEY (`output_unit_id`) REFERENCES `measurement_units` (`id`) ON DELETE SET NULL,
  CONSTRAINT `emission_factors_source_id_foreign` FOREIGN KEY (`source_id`) REFERENCES `emission_sources` (`id`) ON DELETE SET NULL,
  CONSTRAINT `emission_factors_unit_id_foreign` FOREIGN KEY (`emission_unit_id`) REFERENCES `measurement_units` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `emission_report`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `emission_report` (
  `emission_id` bigint unsigned NOT NULL,
  `report_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`emission_id`,`report_id`),
  KEY `emission_report_report_id_foreign` (`report_id`),
  CONSTRAINT `emission_report_emission_id_foreign` FOREIGN KEY (`emission_id`) REFERENCES `emissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `emission_report_report_id_foreign` FOREIGN KEY (`report_id`) REFERENCES `reports` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `emission_sources`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `emission_sources` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `facility_id` bigint unsigned DEFAULT NULL,
  `category_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `emission_sources_facility_id_foreign` (`facility_id`),
  KEY `emission_sources_category_id_foreign` (`category_id`),
  CONSTRAINT `emission_sources_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `emission_categories` (`id`) ON DELETE CASCADE,
  CONSTRAINT `emission_sources_facility_id_foreign` FOREIGN KEY (`facility_id`) REFERENCES `facilities` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `emission_targets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `emission_targets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `target_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `baseline_amount` decimal(18,6) DEFAULT NULL,
  `target_amount` decimal(18,6) NOT NULL,
  `emission_unit_id` bigint unsigned DEFAULT NULL,
  `baseline_year` int DEFAULT NULL,
  `target_year` int NOT NULL,
  `scope_coverage` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `scope_details` text COLLATE utf8mb4_unicode_ci,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `methodology` text COLLATE utf8mb4_unicode_ci,
  `is_validated` tinyint(1) NOT NULL DEFAULT '0',
  `validation_body` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `reduction_percentage` decimal(8,2) DEFAULT NULL,
  `base_year` int DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `is_science_based` tinyint(1) NOT NULL DEFAULT '0',
  `baseline_emissions` decimal(15,2) DEFAULT NULL,
  `target_emissions` decimal(15,2) DEFAULT NULL,
  `current_emissions` decimal(15,2) DEFAULT NULL,
  `intensity_metric` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `progress_percentage` decimal(8,2) DEFAULT NULL,
  `scope_id` bigint unsigned DEFAULT NULL,
  `scope` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `emission_targets_organization_id_foreign` (`organization_id`),
  KEY `emission_targets_scope_id_foreign` (`scope_id`),
  KEY `emission_targets_emission_unit_id_foreign` (`emission_unit_id`),
  CONSTRAINT `emission_targets_emission_unit_id_foreign` FOREIGN KEY (`emission_unit_id`) REFERENCES `measurement_units` (`id`) ON DELETE SET NULL,
  CONSTRAINT `emission_targets_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`),
  CONSTRAINT `emission_targets_scope_id_foreign` FOREIGN KEY (`scope_id`) REFERENCES `scopes` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `emissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `emissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `reporting_period_id` bigint unsigned DEFAULT NULL,
  `scope` tinyint unsigned DEFAULT NULL,
  `source` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reporting_period` date DEFAULT NULL,
  `facility_id` bigint unsigned DEFAULT NULL,
  `category_id` bigint unsigned NOT NULL,
  `emissions_value` decimal(18,6) NOT NULL,
  `emission_unit_id` bigint unsigned NOT NULL,
  `calculation_methodology` text COLLATE utf8mb4_unicode_ci,
  `calculation_method` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `scope_id` bigint unsigned DEFAULT NULL,
  `is_verified` tinyint(1) NOT NULL DEFAULT '0',
  `verification_body` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `emission_source_id` bigint unsigned DEFAULT NULL,
  `emission_factor_id` bigint unsigned DEFAULT NULL,
  `activity_data_id` bigint unsigned DEFAULT NULL,
  `gas_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Type of greenhouse gas (CO2, CH4, N2O, etc.)',
  PRIMARY KEY (`id`),
  KEY `emissions_reporting_period_id_foreign` (`reporting_period_id`),
  KEY `emissions_facility_id_foreign` (`facility_id`),
  KEY `emissions_category_id_foreign` (`category_id`),
  KEY `emissions_unit_id_foreign` (`emission_unit_id`),
  KEY `emissions_created_by_foreign` (`created_by`),
  KEY `emissions_organization_id_reporting_period_id_scope_index` (`organization_id`,`reporting_period_id`),
  KEY `emissions_emission_source_id_foreign` (`emission_source_id`),
  KEY `emissions_emission_factor_id_foreign` (`emission_factor_id`),
  KEY `emissions_activity_data_id_foreign` (`activity_data_id`),
  KEY `emissions_scope_id_foreign` (`scope_id`),
  CONSTRAINT `emissions_activity_data_id_foreign` FOREIGN KEY (`activity_data_id`) REFERENCES `activity_data` (`id`),
  CONSTRAINT `emissions_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `emission_categories` (`id`),
  CONSTRAINT `emissions_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `emissions_emission_factor_id_foreign` FOREIGN KEY (`emission_factor_id`) REFERENCES `emission_factors` (`id`),
  CONSTRAINT `emissions_emission_source_id_foreign` FOREIGN KEY (`emission_source_id`) REFERENCES `emission_sources` (`id`),
  CONSTRAINT `emissions_facility_id_foreign` FOREIGN KEY (`facility_id`) REFERENCES `facilities` (`id`),
  CONSTRAINT `emissions_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`),
  CONSTRAINT `emissions_reporting_period_id_foreign` FOREIGN KEY (`reporting_period_id`) REFERENCES `reporting_periods` (`id`),
  CONSTRAINT `emissions_scope_id_foreign` FOREIGN KEY (`scope_id`) REFERENCES `scopes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `emissions_unit_id_foreign` FOREIGN KEY (`emission_unit_id`) REFERENCES `measurement_units` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `engagement_metrics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `engagement_metrics` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `supplier_id` bigint unsigned DEFAULT NULL,
  `metric_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `metric_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `metric_value` decimal(12,4) NOT NULL,
  `metric_unit` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `metric_date` date NOT NULL,
  `reporting_period` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `metric_details` json DEFAULT NULL,
  `data_source` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `engagement_metrics_user_id_foreign` (`user_id`),
  KEY `engagement_metrics_organization_id_metric_type_metric_date_index` (`organization_id`,`metric_type`,`metric_date`),
  KEY `engagement_metrics_supplier_id_metric_type_index` (`supplier_id`,`metric_type`),
  KEY `engagement_metrics_metric_date_index` (`metric_date`),
  CONSTRAINT `engagement_metrics_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `engagement_metrics_supplier_id_foreign` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `engagement_metrics_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `erp_connections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `erp_connections` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `system_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `endpoint` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `credentials` json NOT NULL,
  `configuration` json DEFAULT NULL,
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `connected_at` timestamp NOT NULL,
  `last_sync` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `erp_connections_system_type_status_index` (`system_type`,`status`),
  KEY `erp_connections_status_index` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `external_assurances`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `external_assurances` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `compliance_report_id` bigint unsigned NOT NULL,
  `assurance_provider` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `assurance_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `assurance_standard` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `scope_of_assurance` json NOT NULL,
  `assurance_level` enum('limited','reasonable') COLLATE utf8mb4_unicode_ci NOT NULL,
  `assurance_opinion` text COLLATE utf8mb4_unicode_ci,
  `material_misstatements` json DEFAULT NULL,
  `recommendations` json DEFAULT NULL,
  `assurance_date` date NOT NULL,
  `assurance_report_reference` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `assurance_certificate_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `external_assurances_compliance_report_id_assurance_date_index` (`compliance_report_id`,`assurance_date`),
  KEY `external_assurances_assurance_provider_index` (`assurance_provider`),
  CONSTRAINT `external_assurances_compliance_report_id_foreign` FOREIGN KEY (`compliance_report_id`) REFERENCES `compliance_reports` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `facilities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `facilities` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organizational_unit_id` bigint unsigned DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `facility_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `size` decimal(15,2) DEFAULT NULL,
  `latitude` decimal(10,7) DEFAULT NULL,
  `longitude` decimal(10,7) DEFAULT NULL,
  `business_unit_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `size_unit` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `employee_count` int DEFAULT NULL,
  `city` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `state` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `postal_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `opening_date` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `facilities_business_unit_id_foreign` (`business_unit_id`),
  KEY `facilities_organizational_unit_id_foreign` (`organizational_unit_id`),
  CONSTRAINT `facilities_business_unit_id_foreign` FOREIGN KEY (`business_unit_id`) REFERENCES `organizational_units` (`id`) ON DELETE SET NULL,
  CONSTRAINT `facilities_organizational_unit_id_foreign` FOREIGN KEY (`organizational_unit_id`) REFERENCES `organizational_units` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `framework_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `framework_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `framework_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `framework_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `version` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `template_structure` json NOT NULL,
  `required_sections` json NOT NULL,
  `validation_rules` json NOT NULL,
  `mapping_rules` json DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `effective_date` date NOT NULL,
  `expiry_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `framework_templates_framework_code_unique` (`framework_code`),
  KEY `framework_templates_framework_code_is_active_index` (`framework_code`,`is_active`),
  KEY `framework_templates_effective_date_index` (`effective_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `ghg_protocol_activities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ghg_protocol_activities` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sector_id` bigint unsigned NOT NULL,
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `display_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `activity_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fuel_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `vehicle_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `equipment_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `applicable_scopes` json DEFAULT NULL,
  `calculation_methods` json DEFAULT NULL,
  `sort_order` int NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ghg_protocol_activities_code_unique` (`code`),
  KEY `ghg_protocol_activities_sector_id_activity_type_index` (`sector_id`,`activity_type`),
  KEY `ghg_protocol_activities_fuel_type_index` (`fuel_type`),
  KEY `ghg_protocol_activities_vehicle_type_index` (`vehicle_type`),
  CONSTRAINT `ghg_protocol_activities_sector_id_foreign` FOREIGN KEY (`sector_id`) REFERENCES `ghg_protocol_sectors` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `ghg_protocol_emission_factors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ghg_protocol_emission_factors` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `sector_id` bigint unsigned NOT NULL,
  `activity_id` bigint unsigned NOT NULL,
  `region_id` bigint unsigned DEFAULT NULL,
  `gas_id` bigint unsigned NOT NULL,
  `calculation_method` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `methodology_reference` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `factor_value` decimal(20,10) NOT NULL,
  `co2_factor` decimal(20,10) DEFAULT NULL,
  `ch4_factor` decimal(20,10) DEFAULT NULL,
  `n2o_factor` decimal(20,10) DEFAULT NULL,
  `carbon_content` decimal(15,8) DEFAULT NULL,
  `oxidation_factor` decimal(8,6) DEFAULT NULL,
  `heating_value` decimal(15,6) DEFAULT NULL,
  `heating_value_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `input_unit_id` bigint unsigned NOT NULL,
  `output_unit_id` bigint unsigned NOT NULL,
  `data_quality_rating` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `uncertainty_percentage` decimal(8,4) DEFAULT NULL,
  `uncertainty_lower` decimal(8,4) DEFAULT NULL,
  `uncertainty_upper` decimal(8,4) DEFAULT NULL,
  `uncertainty_type` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `vintage_year` int DEFAULT NULL,
  `valid_from` date DEFAULT NULL,
  `valid_until` date DEFAULT NULL,
  `data_source` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `data_source_detail` text COLLATE utf8mb4_unicode_ci,
  `reference_link` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `metadata` json DEFAULT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `tier_level` int DEFAULT NULL,
  `priority` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ghg_protocol_emission_factors_sector_id_foreign` (`sector_id`),
  KEY `ghg_protocol_emission_factors_activity_id_foreign` (`activity_id`),
  KEY `ghg_protocol_emission_factors_region_id_foreign` (`region_id`),
  KEY `ghg_protocol_emission_factors_gas_id_foreign` (`gas_id`),
  KEY `ghg_protocol_emission_factors_input_unit_id_foreign` (`input_unit_id`),
  KEY `ghg_protocol_emission_factors_output_unit_id_foreign` (`output_unit_id`),
  CONSTRAINT `ghg_protocol_emission_factors_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `ghg_protocol_activities` (`id`) ON DELETE CASCADE,
  CONSTRAINT `ghg_protocol_emission_factors_gas_id_foreign` FOREIGN KEY (`gas_id`) REFERENCES `greenhouse_gases` (`id`) ON DELETE CASCADE,
  CONSTRAINT `ghg_protocol_emission_factors_input_unit_id_foreign` FOREIGN KEY (`input_unit_id`) REFERENCES `measurement_units` (`id`) ON DELETE CASCADE,
  CONSTRAINT `ghg_protocol_emission_factors_output_unit_id_foreign` FOREIGN KEY (`output_unit_id`) REFERENCES `measurement_units` (`id`) ON DELETE CASCADE,
  CONSTRAINT `ghg_protocol_emission_factors_region_id_foreign` FOREIGN KEY (`region_id`) REFERENCES `ghg_protocol_regions` (`id`) ON DELETE SET NULL,
  CONSTRAINT `ghg_protocol_emission_factors_sector_id_foreign` FOREIGN KEY (`sector_id`) REFERENCES `ghg_protocol_sectors` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `ghg_protocol_regions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ghg_protocol_regions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `region_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `parent_code` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `iso_country_code` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `iso_subdivision_code` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `applicable_sectors` json DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `sort_order` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ghg_protocol_regions_code_unique` (`code`),
  KEY `ghg_protocol_regions_region_type_index` (`region_type`),
  KEY `ghg_protocol_regions_parent_code_index` (`parent_code`),
  KEY `ghg_protocol_regions_iso_country_code_index` (`iso_country_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `ghg_protocol_sectors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ghg_protocol_sectors` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `display_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `sort_order` int NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ghg_protocol_sectors_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `greenhouse_gases`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `greenhouse_gases` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `chemical_formula` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `global_warming_potential` decimal(12,2) DEFAULT NULL,
  `gwp_timeframe` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `atmospheric_lifetime` decimal(12,2) DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `source_reference` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `greenhouse_gases_chemical_formula_unique` (`chemical_formula`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `immutable_audit_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `immutable_audit_entries` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `entry_hash` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `previous_hash` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `entry_timestamp` timestamp NOT NULL,
  `entry_data` json NOT NULL,
  `entry_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `organization_id` bigint unsigned DEFAULT NULL,
  `merkle_root` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `block_number` int DEFAULT NULL,
  `signature` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `immutable_audit_entries_entry_hash_unique` (`entry_hash`),
  KEY `immutable_audit_entries_organization_id_foreign` (`organization_id`),
  KEY `immutable_audit_entries_entry_timestamp_organization_id_index` (`entry_timestamp`,`organization_id`),
  KEY `immutable_audit_entries_entry_type_index` (`entry_type`),
  KEY `immutable_audit_entries_block_number_index` (`block_number`),
  CONSTRAINT `immutable_audit_entries_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `iot_sensors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `iot_sensors` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `facility_id` bigint unsigned NOT NULL,
  `location` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `endpoint` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `api_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `protocol` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'http',
  `data_format` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'json',
  `polling_interval` int NOT NULL DEFAULT '300',
  `unit` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `calibration_factor` decimal(10,6) NOT NULL DEFAULT '1.000000',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `registered_at` timestamp NOT NULL,
  `last_reading_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `iot_sensors_facility_id_type_index` (`facility_id`,`type`),
  KEY `iot_sensors_status_type_index` (`status`,`type`),
  KEY `iot_sensors_type_index` (`type`),
  KEY `iot_sensors_facility_id_index` (`facility_id`),
  KEY `iot_sensors_status_index` (`status`),
  CONSTRAINT `iot_sensors_facility_id_foreign` FOREIGN KEY (`facility_id`) REFERENCES `facilities` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `kpi_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kpi_categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `kpi_categories_code_unique` (`code`),
  UNIQUE KEY `kpi_categories_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `kpi_targets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kpi_targets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `kpi_id` bigint unsigned NOT NULL,
  `organizational_unit_id` bigint unsigned NOT NULL,
  `organization_id` bigint unsigned NOT NULL,
  `target_value` double DEFAULT NULL,
  `baseline_value` double DEFAULT NULL,
  `baseline_year` int DEFAULT NULL,
  `target_year` int NOT NULL,
  `milestone_values` json DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_approved` tinyint(1) NOT NULL DEFAULT '0',
  `approved_by` bigint unsigned DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `kpi_targets_kpi_id_foreign` (`kpi_id`),
  KEY `kpi_targets_organizational_unit_id_foreign` (`organizational_unit_id`),
  KEY `kpi_targets_organization_id_foreign` (`organization_id`),
  KEY `kpi_targets_approved_by_foreign` (`approved_by`),
  CONSTRAINT `kpi_targets_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `kpi_targets_kpi_id_foreign` FOREIGN KEY (`kpi_id`) REFERENCES `kpis` (`id`) ON DELETE CASCADE,
  CONSTRAINT `kpi_targets_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `kpi_targets_organizational_unit_id_foreign` FOREIGN KEY (`organizational_unit_id`) REFERENCES `organizational_units` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `kpi_values`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kpi_values` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `kpi_id` bigint unsigned NOT NULL,
  `organizational_unit_id` bigint unsigned DEFAULT NULL,
  `facility_id` bigint unsigned DEFAULT NULL,
  `organization_id` bigint unsigned NOT NULL,
  `value` decimal(18,6) NOT NULL,
  `unit` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `reporting_period_id` bigint unsigned DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `is_verified` tinyint(1) NOT NULL DEFAULT '0',
  `verified_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `verified_at` timestamp NULL DEFAULT NULL,
  `source_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Polymorphic relation type (e.g., ActivityData, Emission)',
  `source_id` bigint unsigned DEFAULT NULL COMMENT 'Polymorphic relation ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `kpi_values_facility_id_foreign` (`facility_id`),
  KEY `kpi_values_organization_id_foreign` (`organization_id`),
  KEY `kpi_values_reporting_period_id_foreign` (`reporting_period_id`),
  KEY `kpi_values_kpi_id_facility_id_index` (`kpi_id`,`facility_id`),
  KEY `kpi_values_kpi_id_organization_id_index` (`kpi_id`,`organization_id`),
  KEY `kpi_values_period_start_period_end_index` (`period_start`,`period_end`),
  KEY `kpi_values_source_type_source_id_index` (`source_type`,`source_id`),
  KEY `kpi_values_organizational_unit_id_foreign` (`organizational_unit_id`),
  KEY `kpi_values_kpi_id_organizational_unit_id_index` (`kpi_id`,`organizational_unit_id`),
  CONSTRAINT `kpi_values_facility_id_foreign` FOREIGN KEY (`facility_id`) REFERENCES `facilities` (`id`) ON DELETE SET NULL,
  CONSTRAINT `kpi_values_kpi_id_foreign` FOREIGN KEY (`kpi_id`) REFERENCES `kpis` (`id`) ON DELETE CASCADE,
  CONSTRAINT `kpi_values_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `kpi_values_organizational_unit_id_foreign` FOREIGN KEY (`organizational_unit_id`) REFERENCES `organizational_units` (`id`) ON DELETE SET NULL,
  CONSTRAINT `kpi_values_reporting_period_id_foreign` FOREIGN KEY (`reporting_period_id`) REFERENCES `reporting_periods` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `kpis`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kpis` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `kpi_category_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `unit` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `formula` text COLLATE utf8mb4_unicode_ci,
  `target_value` decimal(18,6) DEFAULT NULL,
  `target_direction` enum('minimize','maximize','target') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'minimize',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `is_calculated` tinyint(1) NOT NULL DEFAULT '0',
  `display_order` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `kpis_code_unique` (`code`),
  KEY `kpis_kpi_category_id_foreign` (`kpi_category_id`),
  CONSTRAINT `kpis_kpi_category_id_foreign` FOREIGN KEY (`kpi_category_id`) REFERENCES `kpi_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `localization_strings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `localization_strings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `string_key` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
  `language_code` varchar(5) COLLATE utf8mb4_unicode_ci NOT NULL,
  `string_value` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `context` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `module_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `localization_strings_string_key_language_code_unique` (`string_key`,`language_code`),
  KEY `localization_strings_language_code_module_name_index` (`language_code`,`module_name`),
  KEY `localization_strings_context_index` (`context`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `macc_analyses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `macc_analyses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `analysis_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `target_reduction_percentage` decimal(5,2) NOT NULL,
  `timeframe_years` int NOT NULL,
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USD',
  `baseline_emissions` decimal(15,2) NOT NULL,
  `curve_data` json NOT NULL,
  `optimal_portfolio` json NOT NULL,
  `total_investment_required` decimal(15,2) NOT NULL,
  `average_cost_per_tonne` decimal(10,2) NOT NULL,
  `analysis_date` date NOT NULL,
  `status` enum('draft','approved','active') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `macc_analyses_organization_id_analysis_date_index` (`organization_id`,`analysis_date`),
  KEY `macc_analyses_status_index` (`status`),
  CONSTRAINT `macc_analyses_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `macc_option_selections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `macc_option_selections` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `macc_analysis_id` bigint unsigned NOT NULL,
  `abatement_option_id` bigint unsigned NOT NULL,
  `reduction_used` decimal(15,2) NOT NULL,
  `cost_allocated` decimal(15,2) NOT NULL,
  `cost_per_tonne` decimal(10,2) NOT NULL,
  `implementation_priority` int NOT NULL,
  `implementation_status` enum('planned','in_progress','completed','cancelled') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'planned',
  `planned_start_date` date DEFAULT NULL,
  `planned_completion_date` date DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `macc_option_unique` (`macc_analysis_id`,`abatement_option_id`),
  KEY `macc_option_selections_abatement_option_id_foreign` (`abatement_option_id`),
  KEY `macc_option_selections_implementation_priority_index` (`implementation_priority`),
  CONSTRAINT `macc_option_selections_abatement_option_id_foreign` FOREIGN KEY (`abatement_option_id`) REFERENCES `abatement_options` (`id`) ON DELETE CASCADE,
  CONSTRAINT `macc_option_selections_macc_analysis_id_foreign` FOREIGN KEY (`macc_analysis_id`) REFERENCES `macc_analyses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `measurement_units`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `measurement_units` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `symbol` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `unit_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_base_unit` tinyint(1) NOT NULL DEFAULT '0',
  `si_unit_id` bigint unsigned DEFAULT NULL,
  `conversion_factor` decimal(65,30) DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `measurement_units_symbol_unit_type_unique` (`symbol`,`unit_type`),
  KEY `measurement_units_si_unit_id_foreign` (`si_unit_id`),
  CONSTRAINT `measurement_units_si_unit_id_foreign` FOREIGN KEY (`si_unit_id`) REFERENCES `measurement_units` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `ml_models`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ml_models` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `model_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_purpose` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_version` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_framework` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_parameters` json DEFAULT NULL,
  `training_data_sources` json DEFAULT NULL,
  `feature_columns` json DEFAULT NULL,
  `target_columns` json DEFAULT NULL,
  `performance_metrics` json DEFAULT NULL,
  `model_file_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'training',
  `trained_at` timestamp NULL DEFAULT NULL,
  `deployed_at` timestamp NULL DEFAULT NULL,
  `last_prediction_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ml_models_model_type_status_index` (`model_type`,`status`),
  KEY `ml_models_model_purpose_status_index` (`model_purpose`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `ml_predictions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ml_predictions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ml_model_id` bigint unsigned NOT NULL,
  `organization_id` bigint unsigned DEFAULT NULL,
  `facility_id` bigint unsigned DEFAULT NULL,
  `prediction_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `input_data` json NOT NULL,
  `prediction_results` json NOT NULL,
  `confidence_intervals` json DEFAULT NULL,
  `confidence_score` decimal(5,4) DEFAULT NULL,
  `prediction_date` date NOT NULL,
  `forecast_start_date` date DEFAULT NULL,
  `forecast_end_date` date DEFAULT NULL,
  `scenario_parameters` json DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ml_predictions_facility_id_foreign` (`facility_id`),
  KEY `ml_predictions_ml_model_id_prediction_date_index` (`ml_model_id`,`prediction_date`),
  KEY `ml_predictions_organization_id_prediction_type_index` (`organization_id`,`prediction_type`),
  KEY `ml_predictions_prediction_type_prediction_date_index` (`prediction_type`,`prediction_date`),
  CONSTRAINT `ml_predictions_facility_id_foreign` FOREIGN KEY (`facility_id`) REFERENCES `facilities` (`id`) ON DELETE CASCADE,
  CONSTRAINT `ml_predictions_ml_model_id_foreign` FOREIGN KEY (`ml_model_id`) REFERENCES `ml_models` (`id`) ON DELETE CASCADE,
  CONSTRAINT `ml_predictions_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `model_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `model_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `model_has_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `model_has_roles` (
  `role_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `module_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `module_configurations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `module_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `configuration` json NOT NULL,
  `schema` json DEFAULT NULL,
  `is_valid` tinyint(1) NOT NULL DEFAULT '1',
  `validation_errors` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `module_configurations_module_name_unique` (`module_name`),
  KEY `module_configurations_is_valid_index` (`is_valid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `net_zero_roadmaps`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `net_zero_roadmaps` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `science_based_target_id` bigint unsigned DEFAULT NULL,
  `roadmap_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `net_zero_year` int NOT NULL DEFAULT '2050',
  `baseline_emissions` decimal(15,2) NOT NULL,
  `phase_1_details` json NOT NULL,
  `phase_2_details` json NOT NULL,
  `phase_3_details` json NOT NULL,
  `investment_requirements` json DEFAULT NULL,
  `key_technologies` json DEFAULT NULL,
  `carbon_removal_strategy` json DEFAULT NULL,
  `status` enum('draft','approved','active','achieved') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft',
  `last_updated_date` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `net_zero_roadmaps_science_based_target_id_foreign` (`science_based_target_id`),
  KEY `net_zero_roadmaps_organization_id_net_zero_year_index` (`organization_id`,`net_zero_year`),
  KEY `net_zero_roadmaps_status_index` (`status`),
  CONSTRAINT `net_zero_roadmaps_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `net_zero_roadmaps_science_based_target_id_foreign` FOREIGN KEY (`science_based_target_id`) REFERENCES `science_based_targets` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `organization_facility_links`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organization_facility_links` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `facility_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `organization_facility_links_organization_id_facility_id_unique` (`organization_id`,`facility_id`),
  KEY `organization_facility_links_facility_id_foreign` (`facility_id`),
  CONSTRAINT `organization_facility_links_facility_id_foreign` FOREIGN KEY (`facility_id`) REFERENCES `facilities` (`id`) ON DELETE CASCADE,
  CONSTRAINT `organization_facility_links_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `organizational_units`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organizational_units` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'department',
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `parent_id` bigint unsigned DEFAULT NULL,
  `organization_id` bigint unsigned DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `business_units_code_unique` (`code`),
  KEY `business_units_parent_id_foreign` (`parent_id`),
  KEY `organizational_units_organization_id_foreign` (`organization_id`),
  CONSTRAINT `business_units_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `organizational_units` (`id`) ON DELETE SET NULL,
  CONSTRAINT `organizational_units_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `organizations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `organizations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `industry` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `industry_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_person` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `website` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `regional_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `regional_configurations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `region_code` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `region_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `region_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `parent_region_code` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `iso_country_code` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `iso_subdivision_code` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `currency_code` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `language_code` varchar(5) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `timezone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `regulatory_frameworks` json DEFAULT NULL,
  `compliance_requirements` json DEFAULT NULL,
  `localization_settings` json DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `regional_configurations_region_code_unique` (`region_code`),
  KEY `regional_configurations_region_type_is_active_index` (`region_type`,`is_active`),
  KEY `regional_configurations_iso_country_code_index` (`iso_country_code`),
  KEY `regional_configurations_parent_region_code_foreign` (`parent_region_code`),
  CONSTRAINT `regional_configurations_parent_region_code_foreign` FOREIGN KEY (`parent_region_code`) REFERENCES `regional_configurations` (`region_code`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `regulatory_frameworks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `regulatory_frameworks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `framework_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `framework_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `framework_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `jurisdiction` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `applicable_regions` json DEFAULT NULL,
  `applicable_sectors` json DEFAULT NULL,
  `compliance_rules` json DEFAULT NULL,
  `reporting_requirements` json DEFAULT NULL,
  `pricing_mechanisms` json DEFAULT NULL,
  `effective_date` date DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `regulatory_frameworks_framework_code_unique` (`framework_code`),
  KEY `regulatory_frameworks_framework_type_is_active_index` (`framework_type`,`is_active`),
  KEY `regulatory_frameworks_jurisdiction_index` (`jurisdiction`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `report_approvals`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `report_approvals` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `compliance_report_id` bigint unsigned NOT NULL,
  `approval_stage` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `approval_order` int NOT NULL,
  `approver_role` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `approver_user_id` bigint unsigned DEFAULT NULL,
  `approver_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `approval_status` enum('pending','approved','rejected','delegated') COLLATE utf8mb4_unicode_ci NOT NULL,
  `approval_comments` text COLLATE utf8mb4_unicode_ci,
  `approval_deadline` timestamp NULL DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `approval_conditions` json DEFAULT NULL,
  `digital_signature` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `report_approvals_approver_user_id_foreign` (`approver_user_id`),
  KEY `report_approvals_compliance_report_id_approval_order_index` (`compliance_report_id`,`approval_order`),
  KEY `report_approvals_approval_status_approval_deadline_index` (`approval_status`,`approval_deadline`),
  CONSTRAINT `report_approvals_approver_user_id_foreign` FOREIGN KEY (`approver_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `report_approvals_compliance_report_id_foreign` FOREIGN KEY (`compliance_report_id`) REFERENCES `compliance_reports` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `report_sections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `report_sections` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `compliance_report_id` bigint unsigned NOT NULL,
  `section_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `section_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `section_order` int NOT NULL,
  `section_data` json NOT NULL,
  `data_sources` json DEFAULT NULL,
  `calculation_references` json DEFAULT NULL,
  `section_hash` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `report_sections_compliance_report_id_section_order_index` (`compliance_report_id`,`section_order`),
  KEY `report_sections_section_type_index` (`section_type`),
  CONSTRAINT `report_sections_compliance_report_id_foreign` FOREIGN KEY (`compliance_report_id`) REFERENCES `compliance_reports` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `report_validations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `report_validations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `compliance_report_id` bigint unsigned NOT NULL,
  `validation_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `validation_rule` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `validation_status` enum('passed','failed','warning','skipped') COLLATE utf8mb4_unicode_ci NOT NULL,
  `validation_message` text COLLATE utf8mb4_unicode_ci,
  `validation_details` json DEFAULT NULL,
  `validated_at` timestamp NOT NULL,
  `validated_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `report_validations_compliance_report_id_validation_status_index` (`compliance_report_id`,`validation_status`),
  KEY `report_validations_validation_type_index` (`validation_type`),
  CONSTRAINT `report_validations_compliance_report_id_foreign` FOREIGN KEY (`compliance_report_id`) REFERENCES `compliance_reports` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `reporting_periods`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `reporting_periods` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `organization_id` bigint unsigned NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'planned',
  `description` text COLLATE utf8mb4_unicode_ci,
  `is_fiscal_year` tinyint(1) NOT NULL DEFAULT '0',
  `is_baseline` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `reporting_periods_organization_id_foreign` (`organization_id`),
  CONSTRAINT `reporting_periods_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `reports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `reports` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `reporting_period_id` bigint unsigned NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `report_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `publication_date` date DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft',
  `methodology` text COLLATE utf8mb4_unicode_ci,
  `is_verified` tinyint(1) NOT NULL DEFAULT '0',
  `verification_body` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `verification_standard` text COLLATE utf8mb4_unicode_ci,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `file_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `scope_1_emissions` decimal(15,2) DEFAULT NULL,
  `scope_2_emissions` decimal(15,2) DEFAULT NULL,
  `scope_3_emissions` decimal(15,2) DEFAULT NULL,
  `total_emissions` decimal(15,2) DEFAULT NULL,
  `emission_unit` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'tCO2e',
  `reporting_standard` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `version` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1.0',
  `submitted_at` timestamp NULL DEFAULT NULL,
  `verified_at` timestamp NULL DEFAULT NULL,
  `published_at` timestamp NULL DEFAULT NULL,
  `submitted_by` bigint unsigned DEFAULT NULL,
  `generated_date` timestamp NULL DEFAULT NULL,
  `generated_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `reports_organization_id_foreign` (`organization_id`),
  KEY `reports_reporting_period_id_foreign` (`reporting_period_id`),
  KEY `reports_created_by_foreign` (`created_by`),
  KEY `reports_updated_by_foreign` (`updated_by`),
  CONSTRAINT `reports_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `reports_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`),
  CONSTRAINT `reports_reporting_period_id_foreign` FOREIGN KEY (`reporting_period_id`) REFERENCES `reporting_periods` (`id`),
  CONSTRAINT `reports_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `role_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`role_id`),
  KEY `role_has_permissions_role_id_foreign` (`role_id`),
  CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `scenario_analyses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `scenario_analyses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `analysis_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `scenario_type` enum('business_growth','merger_acquisition','facility_closure','technology_adoption','regulatory_change','market_expansion') COLLATE utf8mb4_unicode_ci NOT NULL,
  `scenario_parameters` json NOT NULL,
  `baseline_emissions` decimal(15,2) NOT NULL,
  `scenario_results` json NOT NULL,
  `comparison_analysis` json DEFAULT NULL,
  `recommendations` json DEFAULT NULL,
  `analysis_date` date NOT NULL,
  `status` enum('draft','approved','active') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `scenario_analyses_organization_id_scenario_type_index` (`organization_id`,`scenario_type`),
  KEY `scenario_analyses_analysis_date_status_index` (`analysis_date`,`status`),
  CONSTRAINT `scenario_analyses_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `scenario_projections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `scenario_projections` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `scenario_analysis_id` bigint unsigned NOT NULL,
  `scenario_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `projection_year` int NOT NULL,
  `projected_emissions` decimal(15,2) NOT NULL,
  `scope_1_emissions` decimal(15,2) DEFAULT NULL,
  `scope_2_emissions` decimal(15,2) DEFAULT NULL,
  `scope_3_emissions` decimal(15,2) DEFAULT NULL,
  `growth_factor` decimal(8,4) DEFAULT NULL,
  `efficiency_factor` decimal(8,4) DEFAULT NULL,
  `key_assumptions` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `scenario_projection_unique` (`scenario_analysis_id`,`scenario_name`,`projection_year`),
  KEY `scenario_projections_projection_year_index` (`projection_year`),
  CONSTRAINT `scenario_projections_scenario_analysis_id_foreign` FOREIGN KEY (`scenario_analysis_id`) REFERENCES `scenario_analyses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `science_based_targets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `science_based_targets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `target_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `temperature_scenario` enum('1.5C','2C','well_below_2C') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1.5C',
  `base_year` int NOT NULL,
  `target_year` int NOT NULL,
  `baseline_emissions` decimal(15,2) NOT NULL,
  `target_emissions` decimal(15,2) NOT NULL,
  `reduction_percentage` decimal(5,2) NOT NULL,
  `scope_coverage` json NOT NULL,
  `yearly_milestones` json DEFAULT NULL,
  `sector_pathway` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_sbti_validated` tinyint(1) NOT NULL DEFAULT '0',
  `sbti_validation_date` date DEFAULT NULL,
  `validation_body` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('draft','submitted','validated','active','achieved') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft',
  `methodology_details` json DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `science_based_targets_organization_id_status_index` (`organization_id`,`status`),
  KEY `science_based_targets_temperature_scenario_target_year_index` (`temperature_scenario`,`target_year`),
  CONSTRAINT `science_based_targets_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `scope3_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `scope3_categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `category_number` int NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `upstream_downstream` enum('upstream','downstream') COLLATE utf8mb4_unicode_ci NOT NULL,
  `calculation_approaches` json DEFAULT NULL,
  `data_requirements` json DEFAULT NULL,
  `typical_activities` json DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `guidance_notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `scope3_categories_category_number_unique` (`category_number`),
  KEY `scope3_categories_category_number_is_active_index` (`category_number`,`is_active`),
  KEY `scope3_categories_upstream_downstream_index` (`upstream_downstream`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `scopes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `scopes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `scopes_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sessions` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `settings_key_unique` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `stakeholder_actions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `stakeholder_actions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `organization_id` bigint unsigned NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `action_type` enum('decarbonization','data_collection','reporting','compliance','engagement','training') COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `assigned_to_user_id` bigint unsigned DEFAULT NULL,
  `assigned_to_supplier_id` bigint unsigned DEFAULT NULL,
  `assigned_to_facility_id` bigint unsigned DEFAULT NULL,
  `priority` enum('low','medium','high','critical') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'medium',
  `due_date` date NOT NULL,
  `estimated_emissions_reduction` decimal(12,2) DEFAULT NULL,
  `estimated_cost` decimal(12,2) DEFAULT NULL,
  `estimated_savings` decimal(12,2) DEFAULT NULL,
  `success_criteria` json DEFAULT NULL,
  `approval_required` tinyint(1) NOT NULL DEFAULT '0',
  `approver_user_id` bigint unsigned DEFAULT NULL,
  `status` enum('assigned','in_progress','pending_approval','approved','completed','cancelled','overdue') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'assigned',
  `progress_percentage` decimal(5,2) NOT NULL DEFAULT '0.00',
  `started_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_by_user_id` bigint unsigned NOT NULL,
  `metadata` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `stakeholder_actions_assigned_to_supplier_id_foreign` (`assigned_to_supplier_id`),
  KEY `stakeholder_actions_assigned_to_facility_id_foreign` (`assigned_to_facility_id`),
  KEY `stakeholder_actions_approver_user_id_foreign` (`approver_user_id`),
  KEY `stakeholder_actions_created_by_user_id_foreign` (`created_by_user_id`),
  KEY `stakeholder_actions_organization_id_status_index` (`organization_id`,`status`),
  KEY `stakeholder_actions_assigned_to_user_id_status_index` (`assigned_to_user_id`,`status`),
  KEY `stakeholder_actions_due_date_status_index` (`due_date`,`status`),
  KEY `stakeholder_actions_priority_status_index` (`priority`,`status`),
  CONSTRAINT `stakeholder_actions_approver_user_id_foreign` FOREIGN KEY (`approver_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `stakeholder_actions_assigned_to_facility_id_foreign` FOREIGN KEY (`assigned_to_facility_id`) REFERENCES `facilities` (`id`) ON DELETE SET NULL,
  CONSTRAINT `stakeholder_actions_assigned_to_supplier_id_foreign` FOREIGN KEY (`assigned_to_supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `stakeholder_actions_assigned_to_user_id_foreign` FOREIGN KEY (`assigned_to_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `stakeholder_actions_created_by_user_id_foreign` FOREIGN KEY (`created_by_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `stakeholder_actions_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `stakeholder_dashboards`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `stakeholder_dashboards` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `supplier_id` bigint unsigned DEFAULT NULL,
  `role_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `dashboard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `dashboard_config` json NOT NULL,
  `widget_preferences` json DEFAULT NULL,
  `filter_preferences` json DEFAULT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `last_accessed_at` timestamp NULL DEFAULT NULL,
  `access_count` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `stakeholder_dashboards_user_id_role_name_index` (`user_id`,`role_name`),
  KEY `stakeholder_dashboards_supplier_id_is_active_index` (`supplier_id`,`is_active`),
  KEY `stakeholder_dashboards_role_name_index` (`role_name`),
  CONSTRAINT `stakeholder_dashboards_supplier_id_foreign` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `stakeholder_dashboards_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `stakeholder_notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `stakeholder_notifications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `organization_id` bigint unsigned DEFAULT NULL,
  `notification_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `data` json DEFAULT NULL,
  `action_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `priority` enum('low','medium','high','urgent') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'medium',
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `read_at` timestamp NULL DEFAULT NULL,
  `email_sent` tinyint(1) NOT NULL DEFAULT '0',
  `email_sent_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `stakeholder_notifications_organization_id_foreign` (`organization_id`),
  KEY `stakeholder_notifications_user_id_is_read_index` (`user_id`,`is_read`),
  KEY `stakeholder_notifications_notification_type_created_at_index` (`notification_type`,`created_at`),
  KEY `stakeholder_notifications_expires_at_index` (`expires_at`),
  CONSTRAINT `stakeholder_notifications_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `stakeholder_notifications_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `supplier_connections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `supplier_connections` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `supplier_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `portal_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `endpoint` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `credentials` json NOT NULL,
  `data_mapping` json DEFAULT NULL,
  `sync_frequency` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'monthly',
  `last_sync` timestamp NULL DEFAULT NULL,
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `connected_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `supplier_connections_supplier_id_unique` (`supplier_id`),
  KEY `supplier_connections_portal_type_status_index` (`portal_type`,`status`),
  KEY `supplier_connections_portal_type_index` (`portal_type`),
  KEY `supplier_connections_status_index` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `supplier_data_submissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `supplier_data_submissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `supplier_id` bigint unsigned NOT NULL,
  `submitted_by_user_id` bigint unsigned NOT NULL,
  `submission_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `reporting_year` int NOT NULL,
  `reporting_period` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `submission_data` json NOT NULL,
  `data_quality_rating` enum('A','B','C','D') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `validation_results` json DEFAULT NULL,
  `status` enum('draft','submitted','under_review','approved','rejected') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft',
  `review_comments` text COLLATE utf8mb4_unicode_ci,
  `reviewed_by_user_id` bigint unsigned DEFAULT NULL,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  `attachments` json DEFAULT NULL,
  `submission_reference` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supplier_data_submissions_submitted_by_user_id_foreign` (`submitted_by_user_id`),
  KEY `supplier_data_submissions_reviewed_by_user_id_foreign` (`reviewed_by_user_id`),
  KEY `supplier_data_submissions_supplier_id_reporting_year_index` (`supplier_id`,`reporting_year`),
  KEY `supplier_data_submissions_status_created_at_index` (`status`,`created_at`),
  KEY `supplier_data_submissions_submission_type_index` (`submission_type`),
  CONSTRAINT `supplier_data_submissions_reviewed_by_user_id_foreign` FOREIGN KEY (`reviewed_by_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `supplier_data_submissions_submitted_by_user_id_foreign` FOREIGN KEY (`submitted_by_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `supplier_data_submissions_supplier_id_foreign` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `supplier_emissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `supplier_emissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `supplier_id` bigint unsigned NOT NULL,
  `scope3_category_id` bigint unsigned NOT NULL,
  `reporting_period` date NOT NULL,
  `emissions_value` decimal(15,6) NOT NULL,
  `unit` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'tCO2e',
  `data_quality` enum('A','B','C','D') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'C',
  `calculation_method` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `verification_status` enum('verified','unverified','third_party') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'unverified',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supplier_emissions_supplier_id_foreign` (`supplier_id`),
  KEY `supplier_emissions_scope3_category_id_foreign` (`scope3_category_id`),
  CONSTRAINT `supplier_emissions_scope3_category_id_foreign` FOREIGN KEY (`scope3_category_id`) REFERENCES `scope3_categories` (`id`) ON DELETE CASCADE,
  CONSTRAINT `supplier_emissions_supplier_id_foreign` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `supplier_portal_accesses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `supplier_portal_accesses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `supplier_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `access_token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `portal_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `access_level` enum('basic','standard','premium') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'standard',
  `data_submission_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `dashboard_access_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `reporting_access_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `last_login_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `access_permissions` json DEFAULT NULL,
  `dashboard_config` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `supplier_portal_accesses_access_token_unique` (`access_token`),
  KEY `supplier_portal_accesses_user_id_foreign` (`user_id`),
  KEY `supplier_portal_accesses_supplier_id_is_active_index` (`supplier_id`,`is_active`),
  KEY `supplier_portal_accesses_access_token_is_active_index` (`access_token`,`is_active`),
  KEY `supplier_portal_accesses_expires_at_index` (`expires_at`),
  CONSTRAINT `supplier_portal_accesses_supplier_id_foreign` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `supplier_portal_accesses_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `suppliers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `suppliers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `supplier_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` text COLLATE utf8mb4_unicode_ci,
  `country` varchar(2) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `region` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `industry_sector` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `supplier_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `annual_spend` decimal(15,2) DEFAULT NULL,
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USD',
  `data_quality_rating` enum('A','B','C','D') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `carbon_disclosure_level` enum('CDP','GRI','TCFD','Custom','None') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'None',
  `sustainability_certifications` json DEFAULT NULL,
  `scope3_categories` json DEFAULT NULL,
  `primary_products_services` json DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `suppliers_supplier_code_unique` (`supplier_code`),
  KEY `suppliers_industry_sector_is_active_index` (`industry_sector`,`is_active`),
  KEY `suppliers_country_region_index` (`country`,`region`),
  KEY `suppliers_data_quality_rating_index` (`data_quality_rating`),
  KEY `suppliers_carbon_disclosure_level_index` (`carbon_disclosure_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `system_modules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `system_modules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `module_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `module_class` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `module_version` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `module_description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `module_dependencies` json DEFAULT NULL,
  `is_core_module` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `configuration` json DEFAULT NULL,
  `installed_at` timestamp NULL DEFAULT NULL,
  `activated_at` timestamp NULL DEFAULT NULL,
  `deactivated_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `system_modules_module_name_unique` (`module_name`),
  KEY `system_modules_is_active_is_core_module_index` (`is_active`,`is_core_module`),
  KEY `system_modules_module_name_index` (`module_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `units`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `units` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `workspace_participants`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workspace_participants` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `collaboration_workspace_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `supplier_id` bigint unsigned DEFAULT NULL,
  `participant_role` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `permissions` json DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `joined_at` timestamp NOT NULL DEFAULT '2025-06-13 17:51:02',
  `last_activity_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `workspace_participants_workspace_active_idx` (`collaboration_workspace_id`,`is_active`),
  KEY `workspace_participants_user_id_is_active_index` (`user_id`,`is_active`),
  KEY `workspace_participants_supplier_id_is_active_index` (`supplier_id`,`is_active`),
  CONSTRAINT `workspace_participants_collaboration_workspace_id_foreign` FOREIGN KEY (`collaboration_workspace_id`) REFERENCES `collaboration_workspaces` (`id`) ON DELETE CASCADE,
  CONSTRAINT `workspace_participants_supplier_id_foreign` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `workspace_participants_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50001 DROP VIEW IF EXISTS `business_units`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `business_units` AS select `organizational_units`.`id` AS `id`,`organizational_units`.`name` AS `name`,`organizational_units`.`type` AS `type`,`organizational_units`.`code` AS `code`,`organizational_units`.`description` AS `description`,`organizational_units`.`parent_id` AS `parent_id`,`organizational_units`.`organization_id` AS `organization_id`,`organizational_units`.`is_active` AS `is_active`,`organizational_units`.`created_at` AS `created_at`,`organizational_units`.`updated_at` AS `updated_at` from `organizational_units` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (55,'2025_04_30_004531_create_cache_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (56,'2025_04_30_140030_add_user_fields_to_reports_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (57,'2025_04_30_165051_update_emission_targets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (58,'2025_04_30_195125_update_activity_data_add_emission_factor',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (59,'2025_04_30_add_amount_tons_co2e_to_carbon_offsets',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (60,'2025_04_30_add_columns_to_reports',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (61,'2025_04_30_add_industry_type_to_organizations',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (62,'2025_04_30_add_is_active_to_facilities_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (63,'2025_04_30_add_is_active_to_reporting_periods',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (64,'2025_04_30_add_is_base_unit_to_measurement_units',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (65,'2025_04_30_add_location_fields_to_facilities',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (66,'2025_04_30_add_missing_columns_to_emission_factors',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (67,'2025_04_30_add_organization_id_to_activity_data',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (68,'2025_04_30_add_region_to_emission_factors',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (69,'2025_01_15_000001_create_ghg_protocol_activities_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (70,'2025_01_15_000002_create_ghg_protocol_regions_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (71,'2025_01_15_000004_create_emission_factor_variants_table',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (72,'2025_01_15_000005_enhance_emission_factor_units_table',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (73,'2025_01_15_000010_add_lb_co2e_measurement_unit',6);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (75,'2025_06_13_032730_add_ghg_protocol_fields_to_activity_data_table',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (76,'2025_06_13_040000_create_data_lake_tables',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (77,'2025_06_13_051000_add_enhanced_scope_fields_to_activity_data',9);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (78,'2025_06_13_070001_create_missing_audit_reporting_tables',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (79,'2025_06_13_080000_create_collaboration_stakeholder_tables',11);
