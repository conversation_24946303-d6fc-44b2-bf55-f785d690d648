<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class Scope3CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            // Upstream Categories (1-8)
            [
                'category_number' => 1,
                'name' => 'Purchased Goods and Services',
                'description' => 'Extraction, production, and transportation of goods and services purchased or acquired by the reporting company in the reporting year, not otherwise included in categories 2-8.',
                'upstream_downstream' => 'upstream',
                'calculation_approaches' => ['supplier_specific', 'activity_based', 'spend_based', 'hybrid'],
                'data_requirements' => ['supplier_data', 'spend_data', 'activity_data'],
                'typical_activities' => ['Raw materials', 'Office supplies', 'IT equipment', 'Professional services'],
                'guidance_notes' => 'Often the largest category for many companies. Prioritize by spend and emission intensity.',
            ],
            [
                'category_number' => 2,
                'name' => 'Capital Goods',
                'description' => 'Extraction, production, and transportation of capital goods purchased or acquired by the reporting company in the reporting year.',
                'upstream_downstream' => 'upstream',
                'calculation_approaches' => ['supplier_specific', 'activity_based', 'spend_based'],
                'data_requirements' => ['capital_expenditure', 'asset_data', 'supplier_data'],
                'typical_activities' => ['Buildings', 'Machinery', 'Equipment', 'Vehicles', 'IT infrastructure'],
                'guidance_notes' => 'Include all capital goods purchased in the reporting year, regardless of useful life.',
            ],
            [
                'category_number' => 3,
                'name' => 'Fuel- and Energy-Related Activities',
                'description' => 'Extraction, production, and transportation of fuels and energy purchased or acquired by the reporting company in the reporting year, not included in scope 1 or scope 2.',
                'upstream_downstream' => 'upstream',
                'calculation_approaches' => ['activity_based', 'spend_based'],
                'data_requirements' => ['fuel_consumption', 'electricity_consumption', 'energy_spend'],
                'typical_activities' => ['Upstream fuel emissions', 'Transmission and distribution losses', 'Energy generation'],
                'guidance_notes' => 'Covers well-to-tank emissions and T&D losses not included in Scope 1 and 2.',
            ],
            [
                'category_number' => 4,
                'name' => 'Upstream Transportation and Distribution',
                'description' => 'Transportation and distribution of products purchased by the reporting company in the reporting year between tier 1 suppliers and the reporting company\'s own operations.',
                'upstream_downstream' => 'upstream',
                'calculation_approaches' => ['supplier_specific', 'activity_based', 'spend_based'],
                'data_requirements' => ['transportation_data', 'logistics_spend', 'supplier_data'],
                'typical_activities' => ['Inbound logistics', 'Third-party transportation', 'Warehousing', 'Distribution'],
                'guidance_notes' => 'Include transportation not paid for by the reporting company but included in product price.',
            ],
            [
                'category_number' => 5,
                'name' => 'Waste Generated in Operations',
                'description' => 'Disposal and treatment of waste generated in the reporting company\'s operations in the reporting year.',
                'upstream_downstream' => 'upstream',
                'calculation_approaches' => ['activity_based', 'spend_based'],
                'data_requirements' => ['waste_data', 'waste_disposal_costs'],
                'typical_activities' => ['Landfill disposal', 'Incineration', 'Recycling', 'Composting', 'Wastewater treatment'],
                'guidance_notes' => 'Include all waste disposal methods and treatment processes.',
            ],
            [
                'category_number' => 6,
                'name' => 'Business Travel',
                'description' => 'Transportation of employees for business-related activities in vehicles owned or operated by third parties.',
                'upstream_downstream' => 'upstream',
                'calculation_approaches' => ['activity_based', 'spend_based'],
                'data_requirements' => ['travel_data', 'travel_expenses'],
                'typical_activities' => ['Air travel', 'Rail travel', 'Car rental', 'Hotel stays', 'Taxi/rideshare'],
                'guidance_notes' => 'Include all business travel regardless of who pays for it.',
            ],
            [
                'category_number' => 7,
                'name' => 'Employee Commuting',
                'description' => 'Transportation of employees between their homes and their worksites.',
                'upstream_downstream' => 'upstream',
                'calculation_approaches' => ['activity_based', 'spend_based', 'survey_based'],
                'data_requirements' => ['commuting_surveys', 'employee_data', 'transportation_allowances'],
                'typical_activities' => ['Personal vehicle commuting', 'Public transportation', 'Working from home'],
                'guidance_notes' => 'Often estimated using employee surveys and regional transportation data.',
            ],
            [
                'category_number' => 8,
                'name' => 'Upstream Leased Assets',
                'description' => 'Operation of assets leased by the reporting company (lessee) and not included in the reporting company\'s scope 1 and scope 2 inventories.',
                'upstream_downstream' => 'upstream',
                'calculation_approaches' => ['activity_based', 'spend_based'],
                'data_requirements' => ['leased_asset_data', 'lease_agreements'],
                'typical_activities' => ['Leased buildings', 'Leased vehicles', 'Leased equipment'],
                'guidance_notes' => 'Only include if not already captured in Scope 1 and 2.',
            ],

            // Downstream Categories (9-15)
            [
                'category_number' => 9,
                'name' => 'Downstream Transportation and Distribution',
                'description' => 'Transportation and distribution of products sold by the reporting company in the reporting year between the reporting company\'s operations and the end consumer.',
                'upstream_downstream' => 'downstream',
                'calculation_approaches' => ['activity_based', 'spend_based'],
                'data_requirements' => ['distribution_data', 'logistics_costs', 'product_sales'],
                'typical_activities' => ['Outbound logistics', 'Retail distribution', 'Customer delivery'],
                'guidance_notes' => 'Include transportation and distribution not included in Scope 1 and 2.',
            ],
            [
                'category_number' => 10,
                'name' => 'Processing of Sold Products',
                'description' => 'Processing of intermediate products sold by the reporting company in the reporting year.',
                'upstream_downstream' => 'downstream',
                'calculation_approaches' => ['activity_based', 'spend_based'],
                'data_requirements' => ['product_data', 'processing_requirements'],
                'typical_activities' => ['Further manufacturing', 'Assembly', 'Processing'],
                'guidance_notes' => 'Only applicable for companies selling intermediate products.',
            ],
            [
                'category_number' => 11,
                'name' => 'Use of Sold Products',
                'description' => 'End use of goods and services sold by the reporting company in the reporting year.',
                'upstream_downstream' => 'downstream',
                'calculation_approaches' => ['activity_based', 'spend_based'],
                'data_requirements' => ['product_usage_data', 'energy_consumption_patterns'],
                'typical_activities' => ['Product operation', 'Energy consumption', 'Fuel consumption'],
                'guidance_notes' => 'Often significant for energy-consuming products and fuel companies.',
            ],
            [
                'category_number' => 12,
                'name' => 'End-of-Life Treatment of Sold Products',
                'description' => 'Waste disposal and treatment of products sold by the reporting company at the end of their life.',
                'upstream_downstream' => 'downstream',
                'calculation_approaches' => ['activity_based', 'spend_based'],
                'data_requirements' => ['product_disposal_data', 'waste_treatment_methods'],
                'typical_activities' => ['Landfill disposal', 'Incineration', 'Recycling', 'Composting'],
                'guidance_notes' => 'Consider typical disposal methods for products in different regions.',
            ],
            [
                'category_number' => 13,
                'name' => 'Downstream Leased Assets',
                'description' => 'Operation of assets owned by the reporting company (lessor) and leased to other entities.',
                'upstream_downstream' => 'downstream',
                'calculation_approaches' => ['activity_based', 'spend_based'],
                'data_requirements' => ['leased_asset_data', 'tenant_energy_usage'],
                'typical_activities' => ['Leased real estate', 'Leased equipment', 'Leased vehicles'],
                'guidance_notes' => 'Include energy consumption by lessees in company-owned assets.',
            ],
            [
                'category_number' => 14,
                'name' => 'Franchises',
                'description' => 'Operation of franchises in the reporting year.',
                'upstream_downstream' => 'downstream',
                'calculation_approaches' => ['activity_based', 'spend_based'],
                'data_requirements' => ['franchise_data', 'franchisee_operations'],
                'typical_activities' => ['Franchise operations', 'Franchisee energy use', 'Franchise transportation'],
                'guidance_notes' => 'Include all franchise operations not included in Scope 1 and 2.',
            ],
            [
                'category_number' => 15,
                'name' => 'Investments',
                'description' => 'Operation of investments (including equity and debt investments and project finance) in the reporting year.',
                'upstream_downstream' => 'downstream',
                'calculation_approaches' => ['activity_based', 'spend_based', 'pcaf'],
                'data_requirements' => ['investment_data', 'portfolio_emissions', 'financial_data'],
                'typical_activities' => ['Equity investments', 'Debt investments', 'Project finance', 'Managed assets'],
                'guidance_notes' => 'Use PCAF methodology for financial institutions. Include proportional share of emissions.',
            ],
        ];

        foreach ($categories as $category) {
            DB::table('scope3_categories')->insert([
                'category_number' => $category['category_number'],
                'name' => $category['name'],
                'description' => $category['description'],
                'upstream_downstream' => $category['upstream_downstream'],
                'calculation_approaches' => json_encode($category['calculation_approaches']),
                'data_requirements' => json_encode($category['data_requirements']),
                'typical_activities' => json_encode($category['typical_activities']),
                'guidance_notes' => $category['guidance_notes'],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
