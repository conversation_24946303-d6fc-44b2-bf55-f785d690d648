<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\EmissionFactorVariant;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\GreenhouseGas;
use App\Models\MeasurementUnit;

class GhgProtocolEmissionFactorsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedStationaryCombustionFactors();
        $this->seedElectricityFactors();
        $this->seedMobileCombustionFactors();
    }

    /**
     * Seed stationary combustion emission factors
     */
    protected function seedStationaryCombustionFactors(): void
    {
        $sector = GhgProtocolSector::where('name', 'stationary_combustion')->first();
        $ngActivity = GhgProtocolActivity::where('code', 'STAT_COMB_NG')->first();
        $co2Gas = GreenhouseGas::where('chemical_formula', 'CO₂')->first();
        $ch4Gas = GreenhouseGas::where('chemical_formula', 'CH₄')->first();
        $n2oGas = GreenhouseGas::where('chemical_formula', 'N₂O')->first();
        
        $gjUnit = MeasurementUnit::where('symbol', 'GJ')->first();
        $kgCo2eUnit = MeasurementUnit::where('symbol', 'kg CO₂e')->first();
        
        $globalRegion = GhgProtocolRegion::where('code', 'GLOBAL')->first();
        $usRegion = GhgProtocolRegion::where('code', 'US')->first();

        if (!$sector || !$ngActivity || !$co2Gas || !$gjUnit || !$kgCo2eUnit) {
            $this->command->warn('Missing required data for stationary combustion factors');
            return;
        }

        // Natural Gas CO2 Factor - Global
        $ngCo2Factor = GhgProtocolEmissionFactor::updateOrCreate(
            ['code' => 'STAT_NG_CO2_GLOBAL'],
            [
                'name' => 'Natural Gas CO2 Emission Factor (Global)',
                'description' => 'Global default CO2 emission factor for natural gas combustion',
                'sector_id' => $sector->id,
                'activity_id' => $ngActivity->id,
                'region_id' => $globalRegion?->id,
                'gas_id' => $co2Gas->id,
                'calculation_method' => 'tier1',
                'methodology_reference' => '2006 IPCC Guidelines',
                'factor_value' => 56.1, // kg CO2/GJ
                'carbon_content' => 15.3, // kg C/GJ
                'oxidation_factor' => 0.995,
                'heating_value' => 38.3, // MJ/m3
                'heating_value_type' => 'net',
                'input_unit_id' => $gjUnit->id,
                'output_unit_id' => $kgCo2eUnit->id,
                'data_quality_rating' => 'A',
                'uncertainty_percentage' => 5.0,
                'vintage_year' => 2023,
                'valid_from' => '2023-01-01',
                'data_source' => 'GHG Protocol',
                'is_default' => true,
                'is_active' => true,
                'tier_level' => 1,
                'priority' => 100,
            ]
        );

        // Natural Gas CO2 Factor - US Variant
        if ($usRegion) {
            EmissionFactorVariant::updateOrCreate(
                [
                    'base_factor_id' => $ngCo2Factor->id,
                    'variant_code' => 'US_SPECIFIC'
                ],
                [
                    'variant_type' => 'regional',
                    'variant_name' => 'US Specific Natural Gas CO2',
                    'variant_description' => 'US-specific natural gas CO2 emission factor',
                    'region_id' => $usRegion->id,
                    'factor_value' => 53.06, // EPA value
                    'carbon_content' => 14.46,
                    'oxidation_factor' => 0.995,
                    'data_quality_rating' => 'A',
                    'uncertainty_percentage' => 2.0,
                    'vintage_year' => 2023,
                    'valid_from' => '2023-01-01',
                    'notes' => 'EPA GHG Inventory 2023',
                    'priority' => 200,
                    'is_active' => true,
                ]
            );
        }

        // Natural Gas CH4 Factor
        if ($ch4Gas) {
            GhgProtocolEmissionFactor::updateOrCreate(
                ['code' => 'STAT_NG_CH4_GLOBAL'],
                [
                    'name' => 'Natural Gas CH4 Emission Factor (Global)',
                    'description' => 'Global default CH4 emission factor for natural gas combustion',
                    'sector_id' => $sector->id,
                    'activity_id' => $ngActivity->id,
                    'region_id' => $globalRegion?->id,
                    'gas_id' => $ch4Gas->id,
                    'calculation_method' => 'tier1',
                    'methodology_reference' => '2006 IPCC Guidelines',
                    'factor_value' => 0.001, // kg CH4/GJ
                    'input_unit_id' => $gjUnit->id,
                    'output_unit_id' => $kgCo2eUnit->id,
                    'data_quality_rating' => 'B',
                    'uncertainty_percentage' => 50.0,
                    'vintage_year' => 2023,
                    'valid_from' => '2023-01-01',
                    'data_source' => 'GHG Protocol',
                    'is_default' => true,
                    'is_active' => true,
                    'tier_level' => 1,
                    'priority' => 100,
                ]
            );
        }

        // Natural Gas N2O Factor
        if ($n2oGas) {
            GhgProtocolEmissionFactor::updateOrCreate(
                ['code' => 'STAT_NG_N2O_GLOBAL'],
                [
                    'name' => 'Natural Gas N2O Emission Factor (Global)',
                    'description' => 'Global default N2O emission factor for natural gas combustion',
                    'sector_id' => $sector->id,
                    'activity_id' => $ngActivity->id,
                    'region_id' => $globalRegion?->id,
                    'gas_id' => $n2oGas->id,
                    'calculation_method' => 'tier1',
                    'methodology_reference' => '2006 IPCC Guidelines',
                    'factor_value' => 0.0001, // kg N2O/GJ
                    'input_unit_id' => $gjUnit->id,
                    'output_unit_id' => $kgCo2eUnit->id,
                    'data_quality_rating' => 'C',
                    'uncertainty_percentage' => 100.0,
                    'vintage_year' => 2023,
                    'valid_from' => '2023-01-01',
                    'data_source' => 'GHG Protocol',
                    'is_default' => true,
                    'is_active' => true,
                    'tier_level' => 1,
                    'priority' => 100,
                ]
            );
        }
    }

    /**
     * Seed electricity emission factors
     */
    protected function seedElectricityFactors(): void
    {
        $sector = GhgProtocolSector::where('name', 'electricity')->first();
        $elecActivity = GhgProtocolActivity::where('code', 'ELEC_GRID')->first();
        $co2Gas = GreenhouseGas::where('chemical_formula', 'CO₂')->first();
        
        $kwhUnit = MeasurementUnit::where('symbol', 'kWh')->first();
        $kgCo2eUnit = MeasurementUnit::where('symbol', 'kg CO₂e')->first();
        
        $globalRegion = GhgProtocolRegion::where('code', 'GLOBAL')->first();
        $usRegion = GhgProtocolRegion::where('code', 'US')->first();
        $weccRegion = GhgProtocolRegion::where('code', 'WECC')->first();

        if (!$sector || !$elecActivity || !$co2Gas || !$kwhUnit || !$kgCo2eUnit) {
            $this->command->warn('Missing required data for electricity factors');
            return;
        }

        // Global Electricity Factor
        $globalElecFactor = GhgProtocolEmissionFactor::updateOrCreate(
            ['code' => 'ELEC_GRID_CO2_GLOBAL'],
            [
                'name' => 'Grid Electricity CO2 Factor (Global)',
                'description' => 'Global average grid electricity CO2 emission factor',
                'sector_id' => $sector->id,
                'activity_id' => $elecActivity->id,
                'region_id' => $globalRegion?->id,
                'gas_id' => $co2Gas->id,
                'calculation_method' => 'location_based',
                'methodology_reference' => 'GHG Protocol Scope 2 Guidance',
                'factor_value' => 0.709, // kg CO2/kWh (IEA global average)
                'input_unit_id' => $kwhUnit->id,
                'output_unit_id' => $kgCo2eUnit->id,
                'data_quality_rating' => 'B',
                'uncertainty_percentage' => 20.0,
                'vintage_year' => 2023,
                'valid_from' => '2023-01-01',
                'data_source' => 'IEA Emissions Factors',
                'is_default' => true,
                'is_active' => true,
                'tier_level' => 1,
                'priority' => 100,
            ]
        );

        // US National Average
        if ($usRegion) {
            EmissionFactorVariant::updateOrCreate(
                [
                    'base_factor_id' => $globalElecFactor->id,
                    'variant_code' => 'US_NATIONAL'
                ],
                [
                    'variant_type' => 'regional',
                    'variant_name' => 'US National Grid Average',
                    'variant_description' => 'US national average grid electricity emission factor',
                    'region_id' => $usRegion->id,
                    'factor_value' => 0.386, // kg CO2/kWh (EPA eGRID)
                    'data_quality_rating' => 'A',
                    'uncertainty_percentage' => 10.0,
                    'vintage_year' => 2023,
                    'valid_from' => '2023-01-01',
                    'notes' => 'EPA eGRID 2023',
                    'priority' => 200,
                    'is_active' => true,
                ]
            );
        }

        // WECC Regional Factor
        if ($weccRegion) {
            EmissionFactorVariant::updateOrCreate(
                [
                    'base_factor_id' => $globalElecFactor->id,
                    'variant_code' => 'WECC_REGIONAL'
                ],
                [
                    'variant_type' => 'regional',
                    'variant_name' => 'WECC Grid Region',
                    'variant_description' => 'Western Electricity Coordinating Council grid emission factor',
                    'region_id' => $weccRegion->id,
                    'factor_value' => 0.313, // kg CO2/kWh
                    'data_quality_rating' => 'A',
                    'uncertainty_percentage' => 8.0,
                    'vintage_year' => 2023,
                    'valid_from' => '2023-01-01',
                    'notes' => 'EPA eGRID WECC region 2023',
                    'priority' => 300,
                    'is_active' => true,
                ]
            );
        }
    }

    /**
     * Seed mobile combustion emission factors
     */
    protected function seedMobileCombustionFactors(): void
    {
        $sector = GhgProtocolSector::where('name', 'mobile_combustion')->first();
        $gasActivity = GhgProtocolActivity::where('code', 'MOB_COMB_GASOLINE')->first();
        $co2Gas = GreenhouseGas::where('chemical_formula', 'CO₂')->first();
        
        $literUnit = MeasurementUnit::where('symbol', 'L')->first();
        $kgCo2eUnit = MeasurementUnit::where('symbol', 'kg CO₂e')->first();
        
        $globalRegion = GhgProtocolRegion::where('code', 'GLOBAL')->first();

        if (!$sector || !$gasActivity || !$co2Gas || !$literUnit || !$kgCo2eUnit) {
            $this->command->warn('Missing required data for mobile combustion factors');
            return;
        }

        // Gasoline CO2 Factor
        GhgProtocolEmissionFactor::updateOrCreate(
            ['code' => 'MOB_GASOLINE_CO2_GLOBAL'],
            [
                'name' => 'Gasoline CO2 Emission Factor (Global)',
                'description' => 'Global default CO2 emission factor for gasoline combustion',
                'sector_id' => $sector->id,
                'activity_id' => $gasActivity->id,
                'region_id' => $globalRegion?->id,
                'gas_id' => $co2Gas->id,
                'calculation_method' => 'tier1',
                'methodology_reference' => '2006 IPCC Guidelines',
                'factor_value' => 2.31, // kg CO2/L
                'carbon_content' => 18.9, // kg C/GJ
                'oxidation_factor' => 0.99,
                'heating_value' => 32.2, // MJ/L
                'heating_value_type' => 'net',
                'input_unit_id' => $literUnit->id,
                'output_unit_id' => $kgCo2eUnit->id,
                'data_quality_rating' => 'A',
                'uncertainty_percentage' => 5.0,
                'vintage_year' => 2023,
                'valid_from' => '2023-01-01',
                'data_source' => 'GHG Protocol',
                'is_default' => true,
                'is_active' => true,
                'tier_level' => 1,
                'priority' => 100,
            ]
        );
    }
}
