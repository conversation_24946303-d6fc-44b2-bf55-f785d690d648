<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;

class GhgProtocolStructureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedSectors();
        $this->seedRegions();
        $this->seedActivities();
    }

    /**
     * Seed GHG Protocol sectors
     */
    protected function seedSectors(): void
    {
        $sectors = [
            [
                'name' => 'stationary_combustion',
                'display_name' => 'Stationary Combustion',
                'description' => 'Emissions from stationary fuel combustion sources',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'mobile_combustion',
                'display_name' => 'Mobile Combustion',
                'description' => 'Emissions from mobile fuel combustion sources',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'electricity',
                'display_name' => 'Electricity',
                'description' => 'Emissions from purchased electricity consumption',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'freight',
                'display_name' => 'Freight and Logistics',
                'description' => 'Emissions from freight transportation and logistics',
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'name' => 'business_travel',
                'display_name' => 'Business Travel',
                'description' => 'Emissions from business travel activities',
                'sort_order' => 5,
                'is_active' => true,
            ],
            [
                'name' => 'employee_commuting',
                'display_name' => 'Employee Commuting',
                'description' => 'Emissions from employee commuting',
                'sort_order' => 6,
                'is_active' => true,
            ],
        ];

        foreach ($sectors as $sectorData) {
            GhgProtocolSector::updateOrCreate(
                ['name' => $sectorData['name']],
                $sectorData
            );
        }
    }

    /**
     * Seed GHG Protocol regions
     */
    protected function seedRegions(): void
    {
        $regions = [
            // Global/Default
            [
                'code' => 'GLOBAL',
                'name' => 'Global',
                'region_type' => 'global',
                'description' => 'Global default factors',
                'is_active' => true,
                'sort_order' => 0,
            ],
            
            // Countries
            [
                'code' => 'US',
                'name' => 'United States',
                'region_type' => 'country',
                'iso_country_code' => 'USA',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'code' => 'CA',
                'name' => 'Canada',
                'region_type' => 'country',
                'iso_country_code' => 'CAN',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'code' => 'EU',
                'name' => 'European Union',
                'region_type' => 'economic_region',
                'description' => 'European Union member states',
                'is_active' => true,
                'sort_order' => 3,
            ],
            
            // US States (examples)
            [
                'code' => 'US-CA',
                'name' => 'California',
                'region_type' => 'state',
                'parent_code' => 'US',
                'iso_country_code' => 'USA',
                'iso_subdivision_code' => 'US-CA',
                'is_active' => true,
                'sort_order' => 10,
            ],
            [
                'code' => 'US-TX',
                'name' => 'Texas',
                'region_type' => 'state',
                'parent_code' => 'US',
                'iso_country_code' => 'USA',
                'iso_subdivision_code' => 'US-TX',
                'is_active' => true,
                'sort_order' => 11,
            ],
            
            // Grid Regions (examples)
            [
                'code' => 'WECC',
                'name' => 'Western Electricity Coordinating Council',
                'region_type' => 'grid_region',
                'parent_code' => 'US',
                'description' => 'Western US electricity grid region',
                'applicable_sectors' => ['electricity'],
                'is_active' => true,
                'sort_order' => 20,
            ],
            [
                'code' => 'ERCOT',
                'name' => 'Electric Reliability Council of Texas',
                'region_type' => 'grid_region',
                'parent_code' => 'US-TX',
                'description' => 'Texas electricity grid region',
                'applicable_sectors' => ['electricity'],
                'is_active' => true,
                'sort_order' => 21,
            ],
        ];

        foreach ($regions as $regionData) {
            GhgProtocolRegion::updateOrCreate(
                ['code' => $regionData['code']],
                $regionData
            );
        }
    }

    /**
     * Seed GHG Protocol activities
     */
    protected function seedActivities(): void
    {
        $activities = [
            // Stationary Combustion
            [
                'sector_name' => 'stationary_combustion',
                'code' => 'STAT_COMB_NG',
                'name' => 'Natural Gas Combustion',
                'activity_type' => 'combustion',
                'fuel_type' => 'natural_gas',
                'applicable_scopes' => [1],
                'calculation_methods' => ['tier1', 'tier2', 'tier3'],
                'sort_order' => 1,
            ],
            [
                'sector_name' => 'stationary_combustion',
                'code' => 'STAT_COMB_COAL',
                'name' => 'Coal Combustion',
                'activity_type' => 'combustion',
                'fuel_type' => 'coal',
                'applicable_scopes' => [1],
                'calculation_methods' => ['tier1', 'tier2', 'tier3'],
                'sort_order' => 2,
            ],
            [
                'sector_name' => 'stationary_combustion',
                'code' => 'STAT_COMB_DIESEL',
                'name' => 'Diesel Combustion',
                'activity_type' => 'combustion',
                'fuel_type' => 'diesel',
                'applicable_scopes' => [1],
                'calculation_methods' => ['tier1', 'tier2', 'tier3'],
                'sort_order' => 3,
            ],
            
            // Mobile Combustion
            [
                'sector_name' => 'mobile_combustion',
                'code' => 'MOB_COMB_GASOLINE',
                'name' => 'Gasoline Vehicle Combustion',
                'activity_type' => 'combustion',
                'fuel_type' => 'gasoline',
                'vehicle_type' => 'passenger_car',
                'applicable_scopes' => [1],
                'calculation_methods' => ['fuel_based', 'distance_based'],
                'sort_order' => 1,
            ],
            [
                'sector_name' => 'mobile_combustion',
                'code' => 'MOB_COMB_DIESEL_TRUCK',
                'name' => 'Diesel Truck Combustion',
                'activity_type' => 'combustion',
                'fuel_type' => 'diesel',
                'vehicle_type' => 'heavy_duty_truck',
                'applicable_scopes' => [1],
                'calculation_methods' => ['fuel_based', 'distance_based'],
                'sort_order' => 2,
            ],
            
            // Electricity
            [
                'sector_name' => 'electricity',
                'code' => 'ELEC_GRID',
                'name' => 'Grid Electricity Consumption',
                'activity_type' => 'electricity',
                'applicable_scopes' => [2],
                'calculation_methods' => ['location_based', 'market_based'],
                'sort_order' => 1,
            ],
            
            // Freight
            [
                'sector_name' => 'freight',
                'code' => 'FREIGHT_TRUCK',
                'name' => 'Truck Freight',
                'activity_type' => 'transport',
                'vehicle_type' => 'freight_truck',
                'applicable_scopes' => [3],
                'calculation_methods' => ['distance_based', 'spend_based'],
                'sort_order' => 1,
            ],
            [
                'sector_name' => 'freight',
                'code' => 'FREIGHT_RAIL',
                'name' => 'Rail Freight',
                'activity_type' => 'transport',
                'vehicle_type' => 'freight_rail',
                'applicable_scopes' => [3],
                'calculation_methods' => ['distance_based', 'spend_based'],
                'sort_order' => 2,
            ],
            
            // Business Travel
            [
                'sector_name' => 'business_travel',
                'code' => 'BT_AIR',
                'name' => 'Air Travel',
                'activity_type' => 'transport',
                'vehicle_type' => 'aircraft',
                'applicable_scopes' => [3],
                'calculation_methods' => ['distance_based', 'spend_based'],
                'sort_order' => 1,
            ],
            [
                'sector_name' => 'business_travel',
                'code' => 'BT_RENTAL_CAR',
                'name' => 'Rental Car',
                'activity_type' => 'transport',
                'vehicle_type' => 'passenger_car',
                'applicable_scopes' => [3],
                'calculation_methods' => ['fuel_based', 'distance_based'],
                'sort_order' => 2,
            ],
        ];

        foreach ($activities as $activityData) {
            $sector = GhgProtocolSector::where('name', $activityData['sector_name'])->first();
            
            if ($sector) {
                $activityData['sector_id'] = $sector->id;
                unset($activityData['sector_name']);
                
                GhgProtocolActivity::updateOrCreate(
                    ['code' => $activityData['code']],
                    $activityData
                );
            }
        }
    }
}
