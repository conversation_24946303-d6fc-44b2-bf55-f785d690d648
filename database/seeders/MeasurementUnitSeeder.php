<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MeasurementUnit;
use Illuminate\Support\Facades\Log;

class MeasurementUnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $kWh = MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'kWh',
                'unit_type' => 'Energy',
            ],
            [
                'name' => 'Kilowatt-hour',
                'is_base_unit' => true,
                'conversion_factor' => 1,
                'description' => 'Standard unit for electricity consumption',
                'is_active' => true,
            ]
        );
        Log::info('Ensured record for kWh-Energy exists or is updated.');

        $liter = MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'L',
                'unit_type' => 'Volume',
            ],
            [
                'name' => 'Liter',
                'is_base_unit' => true,
                'conversion_factor' => 1,
                'description' => 'Standard unit for volume',
                'is_active' => true,
            ]
        );

        $kg = MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'kg',
                'unit_type' => 'Mass',
            ],
            [
                'name' => 'Kilogram',
                'is_base_unit' => true,
                'conversion_factor' => 1,
                'description' => 'Standard unit for mass',
                'is_active' => true,
            ]
        );

        $km = MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'km',
                'unit_type' => 'Distance',
            ],
            [
                'name' => 'Kilometer',
                'is_base_unit' => true,
                'conversion_factor' => 1,
                'description' => 'Standard unit for distance',
                'is_active' => true,
            ]
        );

        $m2 = MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'm²',
                'unit_type' => 'Area',
            ],
            [
                'name' => 'Square Meter',
                'is_base_unit' => true,
                'conversion_factor' => 1,
                'description' => 'Standard unit for area',
                'is_active' => true,
            ]
        );

        $second = MeasurementUnit::updateOrCreate(
            [
                'symbol' => 's',
                'unit_type' => 'Time',
            ],
            [
                'name' => 'Second',
                'is_base_unit' => true,
                'conversion_factor' => 1,
                'description' => 'Standard unit for time',
                'is_active' => true,
            ]
        );

        $watt = MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'W',
                'unit_type' => 'Power',
            ],
            [
                'name' => 'Watt',
                'is_base_unit' => true,
                'conversion_factor' => 1,
                'description' => 'Standard unit for power',
                'is_active' => true,
            ]
        );

        $pascal = MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'Pa',
                'unit_type' => 'Pressure',
            ],
            [
                'name' => 'Pascal',
                'is_base_unit' => true,
                'conversion_factor' => 1,
                'description' => 'Standard unit for pressure',
                'is_active' => true,
            ]
        );

        $celsius = MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'C',
                'unit_type' => 'Temperature',
            ],
            [
                'name' => 'Celsius',
                'is_base_unit' => true,
                'conversion_factor' => 1,
                'description' => 'Standard unit for temperature',
                'is_active' => true,
            ]
        );

        $tCO2e = MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'tCO2e',
                'unit_type' => 'Emissions',
            ],
            [
                'name' => 'Metric Ton CO2 Equivalent',
                'is_base_unit' => true,
                'conversion_factor' => 1,
                'description' => 'Standard unit for greenhouse gas emissions',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'm³/s',
                'unit_type' => 'Flow Rate',
            ],
            [
                'name' => 'Cubic Meters per Second',
                'is_base_unit' => true,
                'conversion_factor' => 1,
                'description' => 'Standard unit for flow rate',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'ppm',
                'unit_type' => 'Concentration',
            ],
            [
                'name' => 'Parts per Million',
                'is_base_unit' => true,
                'conversion_factor' => 1,
                'description' => 'Standard unit for concentration',
                'is_active' => true,
            ]
        );

        $kgCO2ekWh = MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'kgCO2e/kWh',
                'unit_type' => 'Carbon Intensity',
            ],
            [
                'name' => 'Kilograms of CO2e per Kilowatt Hour',
                'is_base_unit' => true,
                'conversion_factor' => 1,
                'description' => 'Standard unit for carbon intensity of electricity',
                'is_active' => true,
            ]
        );

        // ----------------- ENERGY UNITS -----------------
        // Energy units that reference kWh as the base
        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'MWh',
                'unit_type' => 'Energy',
            ],
            [
                'name' => 'Megawatt-hour',
                'is_base_unit' => false,
                'si_unit_id' => $kWh->id,
                'conversion_factor' => 1000,
                'description' => 'Unit for large quantities of electricity',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'GWh',
                'unit_type' => 'Energy',
            ],
            [
                'name' => 'Gigawatt-hour',
                'is_base_unit' => false,
                'si_unit_id' => $kWh->id,
                'conversion_factor' => 1000000,
                'description' => 'Unit for very large quantities of electricity',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'J',
                'unit_type' => 'Energy',
            ],
            [
                'name' => 'Joule',
                'is_base_unit' => false,
                'si_unit_id' => $kWh->id,
                'conversion_factor' => 0.000000278,
                'description' => 'SI unit of energy',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'kJ',
                'unit_type' => 'Energy',
            ],
            [
                'name' => 'Kilojoule',
                'is_base_unit' => false,
                'si_unit_id' => $kWh->id,
                'conversion_factor' => 0.000278,
                'description' => '1000 joules',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'MJ',
                'unit_type' => 'Energy',
            ],
            [
                'name' => 'Megajoule',
                'is_base_unit' => false,
                'si_unit_id' => $kWh->id,
                'conversion_factor' => 0.278,
                'description' => '1 million joules',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'GJ',
                'unit_type' => 'Energy',
            ],
            [
                'name' => 'Gigajoule',
                'is_base_unit' => false,
                'si_unit_id' => $kWh->id,
                'conversion_factor' => 277.778,
                'description' => 'Unit of energy commonly used for natural gas',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'BTU',
                'unit_type' => 'Energy',
            ],
            [
                'name' => 'British Thermal Unit',
                'is_base_unit' => false,
                'si_unit_id' => $kWh->id,
                'conversion_factor' => 0.000293,
                'description' => 'Traditional unit for heat energy',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'MMBTU',
                'unit_type' => 'Energy',
            ],
            [
                'name' => 'Million British Thermal Units',
                'is_base_unit' => false,
                'si_unit_id' => $kWh->id,
                'conversion_factor' => 293.07,
                'description' => 'Common unit for large-scale heating systems',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'therm',
                'unit_type' => 'Energy',
            ],
            [
                'name' => 'Therm',
                'is_base_unit' => false,
                'si_unit_id' => $kWh->id,
                'conversion_factor' => 29.3,
                'description' => 'Unit of heat energy commonly used for natural gas',
                'is_active' => true,
            ]
        );

        // ----------------- VOLUME UNITS -----------------
        // Volume units that reference liter as the base
        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'mL',
                'unit_type' => 'Volume',
            ],
            [
                'name' => 'Milliliter',
                'is_base_unit' => false,
                'si_unit_id' => $liter->id,
                'conversion_factor' => 0.001,
                'description' => 'One-thousandth of a liter',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'gal',
                'unit_type' => 'Volume',
            ],
            [
                'name' => 'Gallon (US)',
                'is_base_unit' => false,
                'si_unit_id' => $liter->id,
                'conversion_factor' => 3.78541,
                'description' => 'US liquid gallon unit for fuel consumption',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'gal (UK)',
                'unit_type' => 'Volume',
            ],
            [
                'name' => 'Gallon (UK/Imperial)',
                'is_base_unit' => false,
                'si_unit_id' => $liter->id,
                'conversion_factor' => 4.54609,
                'description' => 'Imperial gallon used in UK and Commonwealth',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'qt',
                'unit_type' => 'Volume',
            ],
            [
                'name' => 'Quart (US)',
                'is_base_unit' => false,
                'si_unit_id' => $liter->id,
                'conversion_factor' => 0.946353,
                'description' => 'US liquid quart',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'm³',
                'unit_type' => 'Volume',
            ],
            [
                'name' => 'Cubic Meter',
                'is_base_unit' => false,
                'si_unit_id' => $liter->id,
                'conversion_factor' => 1000,
                'description' => 'Unit for large volumes (1000 liters)',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'cm³',
                'unit_type' => 'Volume',
            ],
            [
                'name' => 'Cubic Centimeter',
                'is_base_unit' => false,
                'si_unit_id' => $liter->id,
                'conversion_factor' => 0.001,
                'description' => 'Equal to one milliliter',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'ft³',
                'unit_type' => 'Volume',
            ],
            [
                'name' => 'Cubic Foot',
                'is_base_unit' => false,
                'si_unit_id' => $liter->id,
                'conversion_factor' => 28.3168,
                'description' => 'Common unit for natural gas volume in the US',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'bbl',
                'unit_type' => 'Volume',
            ],
            [
                'name' => 'Barrel (oil)',
                'is_base_unit' => false,
                'si_unit_id' => $liter->id,
                'conversion_factor' => 158.987,
                'description' => 'Standard unit for petroleum',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'CCF',
                'unit_type' => 'Volume',
            ],
            [
                'name' => '100 Cubic Feet (natural gas)',
                'is_base_unit' => false,
                'si_unit_id' => $liter->id,
                'conversion_factor' => 2831.68,
                'description' => 'Common unit for natural gas billing',
                'is_active' => true,
            ]
        );

        // ----------------- MASS UNITS -----------------
        // Mass units that reference kg as the base
        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'g',
                'unit_type' => 'Mass',
            ],
            [
                'name' => 'Gram',
                'is_base_unit' => false,
                'si_unit_id' => $kg->id,
                'conversion_factor' => 0.001,
                'description' => 'One-thousandth of a kilogram',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'mg',
                'unit_type' => 'Mass',
            ],
            [
                'name' => 'Milligram',
                'is_base_unit' => false,
                'si_unit_id' => $kg->id,
                'conversion_factor' => 0.000001,
                'description' => 'One-millionth of a kilogram',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'lb',
                'unit_type' => 'Mass',
            ],
            [
                'name' => 'Pound',
                'is_base_unit' => false,
                'si_unit_id' => $kg->id,
                'conversion_factor' => 0.453592,
                'description' => 'Imperial unit for weight',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'oz',
                'unit_type' => 'Mass',
            ],
            [
                'name' => 'Ounce',
                'is_base_unit' => false,
                'si_unit_id' => $kg->id,
                'conversion_factor' => 0.0283495,
                'description' => 'One-sixteenth of a pound',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'ton',
                'unit_type' => 'Mass',
            ],
            [
                'name' => 'Metric Ton',
                'is_base_unit' => false,
                'si_unit_id' => $kg->id,
                'conversion_factor' => 1000,
                'description' => '1000 kilograms, used for large masses',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'short ton',
                'unit_type' => 'Mass',
            ],
            [
                'name' => 'Short Ton (US)',
                'is_base_unit' => false,
                'si_unit_id' => $kg->id,
                'conversion_factor' => 907.185,
                'description' => '2000 pounds, used in the US',
                'is_active' => true,
            ]
        );

        // ----------------- DISTANCE UNITS -----------------
        // Distance units that reference km as the base
        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'mm',
                'unit_type' => 'Distance',
            ],
            [
                'name' => 'Millimeter',
                'is_base_unit' => false,
                'si_unit_id' => $km->id,
                'conversion_factor' => 0.000001,
                'description' => 'One-thousandth of a meter',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'cm',
                'unit_type' => 'Distance',
            ],
            [
                'name' => 'Centimeter',
                'is_base_unit' => false,
                'si_unit_id' => $km->id,
                'conversion_factor' => 0.00001,
                'description' => 'One-hundredth of a meter',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'm',
                'unit_type' => 'Distance',
            ],
            [
                'name' => 'Meter',
                'is_base_unit' => false,
                'si_unit_id' => $km->id,
                'conversion_factor' => 0.001,
                'description' => 'Base SI unit for length',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'mi',
                'unit_type' => 'Distance',
            ],
            [
                'name' => 'Mile',
                'is_base_unit' => false,
                'si_unit_id' => $km->id,
                'conversion_factor' => 1.60934,
                'description' => 'Imperial unit for distance',
                'is_active' => true,
            ]
        );

        // ----------------- AREA UNITS -----------------
        // Area units that reference m² as the base
        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'cm²',
                'unit_type' => 'Area',
            ],
            [
                'name' => 'Square Centimeter',
                'is_base_unit' => false,
                'si_unit_id' => $m2->id,
                'conversion_factor' => 0.0001,
                'description' => 'Area of a square with sides of one centimeter',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'km²',
                'unit_type' => 'Area',
            ],
            [
                'name' => 'Square Kilometer',
                'is_base_unit' => false,
                'si_unit_id' => $m2->id,
                'conversion_factor' => 1000000,
                'description' => 'Area of a square with sides of one kilometer',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'ha',
                'unit_type' => 'Area',
            ],
            [
                'name' => 'Hectare',
                'is_base_unit' => false,
                'si_unit_id' => $m2->id,
                'conversion_factor' => 10000,
                'description' => '10,000 square meters, used for land measurement',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'acre',
                'unit_type' => 'Area',
            ],
            [
                'name' => 'Acre',
                'is_base_unit' => false,
                'si_unit_id' => $m2->id,
                'conversion_factor' => 4046.86,
                'description' => 'Imperial unit for land measurement',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'ft²',
                'unit_type' => 'Area',
            ],
            [
                'name' => 'Square Foot',
                'is_base_unit' => false,
                'si_unit_id' => $m2->id,
                'conversion_factor' => 0.092903,
                'description' => 'Common unit for building area in the US',
                'is_active' => true,
            ]
        );

        // ----------------- TIME UNITS -----------------
        // Time units that reference second as the base
        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'min',
                'unit_type' => 'Time',
            ],
            [
                'name' => 'Minute',
                'is_base_unit' => false,
                'si_unit_id' => $second->id,
                'conversion_factor' => 60,
                'description' => '60 seconds',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'hr',
                'unit_type' => 'Time',
            ],
            [
                'name' => 'Hour',
                'is_base_unit' => false,
                'si_unit_id' => $second->id,
                'conversion_factor' => 3600,
                'description' => '60 minutes or 3600 seconds',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'day',
                'unit_type' => 'Time',
            ],
            [
                'name' => 'Day',
                'is_base_unit' => false,
                'si_unit_id' => $second->id,
                'conversion_factor' => 86400,
                'description' => '24 hours or 86,400 seconds',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'year',
                'unit_type' => 'Time',
            ],
            [
                'name' => 'Year',
                'is_base_unit' => false,
                'si_unit_id' => $second->id,
                'conversion_factor' => 31536000,
                'description' => '365 days or 31,536,000 seconds',
                'is_active' => true,
            ]
        );

        // ----------------- POWER UNITS -----------------
        // Power units that reference watt as the base
        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'kW',
                'unit_type' => 'Power',
            ],
            [
                'name' => 'Kilowatt',
                'is_base_unit' => false,
                'si_unit_id' => $watt->id,
                'conversion_factor' => 1000,
                'description' => '1000 watts, common unit for electrical power',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'MW',
                'unit_type' => 'Power',
            ],
            [
                'name' => 'Megawatt',
                'is_base_unit' => false,
                'si_unit_id' => $watt->id,
                'conversion_factor' => 1000000,
                'description' => '1 million watts, used for large power systems',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'hp',
                'unit_type' => 'Power',
            ],
            [
                'name' => 'Horsepower',
                'is_base_unit' => false,
                'si_unit_id' => $watt->id,
                'conversion_factor' => 745.7,
                'description' => 'Traditional unit of power',
                'is_active' => true,
            ]
        );

        // ----------------- EMISSIONS UNITS -----------------
        // Emissions units that reference tCO2e as the base
        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'kgCO2e',
                'unit_type' => 'Emissions',
            ],
            [
                'name' => 'Kilogram CO2 Equivalent',
                'is_base_unit' => false,
                'si_unit_id' => $tCO2e->id,
                'conversion_factor' => 0.001,
                'description' => 'Smaller unit for GHG emissions',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'gCO2e',
                'unit_type' => 'Emissions',
            ],
            [
                'name' => 'Gram CO2 Equivalent',
                'is_base_unit' => false,
                'si_unit_id' => $tCO2e->id,
                'conversion_factor' => 0.000001,
                'description' => 'Very small unit for GHG emissions',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'tCO2',
                'unit_type' => 'Emissions',
            ],
            [
                'name' => 'Metric Ton Carbon Dioxide',
                'is_base_unit' => false,
                'si_unit_id' => $tCO2e->id,
                'conversion_factor' => 1,
                'description' => 'Specific to CO2 emissions only',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'tCH4',
                'unit_type' => 'Emissions',
            ],
            [
                'name' => 'Metric Ton Methane',
                'is_base_unit' => false,
                'si_unit_id' => $tCO2e->id,
                'conversion_factor' => 25,
                'description' => 'Methane with GWP-100 factor of 25',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'lb CO₂e',
                'unit_type' => 'Emissions',
            ],
            [
                'name' => 'Pound CO2 Equivalent',
                'is_base_unit' => false,
                'si_unit_id' => $tCO2e->id,
                'conversion_factor' => 0.000453592, // 1 lb = 0.453592 kg = 0.000453592 metric tons
                'description' => 'US unit for GHG emissions (pounds)',
                'is_active' => true,
            ]
        );

        // ----------------- CARBON INTENSITY UNITS -----------------
        // Carbon intensity units with kgCO2e/kWh as base
        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'gCO2e/kWh',
                'unit_type' => 'Carbon Intensity',
            ],
            [
                'name' => 'Grams of CO2e per Kilowatt Hour',
                'is_base_unit' => false,
                'si_unit_id' => $kgCO2ekWh->id,
                'conversion_factor' => 0.001,
                'description' => 'Common unit for grid electricity carbon intensity',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'tCO2e/MWh',
                'unit_type' => 'Carbon Intensity',
            ],
            [
                'name' => 'Tons of CO2e per Megawatt Hour',
                'is_base_unit' => false,
                'si_unit_id' => $kgCO2ekWh->id,
                'conversion_factor' => 1,
                'description' => 'Used for large-scale electricity generation',
                'is_active' => true,
            ]
        );

        MeasurementUnit::updateOrCreate(
            [
                'symbol' => 'kgCO2e/GJ',
                'unit_type' => 'Carbon Intensity',
            ],
            [
                'name' => 'Kilograms of CO2e per Gigajoule',
                'is_base_unit' => false,
                'si_unit_id' => $kgCO2ekWh->id,
                'conversion_factor' => 0.0036,
                'description' => 'Used for fuels and energy sources',
                'is_active' => true,
            ]
        );

        $units = [
            [
                'symbol' => 'g/mile',
                'unit_type' => 'Distance',
                'name' => 'Grams per Mile',
                'is_base_unit' => false,
                'conversion_factor' => 1,
                'description' => 'Measurement unit for emissions per mile.',
                'is_active' => true,
            ],
            [
                'symbol' => 'g/km',
                'unit_type' => 'Distance',
                'name' => 'Grams per Kilometer',
                'is_base_unit' => false,
                'conversion_factor' => 1,
                'description' => 'Measurement unit for emissions per kilometer.',
                'is_active' => true,
            ],
        ];

        foreach ($units as $unit) {
            MeasurementUnit::updateOrCreate(
                [
                    'symbol' => $unit['symbol'],
                    'unit_type' => $unit['unit_type'],
                ],
                $unit
            );
        }

        $this->command->info('Measurement units seeded successfully.');
        Log::info('MeasurementUnitSeeder completed successfully.');
    }
}