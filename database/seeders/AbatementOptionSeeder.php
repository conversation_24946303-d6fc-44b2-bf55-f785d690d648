<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AbatementOptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $options = [
            // Energy Efficiency Options
            [
                'option_code' => 'LED_LIGHTING',
                'name' => 'LED Lighting Retrofit',
                'description' => 'Replace conventional lighting with energy-efficient LED systems',
                'category' => 'Energy Efficiency',
                'applicable_sectors' => ['all'],
                'applicable_scopes' => ['2'],
                'capital_cost_per_tonne' => 200.00,
                'operational_cost_per_tonne' => -50.00, // Savings
                'energy_savings_per_tonne' => 2.50,
                'other_savings_per_tonne' => 10.00,
                'implementation_complexity' => 3,
                'implementation_time' => 6,
                'max_reduction_percentage' => 0.05,
                'max_absolute_reduction' => 1000.00,
                'co_benefits' => ['Improved lighting quality', 'Reduced maintenance costs', 'Enhanced workplace comfort'],
                'risks' => ['Technology obsolescence', 'Initial disruption during installation'],
            ],
            [
                'option_code' => 'HVAC_OPTIMIZATION',
                'name' => 'HVAC System Optimization',
                'description' => 'Optimize heating, ventilation, and air conditioning systems for energy efficiency',
                'category' => 'Energy Efficiency',
                'applicable_sectors' => ['services', 'manufacturing', 'healthcare'],
                'applicable_scopes' => ['2'],
                'capital_cost_per_tonne' => 300.00,
                'operational_cost_per_tonne' => -20.00,
                'energy_savings_per_tonne' => 3.00,
                'other_savings_per_tonne' => 5.00,
                'implementation_complexity' => 5,
                'implementation_time' => 12,
                'max_reduction_percentage' => 0.15,
                'max_absolute_reduction' => 2000.00,
                'co_benefits' => ['Improved indoor air quality', 'Enhanced comfort', 'Better temperature control'],
                'risks' => ['Disruption during installation', 'Complexity of system integration'],
            ],
            [
                'option_code' => 'BUILDING_INSULATION',
                'name' => 'Building Envelope Improvements',
                'description' => 'Improve building insulation, windows, and envelope efficiency',
                'category' => 'Energy Efficiency',
                'applicable_sectors' => ['all'],
                'applicable_scopes' => ['2'],
                'capital_cost_per_tonne' => 400.00,
                'operational_cost_per_tonne' => -30.00,
                'energy_savings_per_tonne' => 3.50,
                'other_savings_per_tonne' => 8.00,
                'implementation_complexity' => 6,
                'implementation_time' => 18,
                'max_reduction_percentage' => 0.20,
                'max_absolute_reduction' => 3000.00,
                'co_benefits' => ['Improved comfort', 'Noise reduction', 'Increased property value'],
                'risks' => ['High upfront costs', 'Long payback period'],
            ],

            // Renewable Energy Options
            [
                'option_code' => 'SOLAR_PV',
                'name' => 'On-site Solar Photovoltaic',
                'description' => 'Install solar photovoltaic panels for on-site renewable electricity generation',
                'category' => 'Renewable Energy',
                'applicable_sectors' => ['all'],
                'applicable_scopes' => ['2'],
                'capital_cost_per_tonne' => 800.00,
                'operational_cost_per_tonne' => 20.00,
                'energy_savings_per_tonne' => 4.00,
                'other_savings_per_tonne' => 0.00,
                'implementation_complexity' => 7,
                'implementation_time' => 18,
                'max_reduction_percentage' => 0.80,
                'max_absolute_reduction' => 10000.00,
                'co_benefits' => ['Energy independence', 'Brand value enhancement', 'Long-term cost stability'],
                'risks' => ['Weather dependency', 'Technology risk', 'Grid integration challenges'],
            ],
            [
                'option_code' => 'WIND_POWER',
                'name' => 'On-site Wind Power',
                'description' => 'Install wind turbines for on-site renewable electricity generation',
                'category' => 'Renewable Energy',
                'applicable_sectors' => ['manufacturing', 'agriculture', 'energy'],
                'applicable_scopes' => ['2'],
                'capital_cost_per_tonne' => 1200.00,
                'operational_cost_per_tonne' => 30.00,
                'energy_savings_per_tonne' => 4.50,
                'other_savings_per_tonne' => 0.00,
                'implementation_complexity' => 8,
                'implementation_time' => 24,
                'max_reduction_percentage' => 0.60,
                'max_absolute_reduction' => 8000.00,
                'co_benefits' => ['Energy independence', 'Rural economic development', 'Land use efficiency'],
                'risks' => ['Wind resource variability', 'Noise concerns', 'Visual impact'],
            ],
            [
                'option_code' => 'GREEN_POWER_PURCHASE',
                'name' => 'Renewable Energy Procurement',
                'description' => 'Purchase renewable energy through PPAs or green energy certificates',
                'category' => 'Renewable Energy',
                'applicable_sectors' => ['all'],
                'applicable_scopes' => ['2'],
                'capital_cost_per_tonne' => 50.00,
                'operational_cost_per_tonne' => 10.00,
                'energy_savings_per_tonne' => 0.00,
                'other_savings_per_tonne' => 0.00,
                'implementation_complexity' => 2,
                'implementation_time' => 3,
                'max_reduction_percentage' => 1.00,
                'max_absolute_reduction' => 50000.00,
                'co_benefits' => ['Quick implementation', 'Market signal for renewables', 'Brand benefits'],
                'risks' => ['Market price volatility', 'Additionality concerns', 'Contract complexity'],
            ],

            // Transportation Options
            [
                'option_code' => 'FLEET_ELECTRIFICATION',
                'name' => 'Fleet Electrification',
                'description' => 'Replace conventional vehicles with electric vehicles',
                'category' => 'Transportation',
                'applicable_sectors' => ['transportation', 'services', 'manufacturing'],
                'applicable_scopes' => ['1', '2'],
                'capital_cost_per_tonne' => 1200.00,
                'operational_cost_per_tonne' => -40.00,
                'energy_savings_per_tonne' => 0.00,
                'other_savings_per_tonne' => 25.00,
                'implementation_complexity' => 6,
                'implementation_time' => 36,
                'max_reduction_percentage' => 0.70,
                'max_absolute_reduction' => 3000.00,
                'co_benefits' => ['Reduced air pollution', 'Lower fuel costs', 'Quiet operation'],
                'risks' => ['Charging infrastructure requirements', 'Range limitations', 'Battery degradation'],
            ],
            [
                'option_code' => 'ALTERNATIVE_FUELS',
                'name' => 'Alternative Fuel Adoption',
                'description' => 'Switch to biofuels, hydrogen, or other low-carbon fuels',
                'category' => 'Transportation',
                'applicable_sectors' => ['transportation', 'manufacturing', 'agriculture'],
                'applicable_scopes' => ['1'],
                'capital_cost_per_tonne' => 600.00,
                'operational_cost_per_tonne' => 20.00,
                'energy_savings_per_tonne' => 0.00,
                'other_savings_per_tonne' => 5.00,
                'implementation_complexity' => 7,
                'implementation_time' => 24,
                'max_reduction_percentage' => 0.50,
                'max_absolute_reduction' => 4000.00,
                'co_benefits' => ['Fuel diversification', 'Support for bio-economy', 'Energy security'],
                'risks' => ['Fuel availability', 'Cost volatility', 'Infrastructure requirements'],
            ],

            // Process Optimization Options
            [
                'option_code' => 'PROCESS_EFFICIENCY',
                'name' => 'Industrial Process Efficiency',
                'description' => 'Optimize industrial processes to reduce energy consumption and emissions',
                'category' => 'Process Optimization',
                'applicable_sectors' => ['manufacturing', 'energy', 'chemicals'],
                'applicable_scopes' => ['1', '2'],
                'capital_cost_per_tonne' => 500.00,
                'operational_cost_per_tonne' => -30.00,
                'energy_savings_per_tonne' => 3.50,
                'other_savings_per_tonne' => 15.00,
                'implementation_complexity' => 8,
                'implementation_time' => 24,
                'max_reduction_percentage' => 0.25,
                'max_absolute_reduction' => 5000.00,
                'co_benefits' => ['Increased productivity', 'Reduced waste', 'Improved quality'],
                'risks' => ['Production disruption', 'Technical complexity', 'Staff training requirements'],
            ],
            [
                'option_code' => 'WASTE_HEAT_RECOVERY',
                'name' => 'Waste Heat Recovery',
                'description' => 'Capture and utilize waste heat from industrial processes',
                'category' => 'Process Optimization',
                'applicable_sectors' => ['manufacturing', 'energy'],
                'applicable_scopes' => ['1', '2'],
                'capital_cost_per_tonne' => 700.00,
                'operational_cost_per_tonne' => -25.00,
                'energy_savings_per_tonne' => 4.00,
                'other_savings_per_tonne' => 12.00,
                'implementation_complexity' => 7,
                'implementation_time' => 18,
                'max_reduction_percentage' => 0.15,
                'max_absolute_reduction' => 2500.00,
                'co_benefits' => ['Energy cost savings', 'Improved efficiency', 'Reduced cooling needs'],
                'risks' => ['Technical complexity', 'Integration challenges', 'Maintenance requirements'],
            ],

            // Supply Chain Options
            [
                'option_code' => 'SUPPLIER_ENGAGEMENT',
                'name' => 'Supplier Engagement Program',
                'description' => 'Engage suppliers to reduce Scope 3 emissions through collaboration and requirements',
                'category' => 'Supply Chain',
                'applicable_sectors' => ['all'],
                'applicable_scopes' => ['3'],
                'capital_cost_per_tonne' => 100.00,
                'operational_cost_per_tonne' => 50.00,
                'energy_savings_per_tonne' => 0.00,
                'other_savings_per_tonne' => 20.00,
                'implementation_complexity' => 5,
                'implementation_time' => 12,
                'max_reduction_percentage' => 0.30,
                'max_absolute_reduction' => 15000.00,
                'co_benefits' => ['Improved supplier relationships', 'Risk reduction', 'Innovation opportunities'],
                'risks' => ['Supplier resistance', 'Increased costs', 'Complexity of coordination'],
            ],
            [
                'option_code' => 'LOCAL_SOURCING',
                'name' => 'Local and Regional Sourcing',
                'description' => 'Reduce transportation emissions by sourcing materials and services locally',
                'category' => 'Supply Chain',
                'applicable_sectors' => ['all'],
                'applicable_scopes' => ['3'],
                'capital_cost_per_tonne' => 150.00,
                'operational_cost_per_tonne' => 10.00,
                'energy_savings_per_tonne' => 0.00,
                'other_savings_per_tonne' => 15.00,
                'implementation_complexity' => 4,
                'implementation_time' => 18,
                'max_reduction_percentage' => 0.20,
                'max_absolute_reduction' => 5000.00,
                'co_benefits' => ['Support local economy', 'Reduced supply chain risk', 'Faster delivery'],
                'risks' => ['Limited supplier options', 'Potential cost increases', 'Quality concerns'],
            ],

            // Digital and Technology Options
            [
                'option_code' => 'DIGITALIZATION',
                'name' => 'Digital Transformation',
                'description' => 'Implement digital technologies to optimize operations and reduce emissions',
                'category' => 'Digital Technology',
                'applicable_sectors' => ['all'],
                'applicable_scopes' => ['1', '2', '3'],
                'capital_cost_per_tonne' => 300.00,
                'operational_cost_per_tonne' => -15.00,
                'energy_savings_per_tonne' => 2.00,
                'other_savings_per_tonne' => 30.00,
                'implementation_complexity' => 6,
                'implementation_time' => 24,
                'max_reduction_percentage' => 0.10,
                'max_absolute_reduction' => 3000.00,
                'co_benefits' => ['Improved efficiency', 'Better data insights', 'Enhanced competitiveness'],
                'risks' => ['Cybersecurity concerns', 'Staff training needs', 'Technology obsolescence'],
            ],
        ];

        foreach ($options as $option) {
            DB::table('abatement_options')->insert([
                'option_code' => $option['option_code'],
                'name' => $option['name'],
                'description' => $option['description'],
                'category' => $option['category'],
                'applicable_sectors' => json_encode($option['applicable_sectors']),
                'applicable_scopes' => json_encode($option['applicable_scopes']),
                'capital_cost_per_tonne' => $option['capital_cost_per_tonne'],
                'operational_cost_per_tonne' => $option['operational_cost_per_tonne'],
                'energy_savings_per_tonne' => $option['energy_savings_per_tonne'],
                'other_savings_per_tonne' => $option['other_savings_per_tonne'],
                'implementation_complexity' => $option['implementation_complexity'],
                'implementation_time' => $option['implementation_time'],
                'max_reduction_percentage' => $option['max_reduction_percentage'],
                'max_absolute_reduction' => $option['max_absolute_reduction'],
                'co_benefits' => json_encode($option['co_benefits']),
                'risks' => json_encode($option['risks']),
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
