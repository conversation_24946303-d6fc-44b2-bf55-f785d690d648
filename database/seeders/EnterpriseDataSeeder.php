<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Organization;
use App\Models\OrganizationalUnit;
use App\Models\Facility;
use App\Models\User;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\ActivityData;
use App\Models\CalculatedEmission;
use App\Models\EmissionTarget;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class EnterpriseDataSeeder extends Seeder
{
    public function run(): void
    {
        // Create Organizations
        $globalCorp = Organization::create([
            'name' => 'Global Manufacturing Corp',
            'industry_type' => 'Manufacturing',
            'address' => '123 Corporate Blvd, New York, NY 10001',
            'contact_person' => '<PERSON>',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '******-0123',
        ]);

        $techSolutions = Organization::create([
            'name' => 'TechSolutions Inc',
            'industry_type' => 'Technology',
            'address' => '456 Innovation Drive, San Francisco, CA 94105',
            'contact_person' => '<PERSON>',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '******-0456',
        ]);

        // Create Organizational Units for Global Corp
        $northAmerica = OrganizationalUnit::create([
            'organization_id' => $globalCorp->id,
            'name' => 'North America Division',
            'type' => 'division',
            'description' => 'North American operations',
            'code' => 'NAM',
            'is_active' => true,
        ]);

        $manufacturing = OrganizationalUnit::create([
            'organization_id' => $globalCorp->id,
            'parent_id' => $northAmerica->id,
            'name' => 'Manufacturing Department',
            'type' => 'department',
            'description' => 'Production and manufacturing operations',
            'code' => 'MFG',
            'is_active' => true,
        ]);

        $sustainability = OrganizationalUnit::create([
            'organization_id' => $globalCorp->id,
            'name' => 'Sustainability Office',
            'type' => 'department',
            'description' => 'Corporate sustainability and ESG',
            'code' => 'SUS',
            'is_active' => true,
        ]);

        // Create Facilities
        $facility1 = Facility::create([
            'organizational_unit_id' => $manufacturing->id,
            'name' => 'Detroit Manufacturing Plant',
            'facility_type' => 'manufacturing',
            'address' => '789 Industrial Way',
            'city' => 'Detroit',
            'state' => 'MI',
            'country' => 'USA',
            'latitude' => 42.3314,
            'longitude' => -83.0458,
            'size' => 250000,
            'size_unit' => 'sq_ft',
            'employee_count' => 450,
            'is_active' => true,
        ]);

        $facility2 = Facility::create([
            'organizational_unit_id' => $manufacturing->id,
            'name' => 'Austin Distribution Center',
            'facility_type' => 'warehouse',
            'address' => '321 Logistics Blvd',
            'city' => 'Austin',
            'state' => 'TX',
            'country' => 'USA',
            'latitude' => 30.2672,
            'longitude' => -97.7431,
            'size' => 150000,
            'size_unit' => 'sq_ft',
            'employee_count' => 125,
            'is_active' => true,
        ]);

        $facility3 = Facility::create([
            'organizational_unit_id' => null,
            'name' => 'San Francisco HQ',
            'facility_type' => 'office',
            'address' => '456 Innovation Drive',
            'city' => 'San Francisco',
            'state' => 'CA',
            'country' => 'USA',
            'latitude' => 37.7749,
            'longitude' => -122.4194,
            'size' => 75000,
            'size_unit' => 'sq_ft',
            'employee_count' => 200,
            'is_active' => true,
        ]);

        // Create Users with different roles and experience levels
        $users = [
            [
                'name' => 'Mike Johnson',
                'email' => '<EMAIL>',
                'organization_id' => $globalCorp->id,
                'organizational_unit_id' => $manufacturing->id,
                'facility_id' => $facility1->id,
                'job_title' => 'Facility Operations Manager',
                'department' => 'Manufacturing',
                'user_type' => 'facility_operator',
                'experience_level' => 'intermediate',
                'preferred_interface' => 'simplified',
                'default_scope' => 'scope_1',
            ],
            [
                'name' => 'Dr. Emily Chen',
                'email' => '<EMAIL>',
                'organization_id' => $globalCorp->id,
                'organizational_unit_id' => $sustainability->id,
                'facility_id' => null,
                'job_title' => 'Chief Sustainability Officer',
                'department' => 'Sustainability',
                'user_type' => 'sustainability_manager',
                'experience_level' => 'expert',
                'preferred_interface' => 'advanced',
                'default_scope' => null,
            ],
            [
                'name' => 'Robert Davis',
                'email' => '<EMAIL>',
                'organization_id' => $globalCorp->id,
                'organizational_unit_id' => $northAmerica->id,
                'facility_id' => null,
                'job_title' => 'VP of Operations',
                'department' => 'Operations',
                'user_type' => 'executive',
                'experience_level' => 'intermediate',
                'preferred_interface' => 'auto',
                'default_scope' => null,
            ],
            [
                'name' => 'Lisa Rodriguez',
                'email' => '<EMAIL>',
                'organization_id' => $globalCorp->id,
                'organizational_unit_id' => $manufacturing->id,
                'facility_id' => $facility2->id,
                'job_title' => 'Warehouse Supervisor',
                'department' => 'Logistics',
                'user_type' => 'facility_operator',
                'experience_level' => 'novice',
                'preferred_interface' => 'simplified',
                'default_scope' => 'scope_2',
            ],
            [
                'name' => 'David Kim',
                'email' => '<EMAIL>',
                'organization_id' => $techSolutions->id,
                'organizational_unit_id' => null,
                'facility_id' => $facility3->id,
                'job_title' => 'Environmental Analyst',
                'department' => 'ESG',
                'user_type' => 'sustainability_manager',
                'experience_level' => 'expert',
                'preferred_interface' => 'advanced',
                'default_scope' => 'scope_3',
            ],
        ];

        foreach ($users as $userData) {
            User::create(array_merge($userData, [
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'is_active' => true,
            ]));
        }

        // Create GHG Protocol Sectors
        $sectors = [
            [
                'name' => 'stationary_combustion',
                'display_name' => 'Stationary Combustion',
                'description' => 'Emissions from fuel combustion in stationary equipment',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'mobile_combustion',
                'display_name' => 'Mobile Combustion',
                'description' => 'Emissions from fuel combustion in mobile equipment',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'electricity',
                'display_name' => 'Purchased Electricity',
                'description' => 'Emissions from purchased electricity consumption',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'business_travel',
                'display_name' => 'Business Travel',
                'description' => 'Emissions from business travel activities',
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'name' => 'employee_commuting',
                'display_name' => 'Employee Commuting',
                'description' => 'Emissions from employee commuting',
                'sort_order' => 5,
                'is_active' => true,
            ],
        ];

        foreach ($sectors as $sectorData) {
            GhgProtocolSector::create($sectorData);
        }

        // Create GHG Protocol Activities
        $stationarySector = GhgProtocolSector::where('name', 'stationary_combustion')->first();
        $mobileSector = GhgProtocolSector::where('name', 'mobile_combustion')->first();
        $electricitySector = GhgProtocolSector::where('name', 'electricity')->first();
        $businessTravelSector = GhgProtocolSector::where('name', 'business_travel')->first();

        $activities = [
            // Stationary Combustion
            [
                'sector_id' => $stationarySector->id,
                'code' => 'SC_NG_BOILER',
                'name' => 'natural_gas_boiler',
                'display_name' => 'Natural Gas - Boiler',
                'activity_type' => 'fuel_combustion',
                'fuel_type' => 'natural_gas',
                'applicable_scopes' => ['scope_1'],
                'calculation_methods' => ['tier_1', 'tier_2', 'tier_3'],
                'is_active' => true,
            ],
            [
                'sector_id' => $stationarySector->id,
                'code' => 'SC_DIESEL_GENERATOR',
                'name' => 'diesel_generator',
                'display_name' => 'Diesel - Emergency Generator',
                'activity_type' => 'fuel_combustion',
                'fuel_type' => 'diesel',
                'applicable_scopes' => ['scope_1'],
                'calculation_methods' => ['tier_1', 'tier_2'],
                'is_active' => true,
            ],
            // Mobile Combustion
            [
                'sector_id' => $mobileSector->id,
                'code' => 'MC_GASOLINE_VEHICLE',
                'name' => 'gasoline_vehicle',
                'display_name' => 'Gasoline - Company Vehicle',
                'activity_type' => 'vehicle_fuel',
                'fuel_type' => 'gasoline',
                'vehicle_type' => 'passenger_car',
                'applicable_scopes' => ['scope_1'],
                'calculation_methods' => ['tier_1', 'tier_2'],
                'is_active' => true,
            ],
            // Electricity
            [
                'sector_id' => $electricitySector->id,
                'code' => 'EL_GRID_ELECTRICITY',
                'name' => 'grid_electricity',
                'display_name' => 'Grid Electricity Consumption',
                'activity_type' => 'electricity_consumption',
                'applicable_scopes' => ['scope_2'],
                'calculation_methods' => ['location_based', 'market_based'],
                'is_active' => true,
            ],
            // Business Travel
            [
                'sector_id' => $businessTravelSector->id,
                'code' => 'BT_AIR_DOMESTIC',
                'name' => 'air_travel_domestic',
                'display_name' => 'Air Travel - Domestic',
                'activity_type' => 'air_travel',
                'applicable_scopes' => ['scope_3'],
                'calculation_methods' => ['tier_1', 'tier_2'],
                'is_active' => true,
            ],
        ];

        foreach ($activities as $activityData) {
            GhgProtocolActivity::create($activityData);
        }

        $this->command->info('Enterprise data seeded successfully!');
        $this->command->info('Created:');
        $this->command->info('- 2 Organizations');
        $this->command->info('- 3 Organizational Units');
        $this->command->info('- 3 Facilities');
        $this->command->info('- 5 Users with different roles');
        $this->command->info('- 5 GHG Protocol Sectors');
        $this->command->info('- 5 GHG Protocol Activities');
    }
}
