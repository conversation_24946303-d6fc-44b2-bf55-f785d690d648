<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CalculationMethodologySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $methodologies = [
            [
                'code' => 'ghg_protocol',
                'name' => 'GHG Protocol Corporate Standard',
                'description' => 'The GHG Protocol Corporate Accounting and Reporting Standard provides requirements and guidance for companies and other organizations preparing a corporate-level GHG emissions inventory.',
                'version' => 'Revised Edition 2015',
                'supported_scopes' => ['1', '2', '3'],
                'default_options' => [
                    'gwp_source' => 'AR4',
                    'gwp_values' => [
                        'CO2' => 1,
                        'CH4' => 25,
                        'N2O' => 298,
                    ],
                    'tier' => 1,
                    'uncertainty_assessment' => false,
                ],
                'required_data' => [
                    'activity_data',
                    'emission_factors',
                    'organizational_boundaries',
                    'operational_boundaries',
                ],
                'guidance_notes' => 'Most widely used corporate GHG accounting standard. Provides flexibility in methodology choice while ensuring consistency and transparency.',
            ],
            [
                'code' => 'iso_14064',
                'name' => 'ISO 14064-1:2018',
                'description' => 'International standard that specifies principles and requirements at the organization level for quantification and reporting of greenhouse gas (GHG) emissions and removals.',
                'version' => '2018',
                'supported_scopes' => ['1', '2', '3'],
                'default_options' => [
                    'gwp_source' => 'AR5',
                    'gwp_values' => [
                        'CO2' => 1,
                        'CH4' => 28,
                        'N2O' => 265,
                    ],
                    'uncertainty_assessment' => true,
                    'data_quality_assessment' => true,
                    'verification_required' => true,
                ],
                'required_data' => [
                    'activity_data',
                    'emission_factors',
                    'uncertainty_data',
                    'data_quality_indicators',
                    'organizational_boundaries',
                ],
                'guidance_notes' => 'International standard with emphasis on uncertainty assessment and data quality. Requires more rigorous documentation and verification.',
            ],
            [
                'code' => 'pcaf',
                'name' => 'Partnership for Carbon Accounting Financials',
                'description' => 'Global partnership of financial institutions that work together to develop and implement a harmonized approach to assess and disclose the greenhouse gas emissions associated with their loans and investments.',
                'version' => 'Global GHG Accounting and Reporting Standard Part A 2020',
                'supported_scopes' => ['3'],
                'default_options' => [
                    'data_quality_scoring' => true,
                    'attribution_required' => true,
                    'asset_class_specific' => true,
                    'data_quality_scores' => [
                        1 => 'Verified emissions data',
                        2 => 'Unverified emissions data',
                        3 => 'Activity data and emission factors',
                        4 => 'Economic data and emission factors',
                        5 => 'Economic data and proxy emission factors',
                    ],
                ],
                'required_data' => [
                    'financial_data',
                    'attribution_factors',
                    'emission_factors',
                    'asset_class_data',
                    'data_quality_scores',
                ],
                'guidance_notes' => 'Specifically designed for financial institutions to measure financed emissions. Includes detailed data quality scoring system.',
            ],
            [
                'code' => 'custom_hybrid',
                'name' => 'Custom Hybrid Methodology',
                'description' => 'Flexible methodology that combines elements from multiple standards to meet specific organizational needs while maintaining scientific rigor and transparency.',
                'version' => '1.0',
                'supported_scopes' => ['1', '2', '3'],
                'default_options' => [
                    'primary_methodology' => 'ghg_protocol',
                    'secondary_methodology' => 'iso_14064',
                    'validation_required' => true,
                    'hybrid_logic' => 'primary_with_secondary_validation',
                    'custom_gwp_allowed' => true,
                ],
                'required_data' => [
                    'activity_data',
                    'emission_factors',
                    'methodology_justification',
                    'validation_criteria',
                ],
                'guidance_notes' => 'Allows organizations to combine methodologies while maintaining transparency. Requires clear documentation of approach and validation.',
            ],
            [
                'code' => 'wri_scope2',
                'name' => 'WRI Scope 2 Guidance',
                'description' => 'World Resources Institute guidance for accounting of Scope 2 emissions, providing detailed methodology for location-based and market-based calculations.',
                'version' => '2015',
                'supported_scopes' => ['2'],
                'default_options' => [
                    'dual_reporting' => true,
                    'location_based_required' => true,
                    'market_based_preferred' => true,
                    'contractual_instruments' => [
                        'energy_attribute_certificates',
                        'direct_contracts',
                        'supplier_specific_factors',
                        'residual_mix_factors',
                    ],
                ],
                'required_data' => [
                    'electricity_consumption',
                    'grid_factors',
                    'contractual_instruments',
                    'supplier_factors',
                ],
                'guidance_notes' => 'Specific guidance for Scope 2 calculations with emphasis on renewable energy procurement and market-based accounting.',
            ],
            [
                'code' => 'sectoral_approach',
                'name' => 'Sectoral Decarbonization Approach',
                'description' => 'Methodology that aligns corporate emissions reduction targets with climate science and sectoral decarbonization pathways.',
                'version' => '2.0',
                'supported_scopes' => ['1', '2', '3'],
                'default_options' => [
                    'sector_specific' => true,
                    'science_based_targets' => true,
                    'pathway_alignment' => true,
                    'intensity_metrics' => true,
                ],
                'required_data' => [
                    'activity_data',
                    'emission_factors',
                    'sector_pathways',
                    'intensity_metrics',
                    'production_data',
                ],
                'guidance_notes' => 'Designed for science-based target setting and sectoral decarbonization alignment. Includes intensity-based metrics.',
            ],
        ];

        foreach ($methodologies as $methodology) {
            DB::table('calculation_methodologies')->insert([
                'code' => $methodology['code'],
                'name' => $methodology['name'],
                'description' => $methodology['description'],
                'version' => $methodology['version'],
                'supported_scopes' => json_encode($methodology['supported_scopes']),
                'default_options' => json_encode($methodology['default_options']),
                'required_data' => json_encode($methodology['required_data']),
                'guidance_notes' => $methodology['guidance_notes'],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
