# GHG Protocol Emission Factors Import Documentation

## Overview

This document provides comprehensive documentation for the GHG Protocol emission factors import process that was executed to populate the carbon management database with standardized emission factors from the GHG Protocol's "Emission Factors for Cross-Sector Tools" Excel workbook.

## Import Summary

### Database Schema Enhancement

The system was enhanced with a new GHG Protocol-specific schema designed to handle the complex, hierarchical structure of official emission factors:

#### New Tables Created:
- `ghg_protocol_sectors` - Emission sectors (e.g., fuel_combustion, mobile_combustion)
- `ghg_protocol_activities` - Specific activities within sectors (e.g., "Fuel Combustion - Natural Gas")
- `ghg_protocol_regions` - Geographic regions with hierarchical support
- `ghg_protocol_emission_factors` - Main emission factors table
- `emission_factor_variants` - Regional/methodological variations
- `emission_factor_units` - Unit conversion relationships

### Import Commands Created

Three specialized import commands were developed to process the fuel combustion tables:

1. **ImportTable1CO2EmissionFactors** - CO2 emission factors by fuel
2. **ImportTable2CH4EmissionFactors** - CH4 emission factors by fuel
3. **ImportTable3N2OEmissionFactors** - N2O emission factors by fuel

## Detailed Import Results

### Table 1: CO2 Emission Factors by Fuel

**Command:** `php artisan import:table1-co2-emission-factors`

**Results:**
- ✅ **110 CO2 emission factors imported**
- **Coverage:** All major fuel types for carbon dioxide emissions
- **Measurement Bases:**
  - Energy basis (kg CO2/GJ) - Primary measurement
  - Mass basis (kg CO2/tonne) - For solid fuels
  - Volume basis (kg CO2/L) - For liquid fuels
  - Volume basis (kg CO2/m³) - For gaseous fuels

**Fuel Categories Imported:**
- **Oil Products (22 types):** Crude oil, gasoline, diesel, jet fuel, heating oil, LPG, etc.
- **Coal Products (16 types):** Anthracite, bituminous coal, lignite, coke, coal gas, etc.
- **Natural Gas (2 types):** Pipeline natural gas, municipal waste
- **Biomass & Waste (8 types):** Wood, biogas, biofuels, waste oils, landfill gas, etc.

**Key Features:**
- Multiple measurement units per fuel type
- Tier 2 methodology compliance
- Global applicability with regional override capability
- Comprehensive metadata including heating values and calculation methods

### Table 2: CH4 Emission Factors by Fuel

**Command:** `php artisan import:table2-ch4-emission-factors`

**Results:**
- ✅ **112 CH4 emission factors imported**
- **Coverage:** Methane emissions for fuel combustion
- **Measurement Bases:** Same as CO2 (energy, mass, volume)

**Notable Characteristics:**
- Higher factors for biomass and waste materials (300 g/GJ vs 10 g/GJ for fossil fuels)
- Natural gas has specific CH4 factors due to incomplete combustion
- Factors properly converted from g/GJ to kg/GJ for consistency
- Essential for complete GHG inventory calculations

### Table 3: N2O Emission Factors by Fuel

**Command:** `php artisan import:table3-n2o-emission-factors`

**Results:**
- ✅ **113 N2O emission factors imported**
- **Coverage:** Nitrous oxide emissions for fuel combustion
- **Measurement Bases:** Same as CO2 and CH4

**Notable Characteristics:**
- Higher factors for coal and biomass fuels
- Lower factors for natural gas and LPG (0.1 g/GJ)
- Critical for complete GHG Protocol compliance
- Completes the trilogy of major greenhouse gases

## Total Import Achievement

### Comprehensive Fuel Combustion Database

| Table | Gas | Factors | Coverage |
|-------|-----|---------|----------|
| **Table 1** | **CO2** | 110 | Carbon dioxide emissions |
| **Table 2** | **CH4** | 112 | Methane emissions |
| **Table 3** | **N2O** | 113 | Nitrous oxide emissions |
| **TOTAL** | **All GHGs** | **335** | **Complete fuel combustion** |

## Data Structure and Relationships

### Hierarchical Organization

```
GHG Protocol Sector (fuel_combustion)
├── Activities (48 fuel types)
│   ├── Fuel Combustion - Natural Gas
│   ├── Fuel Combustion - Motor gasoline
│   ├── Fuel Combustion - Diesel oil
│   └── ... (45 more)
└── Emission Factors (335 total)
    ├── CO2 Factors (110)
    ├── CH4 Factors (112)
    └── N2O Factors (113)
```

### Factor Metadata

Each emission factor includes comprehensive metadata:

```json
{
  "fuel_type": "Natural Gas",
  "measurement_basis": "energy_content",
  "lower_heating_value": 48.0,
  "table_source": "Table 1 - Fuel CO2 Factors",
  "calculation_basis": "primary_energy",
  "original_value_kg_gj": 56.1
}
```

## Technical Implementation Details

### Database Design Features

1. **Multi-Gas Support:** Separate factors for CO2, CH4, and N2O
2. **Multiple Measurement Units:** Energy, mass, and volume bases
3. **Regional Hierarchy:** Global factors with regional override capability
4. **Temporal Validity:** Valid from/until dates for factor versioning
5. **Priority System:** Ranking system for factor selection
6. **Tier Compliance:** GHG Protocol tier level tracking

### Code Generation

Each import command automatically:
- Creates sector and activity records if missing
- Generates unique factor codes
- Establishes proper relationships
- Sets appropriate priorities and defaults
- Includes comprehensive metadata
- Handles unit conversions

### Quality Assurance

- **Data Validation:** All factors validated against source tables
- **Unit Consistency:** Proper unit conversions applied
- **Relationship Integrity:** Foreign key constraints enforced
- **Metadata Completeness:** Full traceability to source data

## Usage and Integration

### Factor Lookup Service

The system includes an advanced lookup service that:
- Finds best factors based on criteria
- Handles regional fallbacks
- Manages temporal validity
- Supports variant selection

### Calculation Service

Enhanced calculation service provides:
- Multi-gas calculations (CO2, CH4, N2O)
- Unit conversion handling
- Factor selection explanation
- Calculation audit trails

## Data Quality and Compliance

### GHG Protocol Compliance

- ✅ **Tier 2 Methodology:** All factors meet Tier 2 requirements
- ✅ **Official Source:** Direct from GHG Protocol workbooks
- ✅ **Complete Coverage:** All major fuel types included
- ✅ **Multi-Gas Support:** CO2, CH4, and N2O factors
- ✅ **Proper Units:** Standardized measurement units

### Data Traceability

Every factor includes:
- Source table reference
- Original value preservation
- Calculation methodology
- Data quality rating
- Uncertainty information (where available)

## Future Expansion

### Ready for Additional Tables

The schema is designed to accommodate:
- Mobile combustion factors
- Electricity grid factors
- Industrial process factors
- Refrigerant factors
- Waste management factors

### Import Command Template

New import commands can follow the established pattern:
1. Define data structure
2. Create import command class
3. Handle relationships and validation
4. Generate proper metadata
5. Execute with transaction safety

## Maintenance and Updates

### Factor Updates

The system supports:
- Version tracking for factor changes
- Temporal validity management
- Priority-based factor selection
- Backward compatibility

### Data Refresh

Import commands can be re-run to:
- Update existing factors
- Add new factors
- Refresh metadata
- Maintain data currency

## Conclusion

The GHG Protocol import process has successfully established a comprehensive, standards-compliant emission factors database that provides:

- **Complete fuel combustion coverage** for all major greenhouse gases
- **Multiple measurement options** to accommodate different data availability
- **Standards compliance** with GHG Protocol methodology
- **Scalable architecture** for future expansion
- **Advanced lookup capabilities** for accurate factor selection

This foundation enables accurate, auditable, and compliant greenhouse gas emissions calculations for corporate carbon management and reporting.

## System Integration Updates

### 1. New GHG Protocol Emission Factor Resource

**File:** `app/Filament/Resources/GhgProtocolEmissionFactorResource.php`

A comprehensive admin interface for managing GHG Protocol emission factors with:
- **Advanced filtering** by sector, gas type, region, quality rating, and tier level
- **Multi-tab forms** for detailed factor configuration
- **Real-time validation** and data quality indicators
- **Batch operations** for factor management
- **Navigation badge** showing active factor count

**Key Features:**
- Sector and activity relationship management
- Regional hierarchy support
- Data quality and uncertainty tracking
- Temporal validity management
- Metadata and calculation method documentation

### 2. Enhanced Activity Data Resource

**File:** `app/Filament/Resources/ActivityDataResource.php`

Updated with new "GHG Protocol Factors" tab providing:
- **Sector selection** from GHG Protocol taxonomy
- **Activity selection** based on chosen sector
- **Factor preview** showing available emission factors
- **Toggle for GHG Protocol** vs legacy factor usage
- **Real-time calculation preview** with method indication

**Enhanced Calculation Display:**
- Shows calculation method (legacy vs GHG Protocol)
- Provides factor selection guidance
- Real-time emission estimates
- Method-specific validation

### 3. Updated Activity Data Model

**File:** `app/Models/ActivityData.php`

Added support for GHG Protocol integration:
- **New fields:** `ghg_protocol_sector_id`, `ghg_protocol_activity_id`, `use_ghg_protocol`
- **Relationships:** Links to GHG Protocol sectors and activities
- **Boolean casting** for protocol usage flag
- **Backward compatibility** with existing emission factor system

### 4. Enhanced Emission Calculation Service

**File:** `app/Services/EmissionCalculationService.php`

New calculation methods:
- **`calculateEnhanced()`** - Intelligent method selection
- **`calculateWithGhgProtocolFromActivity()`** - Direct GHG Protocol calculation
- **Automatic fallback** from GHG Protocol to legacy factors
- **Detailed calculation tracking** with method identification

**Calculation Logic:**
1. Check if GHG Protocol is enabled and configured
2. Use GHG Protocol factors if available
3. Fall back to legacy emission factors
4. Return detailed calculation results with method used

### 5. Database Schema Updates

**Migration:** `2025_06_13_032730_add_ghg_protocol_fields_to_activity_data_table.php`

Added to `activity_data` table:
- `ghg_protocol_sector_id` (nullable foreign key)
- `ghg_protocol_activity_id` (nullable foreign key)
- `use_ghg_protocol` (boolean, default false)
- Foreign key constraints with cascade delete protection

## User Experience Improvements

### 1. Dual Factor System
Users can now choose between:
- **Legacy emission factors** (existing custom factors)
- **GHG Protocol factors** (standardized, auditable factors)
- **Automatic selection** based on data availability

### 2. Enhanced Data Quality
- **Tier level indicators** (Tier 1, 2, 3) with color coding
- **Data quality ratings** (High, Medium, Low) with badges
- **Source traceability** to original GHG Protocol tables
- **Uncertainty information** where available

### 3. Improved Navigation
- **Separate resource** for GHG Protocol factors
- **Badge indicators** showing active factor counts
- **Advanced filtering** for factor discovery
- **Contextual help** and guidance

## Technical Architecture

### 1. Separation of Concerns
- **Legacy factors** remain unchanged for backward compatibility
- **GHG Protocol factors** in dedicated schema
- **Unified calculation service** handles both systems
- **Intelligent routing** based on user preferences

### 2. Data Integrity
- **Foreign key constraints** ensure referential integrity
- **Nullable relationships** allow gradual migration
- **Default values** prevent breaking changes
- **Transaction safety** in all operations

### 3. Scalability
- **Modular design** supports additional GHG Protocol tables
- **Extensible schema** for future enhancements
- **Performance optimized** queries with proper indexing
- **Caching ready** for high-volume operations

## Migration Strategy

### 1. Backward Compatibility
- **Existing data** continues to work unchanged
- **Legacy calculations** remain available
- **Gradual adoption** of GHG Protocol factors
- **No breaking changes** to existing workflows

### 2. Data Transition
- **Optional migration** to GHG Protocol factors
- **Side-by-side comparison** of calculation methods
- **Validation tools** for factor accuracy
- **Audit trails** for all changes

### 3. User Training
- **Clear indicators** of calculation method used
- **Contextual help** for factor selection
- **Preview calculations** before saving
- **Method comparison** tools

## Future Enhancements

### 1. Additional GHG Protocol Tables
- **Mobile combustion** factors (vehicles, equipment)
- **Electricity grid** factors (regional grids)
- **Industrial processes** factors
- **Refrigerant** factors
- **Waste management** factors

### 2. Advanced Features
- **Automatic factor updates** from GHG Protocol releases
- **Regional factor recommendations** based on facility location
- **Uncertainty propagation** in calculations
- **Multi-gas calculations** (CO2, CH4, N2O) with GWP

### 3. Reporting Enhancements
- **GHG Protocol compliance** reporting
- **Factor source documentation** in reports
- **Calculation method transparency**
- **Audit trail** integration

This comprehensive update establishes a robust foundation for GHG Protocol compliance while maintaining full backward compatibility with existing systems.
