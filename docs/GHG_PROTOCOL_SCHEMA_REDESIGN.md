# GHG Protocol Emission Factors Schema Redesign

## Overview

This document describes the redesigned emission factors database schema that supports the GHG Protocol's "Emission Factors for Cross-Sector Tools" structure. The new schema enables accurate, flexible carbon calculations across all covered sectors and scopes with regional and methodological variations.

## Schema Architecture

### Core Tables

#### 1. `ghg_protocol_sectors`
Represents the main sectors from GHG Protocol worksheets (e.g., stationary combustion, mobile combustion, electricity).

**Key Fields:**
- `name`: Unique sector identifier (e.g., 'stationary_combustion')
- `display_name`: Human-readable name (e.g., 'Stationary Combustion')
- `description`: Detailed description of the sector
- `sort_order`: Display ordering
- `is_active`: Enable/disable sectors

#### 2. `ghg_protocol_activities`
Specific activities within each sector (e.g., natural gas combustion, gasoline vehicle operation).

**Key Fields:**
- `sector_id`: Foreign key to sectors table
- `code`: Unique activity code (e.g., 'STAT_COMB_NG')
- `name`: Activity name
- `activity_type`: Type classification ('combustion', 'electricity', 'transport')
- `fuel_type`: Fuel type for combustion activities
- `vehicle_type`: Vehicle type for mobile combustion
- `applicable_scopes`: JSON array of applicable GHG scopes [1, 2, 3]
- `calculation_methods`: JSON array of available calculation approaches

#### 3. `ghg_protocol_regions`
Hierarchical geographic regions supporting country, state, and grid-specific factors.

**Key Fields:**
- `code`: Unique region code (e.g., 'US', 'US-CA', 'WECC')
- `name`: Region name
- `region_type`: Type ('country', 'state', 'grid_region', 'economic_region')
- `parent_code`: Parent region for hierarchy
- `iso_country_code`: ISO 3166-1 alpha-3 country code
- `applicable_sectors`: JSON array of sectors this region applies to

#### 4. `ghg_protocol_emission_factors`
Main emission factors table with comprehensive metadata.

**Key Fields:**
- `code`: Unique factor identifier
- `sector_id`, `activity_id`, `region_id`, `gas_id`: Foreign keys
- `calculation_method`: Methodology ('tier1', 'tier2', 'country_specific')
- `factor_value`: Primary emission factor value
- `co2_factor`, `ch4_factor`, `n2o_factor`: Gas-specific factors
- `carbon_content`, `oxidation_factor`, `heating_value`: Combustion parameters
- `input_unit_id`, `output_unit_id`: Unit relationships
- `data_quality_rating`: Quality rating (A, B, C, D)
- `uncertainty_percentage`: Uncertainty range
- `vintage_year`: Data vintage year
- `valid_from`, `valid_until`: Temporal validity
- `tier_level`: IPCC tier level
- `priority`: Selection priority for factor lookup

#### 5. `emission_factor_variants`
Regional and methodological variations of base emission factors.

**Key Fields:**
- `base_factor_id`: Foreign key to base emission factor
- `variant_type`: Type ('regional', 'temporal', 'methodological')
- `variant_code`: Unique code within base factor
- `region_id`: Specific region for this variant
- `factor_value`: Override factor value (if different from base)
- `variant_conditions`: JSON conditions when variant applies
- `priority`: Variant selection priority

#### 6. `emission_factor_units` (Enhanced)
Complex unit relationships supporting multiple input/output units per factor.

**Key Fields:**
- `ghg_protocol_factor_id`: Link to GHG Protocol factors
- `unit_role`: Role ('input', 'output', 'per_unit', 'density')
- `measurement_unit_id`: Foreign key to units
- `conversion_factor`: Conversion multiplier
- `is_primary`: Primary unit for this role

## Key Features

### 1. Hierarchical Region Support
```sql
-- Example: California inherits from US, which inherits from Global
GLOBAL -> US -> US-CA
```

### 2. Factor Selection Algorithm
The `EmissionFactorLookupService` implements intelligent factor selection:

1. **Exact Match**: Look for factors matching all criteria
2. **Regional Fallback**: Try parent regions in hierarchy
3. **Default Factors**: Fall back to global/default factors
4. **Priority Ranking**: Order by priority and tier level

### 3. Variant System
Factors can have multiple variants for:
- **Regional variations**: Different values for specific regions
- **Temporal variations**: Different values for different time periods
- **Methodological variations**: Different calculation approaches

### 4. Flexible Unit System
Supports complex unit relationships:
- Multiple input units per factor (e.g., volume, mass, energy)
- Automatic unit conversion
- Context-specific conversions

## Usage Examples

### 1. Finding Emission Factors

```php
use App\Services\EmissionFactorLookupService;

$lookupService = new EmissionFactorLookupService();

$criteria = [
    'sector_id' => 1, // Stationary combustion
    'activity_id' => 2, // Natural gas combustion
    'gas_id' => 1, // CO2
    'region_id' => 5, // US-CA
    'date' => '2023-06-15',
    'calculation_method' => 'tier1'
];

$factor = $lookupService->findBestFactor($criteria);
$variant = $lookupService->findBestVariant($factor, $criteria);
```

### 2. Calculating Emissions

```php
use App\Services\EmissionCalculationService;

$calculationService = new EmissionCalculationService($lookupService);

$result = $calculationService->calculateWithGhgProtocol($activityData, [
    'region_id' => 5,
    'calculation_method' => 'tier1'
]);

if ($result['success']) {
    echo "Emissions: " . $result['emissions'] . " kg CO2e";
    echo "Factor used: " . $result['factor_used']->name;
    echo "Explanation: " . $result['explanation'];
}
```

### 3. Importing GHG Protocol Data

```php
use App\Services\GhgProtocolImportService;

$importService = new GhgProtocolImportService();

$data = [
    'sectors' => [...],
    'regions' => [...],
    'activities' => [...],
    'emission_factors' => [...]
];

$result = $importService->importFromArray($data);
```

## Migration Path

### From Existing Schema
The new schema is designed to coexist with the existing `emission_factors` table:

1. **Backward Compatibility**: Existing code continues to work
2. **Gradual Migration**: New features use GHG Protocol tables
3. **Data Mapping**: Services can map between old and new structures

### Enhanced ActivityDataResource
The `ActivityDataResource` can be enhanced to use the new calculation service:

```php
// In ActivityDataResource.php
use App\Services\EmissionCalculationService;
use App\Services\EmissionFactorLookupService;

public static function afterCreate(ActivityData $record, array $data): void
{
    if ($data['auto_calculate_emissions'] ?? true) {
        $lookupService = new EmissionFactorLookupService();
        $calculationService = new EmissionCalculationService($lookupService);
        
        $result = $calculationService->calculateWithGhgProtocol($record);
        
        if ($result['success']) {
            Emission::create([
                'facility_id' => $record->facility_id,
                'organization_id' => $record->organization_id,
                'emission_source_id' => $record->source_id,
                'activity_data_id' => $record->id,
                'emission_factor_id' => $result['factor_used']->id,
                'reporting_period' => $record->date_recorded,
                'scope' => $record->source ? $record->source->scope : '3',
                'emissions_value' => $result['emissions'],
                'calculation_method' => 'ghg_protocol',
                'gas_type' => 'CO2e',
                'notes' => $result['explanation'],
            ]);
        }
    }
}
```

## Commands and Tools

### Setup Commands
```bash
# Setup basic GHG Protocol structure
php artisan db:seed --class=GhgProtocolStructureSeeder

# Create sample emission factors
php artisan ghg:setup-data

# Test the schema
php artisan ghg:test-schema
```

### Data Import
```bash
# Import from structured data array
php artisan ghg:import-data --file=ghg_protocol_data.json
```

## Benefits

1. **Comprehensive Coverage**: Supports all GHG Protocol sectors and methodologies
2. **Regional Flexibility**: Hierarchical regions with intelligent fallback
3. **Data Quality**: Built-in uncertainty and quality ratings
4. **Temporal Support**: Version control with validity periods
5. **Extensibility**: Easy to add new sectors, activities, and regions
6. **Performance**: Optimized indexes for fast factor lookup
7. **Backward Compatibility**: Coexists with existing schema

## Next Steps

1. **Data Population**: Import actual GHG Protocol emission factors
2. **UI Enhancement**: Update Filament resources for new schema
3. **Reporting**: Enhanced reporting with factor traceability
4. **API Development**: RESTful API for factor lookup and calculation
5. **Validation**: Comprehensive data validation rules
6. **Documentation**: User guides and API documentation
