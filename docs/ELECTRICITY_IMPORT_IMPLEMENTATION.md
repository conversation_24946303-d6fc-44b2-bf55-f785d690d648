# Electricity Emission Factors Import Implementation

## Overview

This document describes the implementation of the electricity emission factors import system for GHG Protocol data, specifically designed to handle the Excel workbook structure shown in your screenshots.

## Implemented Components

### 1. ElectricityFactorsImportService (`app/Services/ElectricityFactorsImportService.php`)

**Purpose**: Core service for importing electricity emission factors into the database.

**Key Features**:
- Handles regional variations (China, Taiwan, Brazil, Thailand, U.K.)
- Supports multiple greenhouse gases (CO₂, CH₄, N₂O)
- Manages different unit formats (tCO₂/MWh, kgCO₂e/kWh, etc.)
- Creates standardized emission factor records with proper relationships
- Tracks import statistics and errors

**Data Structure Handled**:
```php
[
    'region' => 'Taiwan',
    'table_name' => 'Table 2. Electricity Emission Factors for Taiwan',
    'notes' => 'Methodology notes...',
    'data' => [
        ['year' => 2022, 'emission_factor' => 0.495, 'unit' => 'kgCO₂e/kWh'],
        // ... more years
    ]
]
```

### 2. GhgProtocolDataValidator (`app/Services/GhgProtocolDataValidator.php`)

**Purpose**: Comprehensive data validation before import.

**Validation Features**:
- **Structure Validation**: Required fields, data types
- **Value Validation**: Numeric ranges, negative values, unusually high values
- **Unit Validation**: Recognizes standard emission factor units
- **Temporal Validation**: Year ranges (1990-2030)
- **Consistency Validation**: Year-over-year change detection
- **Region Validation**: Known region names with suggestions

**Example Validation Results**:
```
✅ PASSED - All validations successful
⚠️  Warnings for unusual values or unrecognized formats
❌ Errors for missing required data or invalid formats
```

### 3. GhgProtocolExcelImportService (`app/Services/GhgProtocolExcelImportService.php`)

**Purpose**: Excel file processing and table extraction.

**Features**:
- **Multi-sheet Processing**: Handles Excel workbooks with multiple sheets
- **Table Detection**: Automatically identifies data tables vs. notes/headers
- **Header Mapping**: Maps Excel columns to standardized data fields
- **Unit Extraction**: Extracts units from headers or data cells
- **Region Detection**: Identifies regions from table titles
- **Sector Classification**: Determines sector from filename patterns

**Supported File Patterns**:
- Files starting with `ghg_`
- `ghg_toc` for table of contents (skipped)
- Sector-specific files (electricity, transport, etc.)

### 4. Import Command (`app/Console/Commands/ImportElectricityFactors.php`)

**Purpose**: Command-line interface for importing electricity data.

**Usage Examples**:
```bash
# Import from Excel file
php artisan ghg:import-electricity path/to/electricity_factors.xlsx

# Validate Excel file without importing
php artisan ghg:import-electricity path/to/file.xlsx --validate-only

# Import test data (from your screenshots)
php artisan ghg:import-electricity --test

# Validate test data only
php artisan ghg:import-electricity --test --validate-only
```

## Data Mapping

### Regional Mapping
| Region Name | Code | ISO Country |
|-------------|------|-------------|
| China | CN | CHN |
| Taiwan | TW | TWN |
| Brazil | BR | BRA |
| Thailand | TH | THA |
| U.K. | GB | GBR |

### Gas Type Mapping
| Input | Database Gas | Chemical Formula |
|-------|--------------|------------------|
| CO2/CO₂ | CO₂ | CO₂ |
| CH4/CH₄ | CH₄ | CH₄ |
| N2O/N₂O | N₂O | N₂O |

### Unit Standardization
| Input Unit | Standard Unit | Conversion |
|------------|---------------|------------|
| tCO₂/MWh | kg CO₂e/kWh | 1:1 (equivalent) |
| kgCO₂e/kWh | kg CO₂e/kWh | Direct |
| CO₂ (kg/kWh) | kg CO₂e/kWh | Direct |

## Database Schema Integration

### Tables Used
1. **ghg_protocol_sectors**: Electricity sector
2. **ghg_protocol_activities**: Grid electricity consumption activity
3. **ghg_protocol_regions**: Country/regional definitions
4. **ghg_protocol_emission_factors**: Main emission factor records
5. **greenhouse_gases**: Gas type definitions
6. **measurement_units**: Unit definitions

### Factor Code Generation
Pattern: `ELEC_GRID_{GAS}_{REGION_CODE}_{YEAR}`

Examples:
- `ELEC_GRID_CO2_TW_2022` (Taiwan CO₂ 2022)
- `ELEC_GRID_CH4_GB_2023` (U.K. CH₄ 2023)

## Test Data Implementation

Based on your screenshots, the system includes test data for:

1. **China**: 2022 national average (0.5703 tCO₂/MWh)
2. **Taiwan**: 2005-2022 time series (0.555 to 0.495 kgCO₂e/kWh)
3. **Brazil**: 2016-2023 time series (0.0385 to 0.1264 tCO₂/MWh)
4. **Thailand**: 2011-2022 generation factors (0.530 to 0.412 kg/kWh)
5. **U.K.**: 2023 multi-gas factors (CO₂: 0.20496, CH₄: 0.000032, N₂O: 0.0000046 kg/kWh)

## Import Results

**Successful Test Import**:
- ✅ 5 regions created
- ✅ 42 emission factors created
- ✅ All data validated successfully
- ✅ Proper relationships established

## Next Steps

1. **Excel File Processing**: Ready to process actual GHG Protocol Excel files
2. **Additional Sectors**: Framework ready for transport, stationary combustion, etc.
3. **Data Quality**: Validation system ensures data integrity
4. **Regional Expansion**: Easy to add new countries/regions
5. **API Integration**: Data available through existing emission factor APIs

## Usage Instructions

### For New Excel Files
1. Place Excel file in accessible location
2. Run: `php artisan ghg:import-electricity path/to/file.xlsx --validate-only`
3. Review validation results
4. If valid, run: `php artisan ghg:import-electricity path/to/file.xlsx`

### For Additional Sectors
1. Extend `GhgProtocolExcelImportService` with sector-specific parsing
2. Create sector-specific import services (following `ElectricityFactorsImportService` pattern)
3. Add validation rules to `GhgProtocolDataValidator`
4. Update command to handle new sector types

The system is now ready to handle your GHG Protocol Excel files and can be easily extended for additional sectors and data sources.
