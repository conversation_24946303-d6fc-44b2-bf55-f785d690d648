<?php
// In routes/api.php
use App\Models\Facility;
use App\Models\Emission;
use App\Models\ActivityData;
use App\Http\Controllers\Api\ExternalDataController;
use Illuminate\Support\Facades\Route;

Route::get('/facilities/{facility}', function (Facility $facility) {
    // Load relationships
    $facility->load('organization');

    // Get statistics
    $facilityData = $facility->toArray();

    // Get activity count
    $facilityData['activity_count'] = ActivityData::where('facility_id', $facility->id)->count();

    // Calculate total emissions
    $totalEmissions = Emission::where('facility_id', $facility->id)->sum('emissions_value');
    $facilityData['total_emissions'] = number_format($totalEmissions, 2);

    return $facilityData;
});

// Add a general help route for contextual help
Route::get('/help/{topic}', function ($topic) {
    $helpContent = [
        'dashboard' => [
            'title' => 'Dashboard Help',
            'content' => 'The dashboard provides an overview of your organization\'s emissions and key metrics.'
        ],
        'facilities' => [
            'title' => 'Facilities Management',
            'content' => 'Facilities represent physical locations where emissions occur. Add details like size and employee count to improve reporting accuracy.'
        ],
        'activity-data' => [
            'title' => 'Activity Data',
            'content' => 'Activity data represents measurable activities that result in emissions, such as electricity consumption or fuel usage.'
        ],
        'emissions' => [
            'title' => 'Emissions',
            'content' => 'Emissions records show calculated greenhouse gas emissions based on your activity data and emission factors.'
        ],
        // Add more help topics
    ];

    if (!isset($helpContent[$topic])) {
        return response()->json(['error' => 'Help topic not found'], 404);
    }

    return response()->json($helpContent[$topic]);
});

// External Data Integration Routes
Route::prefix('external')->group(function () {
    // Webhook endpoints for external systems
    Route::post('/webhooks/erp-data', [ExternalDataController::class, 'handleERPData']);
    Route::post('/webhooks/sensor-data', [ExternalDataController::class, 'handleSensorData']);
    Route::post('/webhooks/supplier-data', [ExternalDataController::class, 'handleSupplierData']);

    // Bulk upload endpoint
    Route::post('/bulk-upload', [ExternalDataController::class, 'bulkUpload']);

    // Data lake and statistics
    Route::get('/data-lake/stats', [ExternalDataController::class, 'getDataLakeStats']);

    // Connection testing
    Route::get('/test-connections', [ExternalDataController::class, 'testConnections']);
});

// ERP Integration Routes
Route::prefix('erp')->group(function () {
    Route::post('/connect/sap', function () {
        // SAP connection endpoint
        return response()->json(['message' => 'SAP connection endpoint']);
    });

    Route::post('/connect/oracle', function () {
        // Oracle connection endpoint
        return response()->json(['message' => 'Oracle connection endpoint']);
    });

    Route::post('/sync/{system}', function ($system) {
        // Sync data from ERP system
        return response()->json(['message' => "Sync from {$system}"]);
    });
});

// IoT Sensor Routes
Route::prefix('iot')->group(function () {
    Route::post('/sensors/register', function () {
        // Register new sensor
        return response()->json(['message' => 'Sensor registration endpoint']);
    });

    Route::post('/sensors/{sensorId}/data', function ($sensorId) {
        // Receive sensor data
        return response()->json(['message' => "Data received for sensor {$sensorId}"]);
    });

    Route::get('/sensors/{facilityId}/realtime', function ($facilityId) {
        // Get real-time data for facility
        return response()->json(['message' => "Real-time data for facility {$facilityId}"]);
    });
});

// Supply Chain Integration Routes
Route::prefix('supply-chain')->group(function () {
    Route::post('/suppliers/{supplierId}/connect', function ($supplierId) {
        // Connect supplier portal
        return response()->json(['message' => "Connect supplier {$supplierId}"]);
    });

    Route::post('/suppliers/sync', function () {
        // Sync all supplier data
        return response()->json(['message' => 'Sync supplier data']);
    });

    Route::post('/scope3/validate', function () {
        // Validate Scope 3 data
        return response()->json(['message' => 'Validate Scope 3 data']);
    });

    Route::post('/scope3/import', function () {
        // Import Scope 3 data
        return response()->json(['message' => 'Import Scope 3 data']);
    });
});