<?php

namespace App\Services;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;
use App\Contracts\ModuleInterface;
use App\Models\SystemModule;
use App\Models\ModuleConfiguration;

class ModuleManagerService
{
    protected array $registeredModules = [];
    protected array $activeModules = [];
    protected string $modulesPath;

    public function __construct()
    {
        $this->modulesPath = app_path('Modules');
    }

    /**
     * Initialize module system
     */
    public function initializeModuleSystem(): array
    {
        try {
            Log::info("Initializing modular architecture system");

            // Discover available modules
            $discoveredModules = $this->discoverModules();
            
            // Register core modules
            $coreModules = $this->registerCoreModules();
            
            // Load active modules
            $activeModules = $this->loadActiveModules();
            
            // Initialize module configurations
            $configurations = $this->initializeModuleConfigurations();

            return [
                'success' => true,
                'discovered_modules' => count($discoveredModules),
                'core_modules' => count($coreModules),
                'active_modules' => count($activeModules),
                'configurations' => count($configurations),
                'message' => 'Module system initialized successfully',
            ];

        } catch (\Exception $e) {
            Log::error("Module system initialization failed", [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Register a new module
     */
    public function registerModule(string $moduleClass, array $config = []): bool
    {
        try {
            if (!class_exists($moduleClass)) {
                throw new \Exception("Module class not found: {$moduleClass}");
            }

            if (!in_array(ModuleInterface::class, class_implements($moduleClass))) {
                throw new \Exception("Module must implement ModuleInterface: {$moduleClass}");
            }

            $module = new $moduleClass();
            $moduleInfo = $module->getModuleInfo();

            // Check dependencies
            if (!$this->checkDependencies($moduleInfo['dependencies'] ?? [])) {
                throw new \Exception("Module dependencies not met: {$moduleClass}");
            }

            // Register module
            $this->registeredModules[$moduleInfo['name']] = [
                'class' => $moduleClass,
                'instance' => $module,
                'info' => $moduleInfo,
                'config' => $config,
                'registered_at' => now(),
            ];

            // Store in database
            SystemModule::updateOrCreate(
                ['module_name' => $moduleInfo['name']],
                [
                    'module_class' => $moduleClass,
                    'module_version' => $moduleInfo['version'],
                    'module_description' => $moduleInfo['description'],
                    'module_dependencies' => $moduleInfo['dependencies'] ?? [],
                    'is_core_module' => $moduleInfo['is_core'] ?? false,
                    'is_active' => false,
                    'configuration' => $config,
                ]
            );

            Log::info("Module registered successfully", [
                'module' => $moduleInfo['name'],
                'version' => $moduleInfo['version'],
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error("Module registration failed", [
                'module_class' => $moduleClass,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Activate a module
     */
    public function activateModule(string $moduleName): array
    {
        try {
            if (!isset($this->registeredModules[$moduleName])) {
                throw new \Exception("Module not registered: {$moduleName}");
            }

            $moduleData = $this->registeredModules[$moduleName];
            $module = $moduleData['instance'];

            // Check if already active
            if (isset($this->activeModules[$moduleName])) {
                return [
                    'success' => true,
                    'message' => 'Module already active',
                    'module' => $moduleName,
                ];
            }

            // Run module activation
            $activationResult = $module->activate();
            
            if (!$activationResult['success']) {
                throw new \Exception("Module activation failed: " . $activationResult['error']);
            }

            // Mark as active
            $this->activeModules[$moduleName] = $moduleData;

            // Update database
            SystemModule::where('module_name', $moduleName)
                ->update([
                    'is_active' => true,
                    'activated_at' => now(),
                ]);

            // Clear module cache
            Cache::forget("active_modules");

            Log::info("Module activated successfully", ['module' => $moduleName]);

            return [
                'success' => true,
                'module' => $moduleName,
                'activation_result' => $activationResult,
                'message' => 'Module activated successfully',
            ];

        } catch (\Exception $e) {
            Log::error("Module activation failed", [
                'module' => $moduleName,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Deactivate a module
     */
    public function deactivateModule(string $moduleName): array
    {
        try {
            if (!isset($this->activeModules[$moduleName])) {
                return [
                    'success' => true,
                    'message' => 'Module already inactive',
                    'module' => $moduleName,
                ];
            }

            $moduleData = $this->activeModules[$moduleName];
            $module = $moduleData['instance'];

            // Run module deactivation
            $deactivationResult = $module->deactivate();
            
            if (!$deactivationResult['success']) {
                throw new \Exception("Module deactivation failed: " . $deactivationResult['error']);
            }

            // Remove from active modules
            unset($this->activeModules[$moduleName]);

            // Update database
            SystemModule::where('module_name', $moduleName)
                ->update([
                    'is_active' => false,
                    'deactivated_at' => now(),
                ]);

            // Clear module cache
            Cache::forget("active_modules");

            Log::info("Module deactivated successfully", ['module' => $moduleName]);

            return [
                'success' => true,
                'module' => $moduleName,
                'deactivation_result' => $deactivationResult,
                'message' => 'Module deactivated successfully',
            ];

        } catch (\Exception $e) {
            Log::error("Module deactivation failed", [
                'module' => $moduleName,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get module configuration
     */
    public function getModuleConfiguration(string $moduleName): array
    {
        $configuration = ModuleConfiguration::where('module_name', $moduleName)->first();
        
        return $configuration ? $configuration->configuration : [];
    }

    /**
     * Update module configuration
     */
    public function updateModuleConfiguration(string $moduleName, array $config): bool
    {
        try {
            ModuleConfiguration::updateOrCreate(
                ['module_name' => $moduleName],
                [
                    'configuration' => $config,
                    'updated_at' => now(),
                ]
            );

            // Reload module if active
            if (isset($this->activeModules[$moduleName])) {
                $this->reloadModule($moduleName);
            }

            return true;

        } catch (\Exception $e) {
            Log::error("Module configuration update failed", [
                'module' => $moduleName,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get all available modules
     */
    public function getAvailableModules(): Collection
    {
        return SystemModule::all()->map(function ($module) {
            return [
                'name' => $module->module_name,
                'class' => $module->module_class,
                'version' => $module->module_version,
                'description' => $module->module_description,
                'is_core' => $module->is_core_module,
                'is_active' => $module->is_active,
                'dependencies' => $module->module_dependencies,
                'configuration' => $module->configuration,
            ];
        });
    }

    /**
     * Get active modules
     */
    public function getActiveModules(): Collection
    {
        return Cache::remember('active_modules', 3600, function () {
            return SystemModule::where('is_active', true)->get();
        });
    }

    /**
     * Execute module hook
     */
    public function executeHook(string $hookName, array $data = []): array
    {
        $results = [];

        foreach ($this->activeModules as $moduleName => $moduleData) {
            $module = $moduleData['instance'];
            
            if (method_exists($module, $hookName)) {
                try {
                    $result = $module->$hookName($data);
                    $results[$moduleName] = $result;
                } catch (\Exception $e) {
                    Log::error("Module hook execution failed", [
                        'module' => $moduleName,
                        'hook' => $hookName,
                        'error' => $e->getMessage(),
                    ]);
                    
                    $results[$moduleName] = [
                        'success' => false,
                        'error' => $e->getMessage(),
                    ];
                }
            }
        }

        return $results;
    }

    /**
     * Discover available modules
     */
    protected function discoverModules(): array
    {
        $modules = [];

        if (!File::exists($this->modulesPath)) {
            File::makeDirectory($this->modulesPath, 0755, true);
            return $modules;
        }

        $moduleDirectories = File::directories($this->modulesPath);

        foreach ($moduleDirectories as $directory) {
            $moduleName = basename($directory);
            $moduleFile = $directory . '/' . $moduleName . 'Module.php';

            if (File::exists($moduleFile)) {
                $modules[] = [
                    'name' => $moduleName,
                    'path' => $directory,
                    'file' => $moduleFile,
                ];
            }
        }

        return $modules;
    }

    /**
     * Register core modules
     */
    protected function registerCoreModules(): array
    {
        $coreModules = [
            'CarbonPricing' => \App\Modules\CarbonPricing\CarbonPricingModule::class,
            'ProductFootprint' => \App\Modules\ProductFootprint\ProductFootprintModule::class,
            'BiodiversityImpact' => \App\Modules\BiodiversityImpact\BiodiversityImpactModule::class,
            'PredictiveAnalytics' => \App\Modules\PredictiveAnalytics\PredictiveAnalyticsModule::class,
            'RegionalCompliance' => \App\Modules\RegionalCompliance\RegionalComplianceModule::class,
        ];

        $registered = [];

        foreach ($coreModules as $name => $class) {
            if (class_exists($class)) {
                if ($this->registerModule($class, ['is_core' => true])) {
                    $registered[] = $name;
                }
            }
        }

        return $registered;
    }

    /**
     * Load active modules
     */
    protected function loadActiveModules(): array
    {
        $activeModules = SystemModule::where('is_active', true)->get();
        $loaded = [];

        foreach ($activeModules as $moduleRecord) {
            if (class_exists($moduleRecord->module_class)) {
                $module = new $moduleRecord->module_class();
                
                $this->activeModules[$moduleRecord->module_name] = [
                    'class' => $moduleRecord->module_class,
                    'instance' => $module,
                    'info' => $module->getModuleInfo(),
                    'config' => $moduleRecord->configuration,
                ];
                
                $loaded[] = $moduleRecord->module_name;
            }
        }

        return $loaded;
    }

    /**
     * Initialize module configurations
     */
    protected function initializeModuleConfigurations(): array
    {
        $configurations = [];

        foreach ($this->registeredModules as $moduleName => $moduleData) {
            $module = $moduleData['instance'];
            
            if (method_exists($module, 'getDefaultConfiguration')) {
                $defaultConfig = $module->getDefaultConfiguration();
                
                ModuleConfiguration::firstOrCreate(
                    ['module_name' => $moduleName],
                    ['configuration' => $defaultConfig]
                );
                
                $configurations[] = $moduleName;
            }
        }

        return $configurations;
    }

    /**
     * Check module dependencies
     */
    protected function checkDependencies(array $dependencies): bool
    {
        foreach ($dependencies as $dependency) {
            if (!isset($this->registeredModules[$dependency])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Reload a module
     */
    protected function reloadModule(string $moduleName): bool
    {
        try {
            $this->deactivateModule($moduleName);
            $this->activateModule($moduleName);
            
            return true;
        } catch (\Exception $e) {
            Log::error("Module reload failed", [
                'module' => $moduleName,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }
}
