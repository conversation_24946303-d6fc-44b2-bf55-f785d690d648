<?php

namespace App\Services;

use App\Models\ActivityData;
use App\Models\Facility;
use App\Models\EmissionSource;
use App\Models\MeasurementUnit;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class ERPIntegrationService
{
    protected array $connections = [];
    protected DataLakeService $dataLakeService;

    public function __construct(DataLakeService $dataLakeService)
    {
        $this->dataLakeService = $dataLakeService;
    }

    /**
     * Connect to SAP ERP system
     */
    public function connectSAP(array $credentials): bool
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Basic ' . base64_encode($credentials['username'] . ':' . $credentials['password']),
                'Content-Type' => 'application/json',
            ])->timeout(30)->get($credentials['endpoint'] . '/api/test-connection');

            if ($response->successful()) {
                $this->connections['sap'] = [
                    'endpoint' => $credentials['endpoint'],
                    'credentials' => $credentials,
                    'connected_at' => now(),
                    'status' => 'active'
                ];

                Cache::put('erp_connection_sap', $this->connections['sap'], 3600);
                Log::info('SAP ERP connection established successfully');
                return true;
            }

            Log::error('SAP ERP connection failed', ['response' => $response->body()]);
            return false;

        } catch (\Exception $e) {
            Log::error('SAP ERP connection error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Connect to Oracle ERP system
     */
    public function connectOracle(array $credentials): bool
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $credentials['token'],
                'Content-Type' => 'application/json',
            ])->timeout(30)->get($credentials['endpoint'] . '/fscmRestApi/resources/11.13.18.05/test');

            if ($response->successful()) {
                $this->connections['oracle'] = [
                    'endpoint' => $credentials['endpoint'],
                    'credentials' => $credentials,
                    'connected_at' => now(),
                    'status' => 'active'
                ];

                Cache::put('erp_connection_oracle', $this->connections['oracle'], 3600);
                Log::info('Oracle ERP connection established successfully');
                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::error('Oracle ERP connection error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Sync energy data from ERP system
     */
    public function syncEnergyData(string $facilityId): array
    {
        $facility = Facility::find($facilityId);
        if (!$facility) {
            throw new \InvalidArgumentException("Facility not found: {$facilityId}");
        }

        $results = [];
        
        // Try SAP first
        if ($this->isConnected('sap')) {
            $results['sap'] = $this->syncSAPEnergyData($facility);
        }

        // Try Oracle if SAP fails or is not connected
        if ($this->isConnected('oracle') && empty($results['sap'])) {
            $results['oracle'] = $this->syncOracleEnergyData($facility);
        }

        return $results;
    }

    /**
     * Pull activity data from ERP systems
     */
    public function pullActivityData(Carbon $fromDate, Carbon $toDate): Collection
    {
        $allData = collect();

        // Pull from all connected ERP systems
        foreach ($this->getActiveConnections() as $system => $connection) {
            try {
                $data = $this->pullFromSystem($system, $fromDate, $toDate);
                $allData = $allData->merge($data);
                
                Log::info("Pulled {$data->count()} records from {$system}");
            } catch (\Exception $e) {
                Log::error("Failed to pull data from {$system}: " . $e->getMessage());
            }
        }

        return $allData;
    }

    /**
     * Sync SAP energy data
     */
    protected function syncSAPEnergyData(Facility $facility): array
    {
        $connection = $this->connections['sap'] ?? Cache::get('erp_connection_sap');
        if (!$connection) {
            return [];
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Basic ' . base64_encode(
                    $connection['credentials']['username'] . ':' . $connection['credentials']['password']
                ),
            ])->get($connection['endpoint'] . '/api/energy-consumption', [
                'plant_code' => $facility->external_id,
                'from_date' => now()->subMonth()->format('Y-m-d'),
                'to_date' => now()->format('Y-m-d'),
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $this->processEnergyData($data, $facility, 'sap');
            }

            return [];

        } catch (\Exception $e) {
            Log::error('SAP energy data sync failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Sync Oracle energy data
     */
    protected function syncOracleEnergyData(Facility $facility): array
    {
        $connection = $this->connections['oracle'] ?? Cache::get('erp_connection_oracle');
        if (!$connection) {
            return [];
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $connection['credentials']['token'],
            ])->get($connection['endpoint'] . '/fscmRestApi/resources/11.13.18.05/energyConsumption', [
                'q' => "FacilityId='{$facility->external_id}'",
                'fromDate' => now()->subMonth()->format('Y-m-d'),
                'toDate' => now()->format('Y-m-d'),
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $this->processEnergyData($data['items'] ?? [], $facility, 'oracle');
            }

            return [];

        } catch (\Exception $e) {
            Log::error('Oracle energy data sync failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Process energy data from ERP systems
     */
    protected function processEnergyData(array $data, Facility $facility, string $source): array
    {
        $processed = [];

        foreach ($data as $record) {
            try {
                // Store raw data in data lake
                $dataId = $this->dataLakeService->storeRawData($source, $record, [
                    'facility_id' => $facility->id,
                    'data_type' => 'energy_consumption',
                    'source_system' => $source,
                ]);

                // Normalize and create activity data
                $activityData = $this->normalizeEnergyRecord($record, $facility, $source);
                if ($activityData) {
                    $processed[] = $activityData;
                }

            } catch (\Exception $e) {
                Log::error("Failed to process energy record from {$source}: " . $e->getMessage());
            }
        }

        return $processed;
    }

    /**
     * Normalize energy record to ActivityData format
     */
    protected function normalizeEnergyRecord(array $record, Facility $facility, string $source): ?array
    {
        // Map common ERP fields to our schema
        $mapping = [
            'sap' => [
                'quantity' => 'CONSUMPTION_QTY',
                'unit' => 'UOM',
                'date' => 'CONSUMPTION_DATE',
                'material' => 'MATERIAL_CODE',
            ],
            'oracle' => [
                'quantity' => 'ConsumptionQuantity',
                'unit' => 'UnitOfMeasure',
                'date' => 'ConsumptionDate',
                'material' => 'MaterialCode',
            ],
        ];

        $map = $mapping[$source] ?? [];
        
        if (!isset($record[$map['quantity']]) || !isset($record[$map['date']])) {
            return null;
        }

        // Find or create emission source
        $emissionSource = EmissionSource::firstOrCreate([
            'name' => $this->mapMaterialToSource($record[$map['material']] ?? 'Unknown'),
        ], [
            'scope' => '2', // Typically Scope 2 for purchased energy
            'description' => "Auto-imported from {$source}",
        ]);

        // Find or create measurement unit
        $unit = MeasurementUnit::firstOrCreate([
            'symbol' => $record[$map['unit']] ?? 'kWh',
        ], [
            'name' => $this->expandUnitName($record[$map['unit']] ?? 'kWh'),
            'unit_type' => 'Energy',
            'is_active' => true,
        ]);

        return [
            'facility_id' => $facility->id,
            'organization_id' => $facility->organization_id,
            'source_id' => $emissionSource->id,
            'quantity' => (float) $record[$map['quantity']],
            'activity_unit_id' => $unit->id,
            'date_recorded' => Carbon::parse($record[$map['date']]),
            'date_start' => Carbon::parse($record[$map['date']])->startOfDay(),
            'date_end' => Carbon::parse($record[$map['date']])->endOfDay(),
            'scope' => '2',
            'material_type' => $record[$map['material']] ?? 'Electricity',
            'notes' => "Auto-imported from {$source} ERP system",
            'recorded_by' => "system_{$source}",
        ];
    }

    /**
     * Check if system is connected
     */
    protected function isConnected(string $system): bool
    {
        $connection = $this->connections[$system] ?? Cache::get("erp_connection_{$system}");
        return $connection && $connection['status'] === 'active';
    }

    /**
     * Get all active connections
     */
    protected function getActiveConnections(): array
    {
        $active = [];
        
        foreach (['sap', 'oracle'] as $system) {
            if ($this->isConnected($system)) {
                $active[$system] = $this->connections[$system] ?? Cache::get("erp_connection_{$system}");
            }
        }

        return $active;
    }

    /**
     * Pull data from specific system
     */
    protected function pullFromSystem(string $system, Carbon $fromDate, Carbon $toDate): Collection
    {
        switch ($system) {
            case 'sap':
                return $this->pullSAPData($fromDate, $toDate);
            case 'oracle':
                return $this->pullOracleData($fromDate, $toDate);
            default:
                return collect();
        }
    }

    /**
     * Pull data from SAP
     */
    protected function pullSAPData(Carbon $fromDate, Carbon $toDate): Collection
    {
        // Implementation for SAP data pulling
        return collect();
    }

    /**
     * Pull data from Oracle
     */
    protected function pullOracleData(Carbon $fromDate, Carbon $toDate): Collection
    {
        // Implementation for Oracle data pulling
        return collect();
    }

    /**
     * Map material codes to emission sources
     */
    protected function mapMaterialToSource(string $material): string
    {
        $mapping = [
            'ELEC' => 'Electricity',
            'GAS' => 'Natural Gas',
            'FUEL' => 'Fuel Oil',
            'COAL' => 'Coal',
            'STEAM' => 'Steam',
        ];

        foreach ($mapping as $code => $source) {
            if (str_contains(strtoupper($material), $code)) {
                return $source;
            }
        }

        return 'Energy Consumption';
    }

    /**
     * Expand unit abbreviations
     */
    protected function expandUnitName(string $unit): string
    {
        $mapping = [
            'kWh' => 'Kilowatt Hour',
            'MWh' => 'Megawatt Hour',
            'GJ' => 'Gigajoule',
            'TJ' => 'Terajoule',
            'm3' => 'Cubic Meter',
            'L' => 'Liter',
            'kg' => 'Kilogram',
            't' => 'Tonne',
        ];

        return $mapping[$unit] ?? $unit;
    }

    /**
     * Test connection to ERP system
     */
    public function testConnection(string $system): array
    {
        if (!$this->isConnected($system)) {
            return ['status' => 'disconnected', 'message' => 'No active connection'];
        }

        try {
            $connection = $this->connections[$system] ?? Cache::get("erp_connection_{$system}");
            
            $response = Http::timeout(10)->get($connection['endpoint'] . '/api/health');
            
            if ($response->successful()) {
                return ['status' => 'connected', 'message' => 'Connection healthy'];
            }

            return ['status' => 'error', 'message' => 'Connection failed'];

        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }
}
