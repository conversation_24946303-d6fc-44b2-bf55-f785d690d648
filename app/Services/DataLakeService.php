<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class DataLakeService
{
    protected string $disk = 'data-lake';
    protected array $compressionTypes = ['gzip', 'none'];
    protected array $retentionPolicies = [
        'raw_data' => 2555, // 7 years in days
        'processed_data' => 1825, // 5 years in days
        'aggregated_data' => 3650, // 10 years in days
    ];

    /**
     * Store raw data in the data lake
     */
    public function storeRawData(string $source, array $data, array $metadata = []): string
    {
        $dataId = $this->generateDataId($source);
        $timestamp = now();
        
        $dataRecord = [
            'id' => $dataId,
            'source' => $source,
            'data_type' => $metadata['data_type'] ?? 'unknown',
            'facility_id' => $metadata['facility_id'] ?? null,
            'organization_id' => $metadata['organization_id'] ?? null,
            'raw_data' => json_encode($data),
            'metadata' => json_encode($metadata),
            'file_path' => null,
            'file_size' => null,
            'compression' => 'none',
            'checksum' => md5(json_encode($data)),
            'version' => 1,
            'created_at' => $timestamp,
            'updated_at' => $timestamp,
            'archived_at' => null,
            'retention_until' => $timestamp->copy()->addDays($this->retentionPolicies['raw_data']),
        ];

        // Store in database
        DB::table('data_lake_entries')->insert($dataRecord);

        // Store file in cloud storage for large datasets
        if (strlen(json_encode($data)) > 1024 * 100) { // > 100KB
            $filePath = $this->storeDataFile($dataId, $data, $metadata);
            if ($filePath) {
                DB::table('data_lake_entries')
                    ->where('id', $dataId)
                    ->update([
                        'file_path' => $filePath,
                        'file_size' => Storage::disk($this->disk)->size($filePath),
                    ]);
            }
        }

        Log::info("Data stored in data lake", [
            'data_id' => $dataId,
            'source' => $source,
            'size' => strlen(json_encode($data)),
        ]);

        return $dataId;
    }

    /**
     * Version existing data
     */
    public function versionData(string $dataId, array $changes): string
    {
        $existingData = DB::table('data_lake_entries')->where('id', $dataId)->first();
        
        if (!$existingData) {
            throw new \InvalidArgumentException("Data not found: {$dataId}");
        }

        $newVersion = $existingData->version + 1;
        $newDataId = $dataId . '_v' . $newVersion;
        
        $originalData = json_decode($existingData->raw_data, true);
        $originalMetadata = json_decode($existingData->metadata, true);
        
        // Apply changes
        $updatedData = array_merge($originalData, $changes['data'] ?? []);
        $updatedMetadata = array_merge($originalMetadata, $changes['metadata'] ?? []);
        $updatedMetadata['version_of'] = $dataId;
        $updatedMetadata['changes'] = $changes;

        $versionRecord = [
            'id' => $newDataId,
            'source' => $existingData->source,
            'data_type' => $existingData->data_type,
            'facility_id' => $existingData->facility_id,
            'organization_id' => $existingData->organization_id,
            'raw_data' => json_encode($updatedData),
            'metadata' => json_encode($updatedMetadata),
            'file_path' => null,
            'file_size' => null,
            'compression' => 'none',
            'checksum' => md5(json_encode($updatedData)),
            'version' => $newVersion,
            'created_at' => now(),
            'updated_at' => now(),
            'archived_at' => null,
            'retention_until' => Carbon::parse($existingData->retention_until),
        ];

        DB::table('data_lake_entries')->insert($versionRecord);

        // Store version relationship
        DB::table('data_lake_versions')->insert([
            'original_id' => $dataId,
            'version_id' => $newDataId,
            'version_number' => $newVersion,
            'changes_summary' => json_encode($changes),
            'created_at' => now(),
        ]);

        Log::info("Data versioned in data lake", [
            'original_id' => $dataId,
            'version_id' => $newDataId,
            'version' => $newVersion,
        ]);

        return $newDataId;
    }

    /**
     * Query raw data from the data lake
     */
    public function queryRawData(array $criteria): Collection
    {
        $query = DB::table('data_lake_entries');

        // Apply filters
        if (isset($criteria['source'])) {
            $query->where('source', $criteria['source']);
        }

        if (isset($criteria['data_type'])) {
            $query->where('data_type', $criteria['data_type']);
        }

        if (isset($criteria['facility_id'])) {
            $query->where('facility_id', $criteria['facility_id']);
        }

        if (isset($criteria['organization_id'])) {
            $query->where('organization_id', $criteria['organization_id']);
        }

        if (isset($criteria['from_date'])) {
            $query->where('created_at', '>=', $criteria['from_date']);
        }

        if (isset($criteria['to_date'])) {
            $query->where('created_at', '<=', $criteria['to_date']);
        }

        if (isset($criteria['latest_version_only']) && $criteria['latest_version_only']) {
            $query->whereRaw('id NOT LIKE "%_v%"');
        }

        // Apply ordering
        $query->orderBy('created_at', 'desc');

        // Apply limit
        if (isset($criteria['limit'])) {
            $query->limit($criteria['limit']);
        }

        $results = $query->get();

        return $results->map(function ($record) {
            return $this->hydrateDataRecord($record);
        });
    }

    /**
     * Archive old data based on retention policies
     */
    public function archiveOldData(Carbon $cutoffDate = null): bool
    {
        if (!$cutoffDate) {
            $cutoffDate = now()->subDays(min($this->retentionPolicies));
        }

        try {
            // Find records to archive
            $recordsToArchive = DB::table('data_lake_entries')
                ->where('retention_until', '<', $cutoffDate)
                ->whereNull('archived_at')
                ->get();

            $archivedCount = 0;

            foreach ($recordsToArchive as $record) {
                // Move file to archive storage if exists
                if ($record->file_path) {
                    $archivePath = 'archive/' . $record->file_path;
                    if (Storage::disk($this->disk)->exists($record->file_path)) {
                        Storage::disk($this->disk)->move($record->file_path, $archivePath);
                    }
                }

                // Update record as archived
                DB::table('data_lake_entries')
                    ->where('id', $record->id)
                    ->update([
                        'archived_at' => now(),
                        'file_path' => $record->file_path ? 'archive/' . $record->file_path : null,
                    ]);

                $archivedCount++;
            }

            Log::info("Archived {$archivedCount} data lake records");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to archive data lake records: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get data lake statistics
     */
    public function getStatistics(): array
    {
        $stats = [
            'total_records' => DB::table('data_lake_entries')->count(),
            'total_size_mb' => DB::table('data_lake_entries')->sum('file_size') / (1024 * 1024),
            'archived_records' => DB::table('data_lake_entries')->whereNotNull('archived_at')->count(),
            'sources' => DB::table('data_lake_entries')->distinct('source')->count('source'),
            'data_types' => DB::table('data_lake_entries')->distinct('data_type')->count('data_type'),
        ];

        // Records by source
        $stats['by_source'] = DB::table('data_lake_entries')
            ->select('source', DB::raw('COUNT(*) as count'))
            ->groupBy('source')
            ->get()
            ->pluck('count', 'source')
            ->toArray();

        // Records by data type
        $stats['by_data_type'] = DB::table('data_lake_entries')
            ->select('data_type', DB::raw('COUNT(*) as count'))
            ->groupBy('data_type')
            ->get()
            ->pluck('count', 'data_type')
            ->toArray();

        // Recent activity (last 30 days)
        $stats['recent_activity'] = DB::table('data_lake_entries')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        return $stats;
    }

    /**
     * Retrieve specific data by ID
     */
    public function retrieveData(string $dataId): ?array
    {
        $record = DB::table('data_lake_entries')->where('id', $dataId)->first();
        
        if (!$record) {
            return null;
        }

        return $this->hydrateDataRecord($record);
    }

    /**
     * Delete data from data lake
     */
    public function deleteData(string $dataId): bool
    {
        $record = DB::table('data_lake_entries')->where('id', $dataId)->first();
        
        if (!$record) {
            return false;
        }

        try {
            // Delete file if exists
            if ($record->file_path && Storage::disk($this->disk)->exists($record->file_path)) {
                Storage::disk($this->disk)->delete($record->file_path);
            }

            // Delete database record
            DB::table('data_lake_entries')->where('id', $dataId)->delete();
            
            // Delete version relationships
            DB::table('data_lake_versions')->where('original_id', $dataId)->delete();
            DB::table('data_lake_versions')->where('version_id', $dataId)->delete();

            Log::info("Deleted data from data lake: {$dataId}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to delete data from data lake: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate unique data ID
     */
    protected function generateDataId(string $source): string
    {
        $timestamp = now()->format('YmdHis');
        $random = Str::random(8);
        return "{$source}_{$timestamp}_{$random}";
    }

    /**
     * Store data file in cloud storage
     */
    protected function storeDataFile(string $dataId, array $data, array $metadata): ?string
    {
        try {
            $year = now()->year;
            $month = now()->format('m');
            $day = now()->format('d');
            
            $filePath = "raw-data/{$year}/{$month}/{$day}/{$dataId}.json";
            
            $fileContent = json_encode([
                'data' => $data,
                'metadata' => $metadata,
                'stored_at' => now()->toISOString(),
            ], JSON_PRETTY_PRINT);

            Storage::disk($this->disk)->put($filePath, $fileContent);
            
            return $filePath;

        } catch (\Exception $e) {
            Log::error("Failed to store data file: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Hydrate data record with actual data
     */
    protected function hydrateDataRecord($record): array
    {
        $data = [
            'id' => $record->id,
            'source' => $record->source,
            'data_type' => $record->data_type,
            'facility_id' => $record->facility_id,
            'organization_id' => $record->organization_id,
            'metadata' => json_decode($record->metadata, true),
            'version' => $record->version,
            'created_at' => $record->created_at,
            'updated_at' => $record->updated_at,
            'archived_at' => $record->archived_at,
            'checksum' => $record->checksum,
        ];

        // Load actual data
        if ($record->file_path && Storage::disk($this->disk)->exists($record->file_path)) {
            // Load from file
            $fileContent = Storage::disk($this->disk)->get($record->file_path);
            $fileData = json_decode($fileContent, true);
            $data['raw_data'] = $fileData['data'] ?? [];
        } else {
            // Load from database
            $data['raw_data'] = json_decode($record->raw_data, true);
        }

        return $data;
    }

    /**
     * Get data versions
     */
    public function getDataVersions(string $dataId): Collection
    {
        return DB::table('data_lake_versions')
            ->where('original_id', $dataId)
            ->orderBy('version_number', 'desc')
            ->get();
    }

    /**
     * Cleanup expired data
     */
    public function cleanupExpiredData(): int
    {
        $expiredRecords = DB::table('data_lake_entries')
            ->where('retention_until', '<', now())
            ->whereNotNull('archived_at')
            ->where('archived_at', '<', now()->subDays(90)) // Keep archived data for 90 days
            ->get();

        $deletedCount = 0;

        foreach ($expiredRecords as $record) {
            if ($this->deleteData($record->id)) {
                $deletedCount++;
            }
        }

        Log::info("Cleaned up {$deletedCount} expired data lake records");
        return $deletedCount;
    }
}
