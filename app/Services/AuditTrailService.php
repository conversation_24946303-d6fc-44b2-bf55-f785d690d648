<?php

namespace App\Services;

use App\Models\Organization;
use App\Models\ActivityData;
use App\Models\EmissionCalculation;
use App\Models\AuditLog;
use App\Models\DataLineage;
use App\Models\CalculationAuditTrail;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;

class AuditTrailService
{
    /**
     * Generate comprehensive audit trail for reporting
     */
    public function generateReportAuditTrail(Organization $organization, int $reportingYear, string $framework): array
    {
        try {
            $auditTrail = [
                'organization_id' => $organization->id,
                'reporting_year' => $reportingYear,
                'framework' => $framework,
                'generated_at' => now(),
                'data_sources' => $this->traceDataSources($organization, $reportingYear),
                'calculation_methods' => $this->traceCalculationMethods($organization, $reportingYear),
                'emission_factors' => $this->traceEmissionFactors($organization, $reportingYear),
                'data_lineage' => $this->generateDataLineage($organization, $reportingYear),
                'validation_steps' => $this->traceValidationSteps($organization, $reportingYear),
                'user_actions' => $this->traceUserActions($organization, $reportingYear),
                'system_changes' => $this->traceSystemChanges($organization, $reportingYear),
                'integrity_hash' => null, // Will be calculated after all data is gathered
            ];

            // Calculate integrity hash for immutability
            $auditTrail['integrity_hash'] = $this->calculateIntegrityHash($auditTrail);

            return $auditTrail;

        } catch (\Exception $e) {
            Log::error('Audit trail generation failed', [
                'organization_id' => $organization->id,
                'reporting_year' => $reportingYear,
                'error' => $e->getMessage(),
            ]);

            return [
                'error' => 'Audit trail generation failed: ' . $e->getMessage(),
                'organization_id' => $organization->id,
                'reporting_year' => $reportingYear,
                'generated_at' => now(),
            ];
        }
    }

    /**
     * Trace data sources for all emissions data
     */
    protected function traceDataSources(Organization $organization, int $reportingYear): array
    {
        $activityData = ActivityData::where('organization_id', $organization->id)
            ->whereYear('date_recorded', $reportingYear)
            ->with(['dataLakeRecord', 'facility', 'emissionSource'])
            ->get();

        $dataSources = [];

        foreach ($activityData as $activity) {
            $sourceKey = $this->generateSourceKey($activity);
            
            if (!isset($dataSources[$sourceKey])) {
                $dataSources[$sourceKey] = [
                    'source_type' => $this->identifySourceType($activity),
                    'source_system' => $this->identifySourceSystem($activity),
                    'facility_id' => $activity->facility_id,
                    'facility_name' => $activity->facility->name ?? 'Unknown',
                    'data_points' => 0,
                    'first_recorded' => $activity->date_recorded,
                    'last_recorded' => $activity->date_recorded,
                    'data_quality' => $activity->data_quality_rating ?? 'Unknown',
                    'validation_status' => $this->getValidationStatus($activity),
                    'lineage_trace' => $this->traceDataLineage($activity),
                ];
            }

            $dataSources[$sourceKey]['data_points']++;
            
            if ($activity->date_recorded < $dataSources[$sourceKey]['first_recorded']) {
                $dataSources[$sourceKey]['first_recorded'] = $activity->date_recorded;
            }
            
            if ($activity->date_recorded > $dataSources[$sourceKey]['last_recorded']) {
                $dataSources[$sourceKey]['last_recorded'] = $activity->date_recorded;
            }
        }

        return array_values($dataSources);
    }

    /**
     * Trace calculation methods used
     */
    protected function traceCalculationMethods(Organization $organization, int $reportingYear): array
    {
        $calculations = EmissionCalculation::whereHas('activityData', function ($query) use ($organization, $reportingYear) {
            $query->where('organization_id', $organization->id)
                  ->whereYear('date_recorded', $reportingYear);
        })->with(['activityData', 'emissionFactor'])->get();

        $methods = [];

        foreach ($calculations as $calculation) {
            $methodKey = $this->generateMethodKey($calculation);
            
            if (!isset($methods[$methodKey])) {
                $methods[$methodKey] = [
                    'calculation_method' => $calculation->calculation_method,
                    'methodology_framework' => $calculation->methodology_framework ?? 'GHG Protocol',
                    'tier_level' => $calculation->tier_level ?? 'Tier 2',
                    'scope' => $calculation->activityData->scope,
                    'emission_type' => $calculation->activityData->emission_type ?? 'combustion',
                    'calculations_count' => 0,
                    'total_emissions' => 0,
                    'factor_sources' => [],
                    'calculation_steps' => $this->traceCalculationSteps($calculation),
                    'quality_indicators' => $this->getCalculationQualityIndicators($calculation),
                ];
            }

            $methods[$methodKey]['calculations_count']++;
            $methods[$methodKey]['total_emissions'] += $calculation->total_co2e;
            
            // Track factor sources
            if ($calculation->emissionFactor) {
                $factorSource = $calculation->emissionFactor->data_source ?? 'Unknown';
                if (!in_array($factorSource, $methods[$methodKey]['factor_sources'])) {
                    $methods[$methodKey]['factor_sources'][] = $factorSource;
                }
            }
        }

        return array_values($methods);
    }

    /**
     * Trace emission factors used
     */
    protected function traceEmissionFactors(Organization $organization, int $reportingYear): array
    {
        $factorUsage = EmissionCalculation::whereHas('activityData', function ($query) use ($organization, $reportingYear) {
            $query->where('organization_id', $organization->id)
                  ->whereYear('date_recorded', $reportingYear);
        })->with(['emissionFactor.ghgProtocolSector', 'emissionFactor.ghgProtocolActivity'])
          ->get()
          ->groupBy('emission_factor_id');

        $factors = [];

        foreach ($factorUsage as $factorId => $calculations) {
            $factor = $calculations->first()->emissionFactor;
            
            if (!$factor) continue;

            $factors[] = [
                'factor_id' => $factorId,
                'factor_name' => $factor->name,
                'factor_value' => $factor->factor_value,
                'factor_unit' => $factor->unit,
                'data_source' => $factor->data_source,
                'source_reference' => $factor->source_reference,
                'vintage_year' => $factor->vintage_year,
                'data_quality_rating' => $factor->data_quality_rating,
                'uncertainty_range' => $factor->uncertainty_range,
                'ghg_protocol_sector' => $factor->ghgProtocolSector->name ?? null,
                'ghg_protocol_activity' => $factor->ghgProtocolActivity->name ?? null,
                'usage_count' => $calculations->count(),
                'total_activity_data' => $calculations->sum(function ($calc) {
                    return $calc->activityData->quantity ?? 0;
                }),
                'total_emissions_calculated' => $calculations->sum('total_co2e'),
                'first_used' => $calculations->min(function ($calc) {
                    return $calc->activityData->date_recorded;
                }),
                'last_used' => $calculations->max(function ($calc) {
                    return $calc->activityData->date_recorded;
                }),
                'provenance' => $this->traceFactorProvenance($factor),
            ];
        }

        return $factors;
    }

    /**
     * Generate comprehensive data lineage
     */
    protected function generateDataLineage(Organization $organization, int $reportingYear): array
    {
        $lineageRecords = DataLineage::whereHas('activityData', function ($query) use ($organization, $reportingYear) {
            $query->where('organization_id', $organization->id)
                  ->whereYear('date_recorded', $reportingYear);
        })->with(['activityData', 'sourceSystem'])->get();

        $lineage = [
            'total_records' => $lineageRecords->count(),
            'source_systems' => $lineageRecords->groupBy('source_system_type')->map(function ($records, $systemType) {
                return [
                    'system_type' => $systemType,
                    'record_count' => $records->count(),
                    'first_ingestion' => $records->min('ingestion_timestamp'),
                    'last_ingestion' => $records->max('ingestion_timestamp'),
                    'data_transformations' => $records->pluck('transformation_steps')->flatten()->unique()->values(),
                ];
            })->values(),
            'transformation_pipeline' => $this->traceTransformationPipeline($lineageRecords),
            'data_quality_journey' => $this->traceDataQualityJourney($lineageRecords),
        ];

        return $lineage;
    }

    /**
     * Trace validation steps performed
     */
    protected function traceValidationSteps(Organization $organization, int $reportingYear): array
    {
        // This would integrate with a validation log system
        return [
            'data_validation' => [
                'completeness_checks' => $this->getCompletenessValidation($organization, $reportingYear),
                'accuracy_checks' => $this->getAccuracyValidation($organization, $reportingYear),
                'consistency_checks' => $this->getConsistencyValidation($organization, $reportingYear),
                'timeliness_checks' => $this->getTimelinessValidation($organization, $reportingYear),
            ],
            'calculation_validation' => [
                'formula_verification' => $this->getFormulaVerification($organization, $reportingYear),
                'factor_validation' => $this->getFactorValidation($organization, $reportingYear),
                'unit_conversion_validation' => $this->getUnitConversionValidation($organization, $reportingYear),
            ],
            'business_rule_validation' => [
                'scope_boundary_checks' => $this->getScopeBoundaryValidation($organization, $reportingYear),
                'materiality_thresholds' => $this->getMaterialityValidation($organization, $reportingYear),
                'temporal_consistency' => $this->getTemporalConsistencyValidation($organization, $reportingYear),
            ],
        ];
    }

    /**
     * Trace user actions affecting the data
     */
    protected function traceUserActions(Organization $organization, int $reportingYear): array
    {
        $auditLogs = AuditLog::where('organization_id', $organization->id)
            ->whereBetween('created_at', [
                Carbon::create($reportingYear, 1, 1),
                Carbon::create($reportingYear, 12, 31, 23, 59, 59)
            ])
            ->orderBy('created_at')
            ->get();

        return $auditLogs->map(function ($log) {
            return [
                'timestamp' => $log->created_at,
                'user_id' => $log->user_id,
                'user_name' => $log->user_name,
                'action' => $log->action,
                'table_name' => $log->table_name,
                'record_id' => $log->record_id,
                'changes' => [
                    'old_values' => $log->old_values,
                    'new_values' => $log->new_values,
                ],
                'ip_address' => $log->ip_address,
                'session_id' => $log->session_id ?? null,
            ];
        })->toArray();
    }

    /**
     * Trace system changes and updates
     */
    protected function traceSystemChanges(Organization $organization, int $reportingYear): array
    {
        // This would track system-level changes like factor updates, methodology changes, etc.
        return [
            'emission_factor_updates' => $this->getFactorUpdates($reportingYear),
            'methodology_changes' => $this->getMethodologyChanges($reportingYear),
            'system_configuration_changes' => $this->getSystemConfigChanges($reportingYear),
            'data_source_changes' => $this->getDataSourceChanges($organization, $reportingYear),
        ];
    }

    /**
     * Calculate integrity hash for immutable audit trail
     */
    protected function calculateIntegrityHash(array $auditTrail): string
    {
        // Remove the integrity_hash field itself from the calculation
        $dataForHash = $auditTrail;
        unset($dataForHash['integrity_hash']);
        
        // Create a deterministic string representation
        $dataString = json_encode($dataForHash, JSON_SORT_KEYS);
        
        // Generate SHA-256 hash
        return hash('sha256', $dataString);
    }

    /**
     * Verify audit trail integrity
     */
    public function verifyAuditTrailIntegrity(array $auditTrail): bool
    {
        $storedHash = $auditTrail['integrity_hash'] ?? null;
        
        if (!$storedHash) {
            return false;
        }

        $calculatedHash = $this->calculateIntegrityHash($auditTrail);
        
        return hash_equals($storedHash, $calculatedHash);
    }

    /**
     * Create immutable audit log entry
     */
    public function createImmutableAuditEntry(array $data): string
    {
        $entry = [
            'timestamp' => now()->toISOString(),
            'data' => $data,
            'previous_hash' => $this->getLastAuditEntryHash(),
        ];

        $entryHash = $this->calculateIntegrityHash($entry);
        $entry['hash'] = $entryHash;

        // Store the entry (this would typically go to a blockchain or immutable storage)
        $this->storeImmutableEntry($entry);

        return $entryHash;
    }

    /**
     * Helper methods for tracing various aspects
     */
    protected function generateSourceKey(ActivityData $activity): string
    {
        return md5($activity->facility_id . '_' . ($activity->emissionSource->name ?? 'unknown') . '_' . $activity->scope);
    }

    protected function identifySourceType(ActivityData $activity): string
    {
        if ($activity->dataLakeRecord) {
            return $activity->dataLakeRecord->source_type ?? 'manual';
        }
        return 'manual';
    }

    protected function identifySourceSystem(ActivityData $activity): string
    {
        if ($activity->dataLakeRecord) {
            return $activity->dataLakeRecord->source_system ?? 'manual_entry';
        }
        return 'manual_entry';
    }

    protected function getValidationStatus(ActivityData $activity): string
    {
        // This would check various validation flags
        return 'validated'; // Placeholder
    }

    protected function traceDataLineage(ActivityData $activity): array
    {
        // This would trace the complete lineage of the data point
        return [
            'original_source' => $this->identifySourceSystem($activity),
            'ingestion_method' => 'api', // Placeholder
            'transformation_steps' => [], // Placeholder
            'validation_steps' => [], // Placeholder
        ];
    }

    protected function generateMethodKey(EmissionCalculation $calculation): string
    {
        return md5($calculation->calculation_method . '_' . $calculation->activityData->scope . '_' . ($calculation->activityData->emission_type ?? 'default'));
    }

    protected function traceCalculationSteps(EmissionCalculation $calculation): array
    {
        // This would trace the detailed calculation steps
        return [
            'step_1' => 'Activity data validation',
            'step_2' => 'Emission factor selection',
            'step_3' => 'Unit conversion',
            'step_4' => 'Emission calculation',
            'step_5' => 'Result validation',
        ];
    }

    protected function getCalculationQualityIndicators(EmissionCalculation $calculation): array
    {
        return [
            'data_quality' => $calculation->activityData->data_quality_rating ?? 'Unknown',
            'factor_quality' => $calculation->emissionFactor->data_quality_rating ?? 'Unknown',
            'uncertainty' => $calculation->uncertainty_percentage ?? null,
            'tier_level' => $calculation->tier_level ?? 'Tier 2',
        ];
    }

    protected function traceFactorProvenance(object $factor): array
    {
        return [
            'original_source' => $factor->data_source,
            'import_date' => $factor->created_at,
            'last_updated' => $factor->updated_at,
            'version' => $factor->version ?? '1.0',
            'validation_status' => 'validated',
        ];
    }

    protected function traceTransformationPipeline(Collection $lineageRecords): array
    {
        return [
            'ingestion' => 'Raw data ingestion from source systems',
            'validation' => 'Data quality validation and cleansing',
            'normalization' => 'Unit conversion and standardization',
            'enrichment' => 'Factor matching and metadata addition',
            'calculation' => 'Emission calculation processing',
        ];
    }

    protected function traceDataQualityJourney(Collection $lineageRecords): array
    {
        return [
            'initial_quality' => 'Raw data quality assessment',
            'validation_results' => 'Validation check outcomes',
            'improvement_steps' => 'Data quality improvement actions',
            'final_quality' => 'Final data quality rating',
        ];
    }

    // Placeholder methods for various validation checks
    protected function getCompletenessValidation(Organization $organization, int $reportingYear): array
    {
        return ['status' => 'passed', 'details' => 'All required data fields present'];
    }

    protected function getAccuracyValidation(Organization $organization, int $reportingYear): array
    {
        return ['status' => 'passed', 'details' => 'Data accuracy checks completed'];
    }

    protected function getConsistencyValidation(Organization $organization, int $reportingYear): array
    {
        return ['status' => 'passed', 'details' => 'Data consistency verified'];
    }

    protected function getTimelinessValidation(Organization $organization, int $reportingYear): array
    {
        return ['status' => 'passed', 'details' => 'Data timeliness requirements met'];
    }

    protected function getFormulaVerification(Organization $organization, int $reportingYear): array
    {
        return ['status' => 'verified', 'details' => 'Calculation formulas verified'];
    }

    protected function getFactorValidation(Organization $organization, int $reportingYear): array
    {
        return ['status' => 'validated', 'details' => 'Emission factors validated'];
    }

    protected function getUnitConversionValidation(Organization $organization, int $reportingYear): array
    {
        return ['status' => 'validated', 'details' => 'Unit conversions verified'];
    }

    protected function getScopeBoundaryValidation(Organization $organization, int $reportingYear): array
    {
        return ['status' => 'validated', 'details' => 'Scope boundaries verified'];
    }

    protected function getMaterialityValidation(Organization $organization, int $reportingYear): array
    {
        return ['status' => 'validated', 'details' => 'Materiality thresholds applied'];
    }

    protected function getTemporalConsistencyValidation(Organization $organization, int $reportingYear): array
    {
        return ['status' => 'validated', 'details' => 'Temporal consistency verified'];
    }

    protected function getFactorUpdates(int $reportingYear): array
    {
        return ['updates' => 'No factor updates during reporting period'];
    }

    protected function getMethodologyChanges(int $reportingYear): array
    {
        return ['changes' => 'No methodology changes during reporting period'];
    }

    protected function getSystemConfigChanges(int $reportingYear): array
    {
        return ['changes' => 'No system configuration changes during reporting period'];
    }

    protected function getDataSourceChanges(Organization $organization, int $reportingYear): array
    {
        return ['changes' => 'No data source changes during reporting period'];
    }

    protected function getLastAuditEntryHash(): ?string
    {
        // This would retrieve the hash of the last audit entry
        return null; // Placeholder
    }

    protected function storeImmutableEntry(array $entry): void
    {
        // This would store the entry in an immutable storage system
        Log::info('Immutable audit entry created', ['hash' => $entry['hash']]);
    }
}
