<?php

namespace App\Services;

use App\Models\ActivityData;
use App\Models\Facility;
use App\Models\EmissionSource;
use App\Models\MeasurementUnit;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class IoTSensorService
{
    protected DataLakeService $dataLakeService;
    protected array $sensorTypes = [
        'energy_meter' => 'Energy Consumption Meter',
        'gas_meter' => 'Natural Gas Meter',
        'water_meter' => 'Water Consumption Meter',
        'temperature' => 'Temperature Sensor',
        'humidity' => 'Humidity Sensor',
        'air_quality' => 'Air Quality Monitor',
        'flow_meter' => 'Flow Rate Meter',
        'pressure' => 'Pressure Sensor',
    ];

    public function __construct(DataLakeService $dataLakeService)
    {
        $this->dataLakeService = $dataLakeService;
    }

    /**
     * Register a new IoT sensor
     */
    public function registerSensor(array $sensorConfig): string
    {
        $sensorId = 'sensor_' . uniqid();
        
        $sensor = [
            'id' => $sensorId,
            'name' => $sensorConfig['name'],
            'type' => $sensorConfig['type'],
            'facility_id' => $sensorConfig['facility_id'],
            'location' => $sensorConfig['location'] ?? null,
            'endpoint' => $sensorConfig['endpoint'] ?? null,
            'api_key' => $sensorConfig['api_key'] ?? null,
            'protocol' => $sensorConfig['protocol'] ?? 'http', // http, mqtt, modbus
            'data_format' => $sensorConfig['data_format'] ?? 'json',
            'polling_interval' => $sensorConfig['polling_interval'] ?? 300, // seconds
            'unit' => $sensorConfig['unit'] ?? null,
            'calibration_factor' => $sensorConfig['calibration_factor'] ?? 1.0,
            'status' => 'active',
            'registered_at' => now(),
            'last_reading_at' => null,
        ];

        // Store sensor configuration
        Cache::put("iot_sensor_{$sensorId}", $sensor, 86400 * 30); // 30 days
        
        // Store in database for persistence
        DB::table('iot_sensors')->insert($sensor);

        Log::info("IoT sensor registered: {$sensorId}", $sensor);
        
        return $sensorId;
    }

    /**
     * Ingest sensor data
     */
    public function ingestSensorData(string $sensorId, array $data): bool
    {
        $sensor = $this->getSensor($sensorId);
        if (!$sensor) {
            Log::error("Sensor not found: {$sensorId}");
            return false;
        }

        try {
            // Store raw data in data lake
            $dataId = $this->dataLakeService->storeRawData('iot_sensor', $data, [
                'sensor_id' => $sensorId,
                'sensor_type' => $sensor['type'],
                'facility_id' => $sensor['facility_id'],
                'data_type' => 'sensor_reading',
            ]);

            // Process and normalize the data
            $processedData = $this->processSensorData($sensor, $data);
            
            if ($processedData) {
                // Create activity data record if applicable
                $this->createActivityDataFromSensor($processedData, $sensor);
                
                // Update sensor last reading time
                $this->updateSensorLastReading($sensorId);
                
                Log::info("Sensor data ingested successfully", [
                    'sensor_id' => $sensorId,
                    'data_id' => $dataId,
                ]);
                
                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::error("Failed to ingest sensor data: " . $e->getMessage(), [
                'sensor_id' => $sensorId,
                'data' => $data,
            ]);
            return false;
        }
    }

    /**
     * Get real-time data for a facility
     */
    public function getRealtimeData(string $facilityId): array
    {
        $sensors = $this->getFacilitySensors($facilityId);
        $realtimeData = [];

        foreach ($sensors as $sensor) {
            try {
                $data = $this->pollSensorData($sensor);
                if ($data) {
                    $realtimeData[$sensor['id']] = [
                        'sensor' => $sensor,
                        'data' => $data,
                        'timestamp' => now(),
                    ];
                }
            } catch (\Exception $e) {
                Log::error("Failed to poll sensor {$sensor['id']}: " . $e->getMessage());
            }
        }

        return $realtimeData;
    }

    /**
     * Poll data from a specific sensor
     */
    public function pollSensorData(array $sensor): ?array
    {
        switch ($sensor['protocol']) {
            case 'http':
                return $this->pollHttpSensor($sensor);
            case 'mqtt':
                return $this->pollMqttSensor($sensor);
            case 'modbus':
                return $this->pollModbusSensor($sensor);
            default:
                Log::warning("Unsupported sensor protocol: {$sensor['protocol']}");
                return null;
        }
    }

    /**
     * Poll HTTP-based sensor
     */
    protected function pollHttpSensor(array $sensor): ?array
    {
        if (!$sensor['endpoint']) {
            return null;
        }

        try {
            $headers = [];
            if ($sensor['api_key']) {
                $headers['Authorization'] = 'Bearer ' . $sensor['api_key'];
            }

            $response = Http::withHeaders($headers)
                ->timeout(10)
                ->get($sensor['endpoint']);

            if ($response->successful()) {
                $data = $response->json();
                return $this->normalizeSensorData($data, $sensor);
            }

            Log::warning("HTTP sensor poll failed", [
                'sensor_id' => $sensor['id'],
                'status' => $response->status(),
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error("HTTP sensor poll error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Poll MQTT-based sensor (placeholder)
     */
    protected function pollMqttSensor(array $sensor): ?array
    {
        // MQTT implementation would require additional libraries
        // This is a placeholder for future implementation
        Log::info("MQTT polling not yet implemented for sensor: {$sensor['id']}");
        return null;
    }

    /**
     * Poll Modbus-based sensor (placeholder)
     */
    protected function pollModbusSensor(array $sensor): ?array
    {
        // Modbus implementation would require additional libraries
        // This is a placeholder for future implementation
        Log::info("Modbus polling not yet implemented for sensor: {$sensor['id']}");
        return null;
    }

    /**
     * Normalize sensor data to standard format
     */
    protected function normalizeSensorData(array $rawData, array $sensor): array
    {
        $normalized = [
            'sensor_id' => $sensor['id'],
            'timestamp' => $rawData['timestamp'] ?? now()->toISOString(),
            'value' => null,
            'unit' => $sensor['unit'],
            'quality' => $rawData['quality'] ?? 'good',
            'raw_data' => $rawData,
        ];

        // Extract value based on sensor type and data format
        switch ($sensor['type']) {
            case 'energy_meter':
                $normalized['value'] = $rawData['energy'] ?? $rawData['consumption'] ?? $rawData['value'];
                break;
            case 'gas_meter':
                $normalized['value'] = $rawData['volume'] ?? $rawData['flow'] ?? $rawData['value'];
                break;
            case 'water_meter':
                $normalized['value'] = $rawData['volume'] ?? $rawData['flow'] ?? $rawData['value'];
                break;
            case 'temperature':
                $normalized['value'] = $rawData['temperature'] ?? $rawData['temp'] ?? $rawData['value'];
                break;
            case 'humidity':
                $normalized['value'] = $rawData['humidity'] ?? $rawData['rh'] ?? $rawData['value'];
                break;
            case 'air_quality':
                $normalized['value'] = $rawData['aqi'] ?? $rawData['pm25'] ?? $rawData['value'];
                break;
            default:
                $normalized['value'] = $rawData['value'] ?? null;
        }

        // Apply calibration factor
        if ($normalized['value'] && $sensor['calibration_factor'] != 1.0) {
            $normalized['value'] *= $sensor['calibration_factor'];
        }

        return $normalized;
    }

    /**
     * Process sensor data for emissions calculation
     */
    protected function processSensorData(array $sensor, array $data): ?array
    {
        // Only process sensors that contribute to emissions
        $emissionRelevantTypes = ['energy_meter', 'gas_meter'];
        
        if (!in_array($sensor['type'], $emissionRelevantTypes)) {
            return null;
        }

        $normalized = $this->normalizeSensorData($data, $sensor);
        
        if (!$normalized['value']) {
            return null;
        }

        return [
            'sensor_id' => $sensor['id'],
            'facility_id' => $sensor['facility_id'],
            'quantity' => $normalized['value'],
            'unit' => $normalized['unit'],
            'timestamp' => Carbon::parse($normalized['timestamp']),
            'data_quality' => $normalized['quality'],
            'sensor_type' => $sensor['type'],
        ];
    }

    /**
     * Create activity data from sensor reading
     */
    protected function createActivityDataFromSensor(array $processedData, array $sensor): void
    {
        $facility = Facility::find($sensor['facility_id']);
        if (!$facility) {
            return;
        }

        // Find or create emission source based on sensor type
        $sourceName = $this->mapSensorTypeToEmissionSource($sensor['type']);
        $emissionSource = EmissionSource::firstOrCreate([
            'name' => $sourceName,
        ], [
            'scope' => $this->getScopeForSensorType($sensor['type']),
            'description' => "Auto-created from IoT sensor: {$sensor['name']}",
        ]);

        // Find or create measurement unit
        $unit = MeasurementUnit::firstOrCreate([
            'symbol' => $processedData['unit'],
        ], [
            'name' => $this->expandUnitName($processedData['unit']),
            'unit_type' => $this->getUnitTypeForSensor($sensor['type']),
            'is_active' => true,
        ]);

        // Create activity data record
        ActivityData::create([
            'facility_id' => $facility->id,
            'organization_id' => $facility->organization_id,
            'source_id' => $emissionSource->id,
            'quantity' => $processedData['quantity'],
            'activity_unit_id' => $unit->id,
            'date_recorded' => $processedData['timestamp'],
            'date_start' => $processedData['timestamp']->startOfHour(),
            'date_end' => $processedData['timestamp']->endOfHour(),
            'scope' => $this->getScopeForSensorType($sensor['type']),
            'material_type' => $this->getMaterialTypeForSensor($sensor['type']),
            'notes' => "Auto-generated from IoT sensor: {$sensor['name']} (ID: {$sensor['id']})",
            'recorded_by' => 'iot_system',
        ]);
    }

    /**
     * Get sensor configuration
     */
    protected function getSensor(string $sensorId): ?array
    {
        $sensor = Cache::get("iot_sensor_{$sensorId}");
        
        if (!$sensor) {
            // Try to load from database
            $sensorData = DB::table('iot_sensors')->where('id', $sensorId)->first();
            if ($sensorData) {
                $sensor = (array) $sensorData;
                Cache::put("iot_sensor_{$sensorId}", $sensor, 86400);
            }
        }

        return $sensor;
    }

    /**
     * Get all sensors for a facility
     */
    protected function getFacilitySensors(string $facilityId): array
    {
        $sensors = DB::table('iot_sensors')
            ->where('facility_id', $facilityId)
            ->where('status', 'active')
            ->get()
            ->toArray();

        return array_map(fn($sensor) => (array) $sensor, $sensors);
    }

    /**
     * Update sensor last reading timestamp
     */
    protected function updateSensorLastReading(string $sensorId): void
    {
        $sensor = $this->getSensor($sensorId);
        if ($sensor) {
            $sensor['last_reading_at'] = now();
            Cache::put("iot_sensor_{$sensorId}", $sensor, 86400 * 30);
            DB::table('iot_sensors')->where('id', $sensorId)->update(['last_reading_at' => now()]);
        }
    }

    /**
     * Map sensor type to emission source
     */
    protected function mapSensorTypeToEmissionSource(string $sensorType): string
    {
        $mapping = [
            'energy_meter' => 'Electricity',
            'gas_meter' => 'Natural Gas',
            'water_meter' => 'Water',
        ];

        return $mapping[$sensorType] ?? 'Unknown Source';
    }

    /**
     * Get scope for sensor type
     */
    protected function getScopeForSensorType(string $sensorType): string
    {
        $mapping = [
            'energy_meter' => '2', // Purchased electricity
            'gas_meter' => '1',    // Direct combustion
            'water_meter' => '3',  // Indirect
        ];

        return $mapping[$sensorType] ?? '3';
    }

    /**
     * Get unit type for sensor
     */
    protected function getUnitTypeForSensor(string $sensorType): string
    {
        $mapping = [
            'energy_meter' => 'Energy',
            'gas_meter' => 'Volume',
            'water_meter' => 'Volume',
        ];

        return $mapping[$sensorType] ?? 'Other';
    }

    /**
     * Get material type for sensor
     */
    protected function getMaterialTypeForSensor(string $sensorType): string
    {
        $mapping = [
            'energy_meter' => 'Electricity',
            'gas_meter' => 'Natural Gas',
            'water_meter' => 'Water',
        ];

        return $mapping[$sensorType] ?? 'Unknown';
    }

    /**
     * Expand unit abbreviations
     */
    protected function expandUnitName(string $unit): string
    {
        $mapping = [
            'kWh' => 'Kilowatt Hour',
            'MWh' => 'Megawatt Hour',
            'm3' => 'Cubic Meter',
            'L' => 'Liter',
            'gal' => 'Gallon',
        ];

        return $mapping[$unit] ?? $unit;
    }

    /**
     * Get sensor status
     */
    public function getSensorStatus(string $sensorId): array
    {
        $sensor = $this->getSensor($sensorId);
        if (!$sensor) {
            return ['status' => 'not_found'];
        }

        $lastReading = $sensor['last_reading_at'] ? Carbon::parse($sensor['last_reading_at']) : null;
        $isOnline = $lastReading && $lastReading->diffInMinutes(now()) < ($sensor['polling_interval'] / 60 * 2);

        return [
            'status' => $isOnline ? 'online' : 'offline',
            'last_reading' => $lastReading,
            'sensor' => $sensor,
        ];
    }

    /**
     * List all sensors
     */
    public function listSensors(string $facilityId = null): Collection
    {
        $query = DB::table('iot_sensors');
        
        if ($facilityId) {
            $query->where('facility_id', $facilityId);
        }

        return collect($query->get());
    }
}
