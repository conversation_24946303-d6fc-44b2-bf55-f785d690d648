<?php

namespace App\Services;

use App\Models\MeasurementUnit;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class AdvancedUnitConverter
{
    protected array $conversionMatrix = [];
    protected array $unitPatterns = [];
    protected array $contextualFactors = [];

    public function __construct()
    {
        $this->loadConversionMatrix();
        $this->loadUnitPatterns();
        $this->loadContextualFactors();
    }

    /**
     * Convert with context awareness
     */
    public function convertWithContext(float $value, string $fromUnit, string $toUnit, array $context = []): float
    {
        // Normalize unit names
        $fromUnit = $this->normalizeUnitName($fromUnit);
        $toUnit = $this->normalizeUnitName($toUnit);

        // Check if units are the same
        if ($fromUnit === $toUnit) {
            return $value;
        }

        // Get conversion factor
        $factor = $this->getConversionFactor($fromUnit, $toUnit, $context);

        if ($factor === null) {
            throw new \InvalidArgumentException("Cannot convert from {$fromUnit} to {$toUnit}");
        }

        $result = $value * $factor;

        Log::info("Unit conversion performed", [
            'value' => $value,
            'from_unit' => $fromUnit,
            'to_unit' => $toUnit,
            'factor' => $factor,
            'result' => $result,
            'context' => $context,
        ]);

        return $result;
    }

    /**
     * Detect unit from text
     */
    public function detectUnitFromText(string $text): ?MeasurementUnit
    {
        $text = strtolower(trim($text));

        // Try exact matches first
        foreach ($this->unitPatterns as $pattern => $unitData) {
            if (preg_match($pattern, $text)) {
                return $this->findOrCreateUnit($unitData);
            }
        }

        // Try fuzzy matching
        $bestMatch = $this->fuzzyMatchUnit($text);
        if ($bestMatch) {
            return $bestMatch;
        }

        return null;
    }

    /**
     * Suggest possible conversions for a unit
     */
    public function suggestConversions(string $unit): array
    {
        $unit = $this->normalizeUnitName($unit);
        $suggestions = [];

        // Find units in the same category
        $unitType = $this->getUnitType($unit);
        if ($unitType) {
            $relatedUnits = MeasurementUnit::where('unit_type', $unitType)
                ->where('symbol', '!=', $unit)
                ->where('is_active', true)
                ->get();

            foreach ($relatedUnits as $relatedUnit) {
                $factor = $this->getConversionFactor($unit, $relatedUnit->symbol);
                if ($factor !== null) {
                    $suggestions[] = [
                        'unit' => $relatedUnit,
                        'conversion_factor' => $factor,
                        'example' => "1 {$unit} = " . number_format($factor, 6) . " {$relatedUnit->symbol}",
                    ];
                }
            }
        }

        return $suggestions;
    }

    /**
     * Get conversion factor between two units
     */
    protected function getConversionFactor(string $fromUnit, string $toUnit, array $context = []): ?float
    {
        // Check direct conversion
        $cacheKey = "conversion_{$fromUnit}_{$toUnit}";
        $factor = Cache::get($cacheKey);

        if ($factor !== null) {
            return $this->applyContextualAdjustment($factor, $context);
        }

        // Check database for conversion
        $fromUnitModel = MeasurementUnit::where('symbol', $fromUnit)->first();
        $toUnitModel = MeasurementUnit::where('symbol', $toUnit)->first();

        if ($fromUnitModel && $toUnitModel) {
            $factor = $this->calculateConversionFactor($fromUnitModel, $toUnitModel);
            if ($factor !== null) {
                Cache::put($cacheKey, $factor, 3600); // Cache for 1 hour
                return $this->applyContextualAdjustment($factor, $context);
            }
        }

        // Check conversion matrix
        if (isset($this->conversionMatrix[$fromUnit][$toUnit])) {
            $factor = $this->conversionMatrix[$fromUnit][$toUnit];
            Cache::put($cacheKey, $factor, 3600);
            return $this->applyContextualAdjustment($factor, $context);
        }

        // Try indirect conversion through base units
        $factor = $this->findIndirectConversion($fromUnit, $toUnit);
        if ($factor !== null) {
            Cache::put($cacheKey, $factor, 3600);
            return $this->applyContextualAdjustment($factor, $context);
        }

        return null;
    }

    /**
     * Calculate conversion factor between two unit models
     */
    protected function calculateConversionFactor(MeasurementUnit $fromUnit, MeasurementUnit $toUnit): ?float
    {
        // If both have conversion factors to SI units
        if ($fromUnit->conversion_factor && $toUnit->conversion_factor) {
            return $fromUnit->conversion_factor / $toUnit->conversion_factor;
        }

        // If one is the base unit
        if ($fromUnit->is_base_unit && $toUnit->conversion_factor) {
            return 1 / $toUnit->conversion_factor;
        }

        if ($toUnit->is_base_unit && $fromUnit->conversion_factor) {
            return $fromUnit->conversion_factor;
        }

        return null;
    }

    /**
     * Find indirect conversion through base units
     */
    protected function findIndirectConversion(string $fromUnit, string $toUnit): ?float
    {
        // Try to find a common base unit
        $fromType = $this->getUnitType($fromUnit);
        $toType = $this->getUnitType($toUnit);

        if ($fromType !== $toType) {
            return null; // Cannot convert between different unit types
        }

        // Find base unit for this type
        $baseUnit = MeasurementUnit::where('unit_type', $fromType)
            ->where('is_base_unit', true)
            ->first();

        if (!$baseUnit) {
            return null;
        }

        // Convert from -> base -> to
        $fromToBase = $this->getConversionFactor($fromUnit, $baseUnit->symbol);
        $baseToTo = $this->getConversionFactor($baseUnit->symbol, $toUnit);

        if ($fromToBase !== null && $baseToTo !== null) {
            return $fromToBase * $baseToTo;
        }

        return null;
    }

    /**
     * Apply contextual adjustments to conversion factor
     */
    protected function applyContextualAdjustment(float $factor, array $context): float
    {
        // Apply temperature corrections for gas volumes
        if (isset($context['temperature']) && isset($context['pressure'])) {
            $factor = $this->applyTPCorrection($factor, $context);
        }

        // Apply regional heating value corrections
        if (isset($context['region']) && isset($context['fuel_type'])) {
            $factor = $this->applyRegionalCorrection($factor, $context);
        }

        // Apply density corrections for liquid fuels
        if (isset($context['density']) && isset($context['fuel_type'])) {
            $factor = $this->applyDensityCorrection($factor, $context);
        }

        return $factor;
    }

    /**
     * Apply temperature and pressure corrections
     */
    protected function applyTPCorrection(float $factor, array $context): float
    {
        $standardTemp = 273.15 + 15; // 15°C in Kelvin
        $standardPressure = 101.325; // kPa

        $actualTemp = $context['temperature'] + 273.15; // Convert to Kelvin
        $actualPressure = $context['pressure'];

        // Ideal gas law correction: (P1/T1) = (P2/T2)
        $correction = ($standardPressure / $standardTemp) / ($actualPressure / $actualTemp);

        return $factor * $correction;
    }

    /**
     * Apply regional heating value corrections
     */
    protected function applyRegionalCorrection(float $factor, array $context): float
    {
        $region = $context['region'];
        $fuelType = $context['fuel_type'];

        if (isset($this->contextualFactors['regional'][$region][$fuelType])) {
            $correction = $this->contextualFactors['regional'][$region][$fuelType];
            return $factor * $correction;
        }

        return $factor;
    }

    /**
     * Apply density corrections for liquid fuels
     */
    protected function applyDensityCorrection(float $factor, array $context): float
    {
        $standardDensity = $this->getStandardDensity($context['fuel_type']);
        $actualDensity = $context['density'];

        if ($standardDensity && $actualDensity) {
            return $factor * ($actualDensity / $standardDensity);
        }

        return $factor;
    }

    /**
     * Normalize unit names
     */
    protected function normalizeUnitName(string $unit): string
    {
        $unit = trim($unit);
        
        // Common normalizations
        $normalizations = [
            'kilowatt hour' => 'kWh',
            'kilowatt-hour' => 'kWh',
            'kilowatthour' => 'kWh',
            'kwh' => 'kWh',
            'megawatt hour' => 'MWh',
            'megawatt-hour' => 'MWh',
            'mwh' => 'MWh',
            'gigajoule' => 'GJ',
            'gj' => 'GJ',
            'terajoule' => 'TJ',
            'tj' => 'TJ',
            'cubic meter' => 'm³',
            'cubic metre' => 'm³',
            'm3' => 'm³',
            'litre' => 'L',
            'liter' => 'L',
            'gallon' => 'gal',
            'tonne' => 't',
            'metric ton' => 't',
            'kilogram' => 'kg',
            'pound' => 'lb',
            'tonnes co2 equivalent' => 'tCO2e',
            'tonnes co2e' => 'tCO2e',
            'tons co2e' => 'tCO2e',
            'kg co2e' => 'kgCO2e',
            'kilograms co2e' => 'kgCO2e',
        ];

        $lowerUnit = strtolower($unit);
        return $normalizations[$lowerUnit] ?? $unit;
    }

    /**
     * Get unit type for a unit symbol
     */
    protected function getUnitType(string $unit): ?string
    {
        $unit = MeasurementUnit::where('symbol', $unit)->first();
        return $unit ? $unit->unit_type : null;
    }

    /**
     * Fuzzy match unit from text
     */
    protected function fuzzyMatchUnit(string $text): ?MeasurementUnit
    {
        $units = MeasurementUnit::where('is_active', true)->get();
        $bestMatch = null;
        $bestScore = 0;

        foreach ($units as $unit) {
            $score = $this->calculateSimilarity($text, strtolower($unit->name));
            $symbolScore = $this->calculateSimilarity($text, strtolower($unit->symbol));
            
            $maxScore = max($score, $symbolScore);
            
            if ($maxScore > $bestScore && $maxScore > 0.7) {
                $bestScore = $maxScore;
                $bestMatch = $unit;
            }
        }

        return $bestMatch;
    }

    /**
     * Calculate string similarity
     */
    protected function calculateSimilarity(string $str1, string $str2): float
    {
        $len1 = strlen($str1);
        $len2 = strlen($str2);
        
        if ($len1 === 0 || $len2 === 0) {
            return 0;
        }

        $distance = levenshtein($str1, $str2);
        $maxLen = max($len1, $len2);
        
        return 1 - ($distance / $maxLen);
    }

    /**
     * Find or create unit from pattern data
     */
    protected function findOrCreateUnit(array $unitData): MeasurementUnit
    {
        return MeasurementUnit::firstOrCreate([
            'symbol' => $unitData['symbol'],
        ], $unitData);
    }

    /**
     * Get standard density for fuel type
     */
    protected function getStandardDensity(string $fuelType): ?float
    {
        $densities = [
            'gasoline' => 0.745, // kg/L
            'diesel' => 0.832,
            'jet_fuel' => 0.775,
            'heating_oil' => 0.85,
            'heavy_fuel_oil' => 0.95,
        ];

        return $densities[strtolower($fuelType)] ?? null;
    }

    /**
     * Load conversion matrix
     */
    protected function loadConversionMatrix(): void
    {
        $this->conversionMatrix = [
            // Energy conversions
            'kWh' => [
                'MWh' => 0.001,
                'GJ' => 0.0036,
                'TJ' => 0.0000036,
                'BTU' => 3412.14,
                'therm' => 0.034121,
            ],
            'MWh' => [
                'kWh' => 1000,
                'GJ' => 3.6,
                'TJ' => 0.0036,
            ],
            'GJ' => [
                'kWh' => 277.778,
                'MWh' => 0.277778,
                'TJ' => 0.001,
                'BTU' => 947817,
            ],
            'TJ' => [
                'GJ' => 1000,
                'kWh' => 277778,
                'MWh' => 277.778,
            ],
            
            // Volume conversions
            'L' => [
                'm³' => 0.001,
                'gal' => 0.264172,
                'ft³' => 0.0353147,
            ],
            'm³' => [
                'L' => 1000,
                'gal' => 264.172,
                'ft³' => 35.3147,
            ],
            'gal' => [
                'L' => 3.78541,
                'm³' => 0.00378541,
            ],
            
            // Mass conversions
            'kg' => [
                't' => 0.001,
                'lb' => 2.20462,
                'oz' => 35.274,
            ],
            't' => [
                'kg' => 1000,
                'lb' => 2204.62,
            ],
            'lb' => [
                'kg' => 0.453592,
                't' => 0.000453592,
            ],
            
            // Emissions conversions
            'kgCO2e' => [
                'tCO2e' => 0.001,
                'gCO2e' => 1000,
            ],
            'tCO2e' => [
                'kgCO2e' => 1000,
                'MtCO2e' => 0.000001,
                'GtCO2e' => 0.000000001,
            ],
        ];
    }

    /**
     * Load unit detection patterns
     */
    protected function loadUnitPatterns(): void
    {
        $this->unitPatterns = [
            '/\bkwh\b|\bkilowatt.?hours?\b/' => [
                'symbol' => 'kWh',
                'name' => 'Kilowatt Hour',
                'unit_type' => 'Energy',
                'is_active' => true,
            ],
            '/\bmwh\b|\bmegawatt.?hours?\b/' => [
                'symbol' => 'MWh',
                'name' => 'Megawatt Hour',
                'unit_type' => 'Energy',
                'is_active' => true,
            ],
            '/\bgj\b|\bgigajoules?\b/' => [
                'symbol' => 'GJ',
                'name' => 'Gigajoule',
                'unit_type' => 'Energy',
                'is_active' => true,
            ],
            '/\btj\b|\bterajoules?\b/' => [
                'symbol' => 'TJ',
                'name' => 'Terajoule',
                'unit_type' => 'Energy',
                'is_active' => true,
            ],
            '/\bm3\b|\bcubic.?meters?\b|\bcubic.?metres?\b/' => [
                'symbol' => 'm³',
                'name' => 'Cubic Meter',
                'unit_type' => 'Volume',
                'is_active' => true,
            ],
            '/\bliters?\b|\blitres?\b/' => [
                'symbol' => 'L',
                'name' => 'Liter',
                'unit_type' => 'Volume',
                'is_active' => true,
            ],
            '/\bgallons?\b/' => [
                'symbol' => 'gal',
                'name' => 'Gallon',
                'unit_type' => 'Volume',
                'is_active' => true,
            ],
            '/\btonnes?\b|\bmetric.?tons?\b/' => [
                'symbol' => 't',
                'name' => 'Tonne',
                'unit_type' => 'Mass',
                'is_active' => true,
            ],
            '/\bkg\b|\bkilograms?\b/' => [
                'symbol' => 'kg',
                'name' => 'Kilogram',
                'unit_type' => 'Mass',
                'is_active' => true,
            ],
            '/\btco2e?\b|\btonnes?.?co2.?equivalent\b/' => [
                'symbol' => 'tCO2e',
                'name' => 'Tonnes CO2 Equivalent',
                'unit_type' => 'Emissions',
                'is_active' => true,
            ],
        ];
    }

    /**
     * Load contextual factors
     */
    protected function loadContextualFactors(): void
    {
        $this->contextualFactors = [
            'regional' => [
                'US' => [
                    'natural_gas' => 1.02, // Higher heating value adjustment
                    'gasoline' => 0.98,
                ],
                'EU' => [
                    'natural_gas' => 1.0,
                    'gasoline' => 1.0,
                ],
                'APAC' => [
                    'natural_gas' => 0.97,
                    'gasoline' => 1.01,
                ],
            ],
        ];
    }
}
