<?php

namespace App\Services;

use App\Models\ActivityData;
use App\Models\Organization;
use App\Models\EmissionSource;
use App\Models\MeasurementUnit;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SupplyChainService
{
    protected DataLakeService $dataLakeService;
    protected array $supportedPortals = [
        'cdp' => 'CDP Supply Chain',
        'ecovadis' => 'EcoVadis Platform',
        'supplier_portal' => 'Custom Supplier Portal',
        'ariba' => 'SAP Ariba',
        'coupa' => 'Coupa Supplier Portal',
    ];

    public function __construct(DataLakeService $dataLakeService)
    {
        $this->dataLakeService = $dataLakeService;
    }

    /**
     * Connect to supplier portal
     */
    public function connectSupplierPortal(string $supplierId, array $config): bool
    {
        try {
            $portalType = $config['portal_type'] ?? 'supplier_portal';

            $connection = [
                'supplier_id' => $supplierId,
                'portal_type' => $portalType,
                'endpoint' => $config['endpoint'],
                'credentials' => $config['credentials'],
                'data_mapping' => $config['data_mapping'] ?? [],
                'sync_frequency' => $config['sync_frequency'] ?? 'monthly',
                'last_sync' => null,
                'status' => 'active',
                'connected_at' => now(),
            ];

            // Test connection
            if ($this->testSupplierConnection($connection)) {
                // Store connection configuration
                Cache::put("supplier_connection_{$supplierId}", $connection, 86400 * 30);

                // Store in database
                DB::table('supplier_connections')->updateOrInsert(
                    ['supplier_id' => $supplierId],
                    $connection
                );

                Log::info("Supplier portal connected successfully", [
                    'supplier_id' => $supplierId,
                    'portal_type' => $portalType,
                ]);

                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::error("Failed to connect supplier portal: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Sync supplier data from all connected portals
     */
    public function syncSupplierData(): array
    {
        $connections = $this->getActiveConnections();
        $results = [];

        foreach ($connections as $connection) {
            try {
                $data = $this->syncFromSupplier($connection);
                $results[$connection['supplier_id']] = [
                    'status' => 'success',
                    'records_synced' => count($data),
                    'data' => $data,
                ];

                // Update last sync time
                $this->updateLastSync($connection['supplier_id']);

            } catch (\Exception $e) {
                $results[$connection['supplier_id']] = [
                    'status' => 'error',
                    'error' => $e->getMessage(),
                ];

                Log::error("Failed to sync supplier data", [
                    'supplier_id' => $connection['supplier_id'],
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $results;
    }

    /**
     * Validate Scope 3 data from suppliers
     */
    public function validateScope3Data(array $data): array
    {
        $validationResults = [
            'valid' => true,
            'errors' => [],
            'warnings' => [],
            'processed_data' => [],
        ];

        foreach ($data as $index => $record) {
            $recordValidation = $this->validateSupplierRecord($record, $index);

            if (!$recordValidation['valid']) {
                $validationResults['valid'] = false;
                $validationResults['errors'] = array_merge(
                    $validationResults['errors'],
                    $recordValidation['errors']
                );
            }

            $validationResults['warnings'] = array_merge(
                $validationResults['warnings'],
                $recordValidation['warnings']
            );

            if ($recordValidation['valid']) {
                $validationResults['processed_data'][] = $recordValidation['processed_record'];
            }
        }

        return $validationResults;
    }

    /**
     * Import Scope 3 data from suppliers
     */
    public function importScope3Data(array $validatedData, string $supplierId): array
    {
        $imported = [];
        $errors = [];

        foreach ($validatedData as $record) {
            try {
                // Store raw data in data lake
                $dataId = $this->dataLakeService->storeRawData('supplier', $record, [
                    'supplier_id' => $supplierId,
                    'data_type' => 'scope3_emissions',
                    'import_date' => now(),
                ]);

                // Create activity data record
                $activityData = $this->createActivityDataFromSupplier($record, $supplierId);

                if ($activityData) {
                    $imported[] = [
                        'data_id' => $dataId,
                        'activity_data_id' => $activityData->id,
                        'record' => $record,
                    ];
                }

            } catch (\Exception $e) {
                $errors[] = [
                    'record' => $record,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return [
            'imported' => $imported,
            'errors' => $errors,
            'total_imported' => count($imported),
            'total_errors' => count($errors),
        ];
    }

    /**
     * Get supplier emissions data
     */
    public function getSupplierEmissions(string $supplierId, Carbon $fromDate = null, Carbon $toDate = null): Collection
    {
        $query = ActivityData::where('notes', 'like', "%supplier:{$supplierId}%")
            ->where('scope', '3');

        if ($fromDate) {
            $query->where('date_recorded', '>=', $fromDate);
        }

        if ($toDate) {
            $query->where('date_recorded', '<=', $toDate);
        }

        return $query->with(['emissionSource', 'activityUnit', 'facility'])
            ->orderBy('date_recorded', 'desc')
            ->get();
    }

    /**
     * Test supplier connection
     */
    protected function testSupplierConnection(array $connection): bool
    {
        switch ($connection['portal_type']) {
            case 'cdp':
                return $this->testCDPConnection($connection);
            case 'ecovadis':
                return $this->testEcoVadisConnection($connection);
            case 'supplier_portal':
                return $this->testCustomPortalConnection($connection);
            case 'ariba':
                return $this->testAribaConnection($connection);
            case 'coupa':
                return $this->testCoupaConnection($connection);
            default:
                return false;
        }
    }

    /**
     * Test CDP connection
     */
    protected function testCDPConnection(array $connection): bool
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $connection['credentials']['api_key'],
                'Content-Type' => 'application/json',
            ])->timeout(10)->get($connection['endpoint'] . '/api/v1/health');

            return $response->successful();

        } catch (\Exception $e) {
            Log::error("CDP connection test failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Test EcoVadis connection
     */
    protected function testEcoVadisConnection(array $connection): bool
    {
        try {
            $response = Http::withHeaders([
                'X-API-Key' => $connection['credentials']['api_key'],
                'Content-Type' => 'application/json',
            ])->timeout(10)->get($connection['endpoint'] . '/api/ping');

            return $response->successful();

        } catch (\Exception $e) {
            Log::error("EcoVadis connection test failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Test custom portal connection
     */
    protected function testCustomPortalConnection(array $connection): bool
    {
        try {
            $headers = [];

            if (isset($connection['credentials']['api_key'])) {
                $headers['Authorization'] = 'Bearer ' . $connection['credentials']['api_key'];
            }

            if (isset($connection['credentials']['username']) && isset($connection['credentials']['password'])) {
                $headers['Authorization'] = 'Basic ' . base64_encode(
                    $connection['credentials']['username'] . ':' . $connection['credentials']['password']
                );
            }

            $response = Http::withHeaders($headers)
                ->timeout(10)
                ->get($connection['endpoint'] . '/api/test');

            return $response->successful();

        } catch (\Exception $e) {
            Log::error("Custom portal connection test failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Test Ariba connection
     */
    protected function testAribaConnection(array $connection): bool
    {
        // Placeholder for SAP Ariba integration
        Log::info("Ariba connection test - implementation pending");
        return false;
    }

    /**
     * Test Coupa connection
     */
    protected function testCoupaConnection(array $connection): bool
    {
        // Placeholder for Coupa integration
        Log::info("Coupa connection test - implementation pending");
        return false;
    }

    /**
     * Sync data from specific supplier
     */
    protected function syncFromSupplier(array $connection): array
    {
        switch ($connection['portal_type']) {
            case 'cdp':
                return $this->syncCDPData($connection);
            case 'ecovadis':
                return $this->syncEcoVadisData($connection);
            case 'supplier_portal':
                return $this->syncCustomPortalData($connection);
            default:
                return [];
        }
    }

    /**
     * Sync CDP data
     */
    protected function syncCDPData(array $connection): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $connection['credentials']['api_key'],
            ])->get($connection['endpoint'] . '/api/v1/emissions', [
                'supplier_id' => $connection['supplier_id'],
                'year' => now()->year,
            ]);

            if ($response->successful()) {
                return $response->json()['data'] ?? [];
            }

            return [];

        } catch (\Exception $e) {
            Log::error("CDP data sync failed: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Sync EcoVadis data
     */
    protected function syncEcoVadisData(array $connection): array
    {
        try {
            $response = Http::withHeaders([
                'X-API-Key' => $connection['credentials']['api_key'],
            ])->get($connection['endpoint'] . '/api/carbon-footprint', [
                'company_id' => $connection['supplier_id'],
                'reporting_year' => now()->year,
            ]);

            if ($response->successful()) {
                return $response->json()['results'] ?? [];
            }

            return [];

        } catch (\Exception $e) {
            Log::error("EcoVadis data sync failed: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Sync custom portal data
     */
    protected function syncCustomPortalData(array $connection): array
    {
        try {
            $headers = [];

            if (isset($connection['credentials']['api_key'])) {
                $headers['Authorization'] = 'Bearer ' . $connection['credentials']['api_key'];
            }

            $response = Http::withHeaders($headers)
                ->get($connection['endpoint'] . '/api/emissions', [
                    'supplier_id' => $connection['supplier_id'],
                    'from_date' => now()->subYear()->format('Y-m-d'),
                    'to_date' => now()->format('Y-m-d'),
                ]);

            if ($response->successful()) {
                return $response->json()['data'] ?? [];
            }

            return [];

        } catch (\Exception $e) {
            Log::error("Custom portal data sync failed: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Validate individual supplier record
     */
    protected function validateSupplierRecord(array $record, int $index): array
    {
        $validation = [
            'valid' => true,
            'errors' => [],
            'warnings' => [],
            'processed_record' => $record,
        ];

        // Required fields validation
        $requiredFields = ['emissions_value', 'activity_type', 'reporting_period'];

        foreach ($requiredFields as $field) {
            if (!isset($record[$field]) || empty($record[$field])) {
                $validation['valid'] = false;
                $validation['errors'][] = "Record {$index}: Missing required field '{$field}'";
            }
        }

        // Data type validation
        if (isset($record['emissions_value']) && !is_numeric($record['emissions_value'])) {
            $validation['valid'] = false;
            $validation['errors'][] = "Record {$index}: Emissions value must be numeric";
        }

        // Date validation
        if (isset($record['reporting_period'])) {
            try {
                Carbon::parse($record['reporting_period']);
            } catch (\Exception $e) {
                $validation['valid'] = false;
                $validation['errors'][] = "Record {$index}: Invalid reporting period date format";
            }
        }

        // Unit validation
        if (isset($record['unit']) && !$this->isValidUnit($record['unit'])) {
            $validation['warnings'][] = "Record {$index}: Unknown unit '{$record['unit']}', will attempt to map";
        }

        return $validation;
    }

    /**
     * Create activity data from supplier record
     */
    protected function createActivityDataFromSupplier(array $record, string $supplierId): ?ActivityData
    {
        try {
            // Find or create emission source
            $sourceName = $this->mapActivityTypeToSource($record['activity_type']);
            $emissionSource = EmissionSource::firstOrCreate([
                'name' => $sourceName,
            ], [
                'scope' => '3',
                'description' => "Supplier reported: {$record['activity_type']}",
            ]);

            // Find or create measurement unit
            $unitSymbol = $this->normalizeUnit($record['unit'] ?? 'tCO2e');
            $unit = MeasurementUnit::firstOrCreate([
                'symbol' => $unitSymbol,
            ], [
                'name' => $this->expandUnitName($unitSymbol),
                'unit_type' => 'Emissions',
                'is_active' => true,
            ]);

            // Create activity data
            return ActivityData::create([
                'organization_id' => 1, // Default organization - should be configurable
                'source_id' => $emissionSource->id,
                'quantity' => (float) $record['emissions_value'],
                'activity_unit_id' => $unit->id,
                'date_recorded' => Carbon::parse($record['reporting_period']),
                'date_start' => Carbon::parse($record['reporting_period'])->startOfYear(),
                'date_end' => Carbon::parse($record['reporting_period'])->endOfYear(),
                'scope' => '3',
                'material_type' => $record['activity_type'],
                'notes' => "Supplier data - supplier:{$supplierId}, portal:" . (isset($record['portal_type']) ? $record['portal_type'] : 'unknown'),
                'recorded_by' => 'supplier_system',
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to create activity data from supplier record: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get active supplier connections
     */
    protected function getActiveConnections(): array
    {
        return DB::table('supplier_connections')
            ->where('status', 'active')
            ->get()
            ->toArray();
    }

    /**
     * Update last sync time
     */
    protected function updateLastSync(string $supplierId): void
    {
        DB::table('supplier_connections')
            ->where('supplier_id', $supplierId)
            ->update(['last_sync' => now()]);

        $connection = Cache::get("supplier_connection_{$supplierId}");
        if ($connection) {
            $connection['last_sync'] = now();
            Cache::put("supplier_connection_{$supplierId}", $connection, 86400 * 30);
        }
    }

    /**
     * Check if unit is valid
     */
    protected function isValidUnit(string $unit): bool
    {
        $validUnits = ['tCO2e', 'kgCO2e', 'tCO2', 'kgCO2', 'MtCO2e', 'GtCO2e'];
        return in_array($unit, $validUnits);
    }

    /**
     * Normalize unit symbols
     */
    protected function normalizeUnit(string $unit): string
    {
        $mapping = [
            'tonnes CO2e' => 'tCO2e',
            'tons CO2e' => 'tCO2e',
            'kg CO2e' => 'kgCO2e',
            'kilograms CO2e' => 'kgCO2e',
            'MT CO2e' => 'MtCO2e',
            'megatonnes CO2e' => 'MtCO2e',
        ];

        return $mapping[strtolower($unit)] ?? $unit;
    }

    /**
     * Map activity type to emission source
     */
    protected function mapActivityTypeToSource(string $activityType): string
    {
        $mapping = [
            'purchased_goods' => 'Purchased Goods and Services',
            'transportation' => 'Upstream Transportation',
            'business_travel' => 'Business Travel',
            'employee_commuting' => 'Employee Commuting',
            'waste' => 'Waste Generated in Operations',
            'fuel_energy' => 'Fuel and Energy Related Activities',
        ];

        return $mapping[strtolower($activityType)] ?? $activityType;
    }

    /**
     * Expand unit names
     */
    protected function expandUnitName(string $unit): string
    {
        $mapping = [
            'tCO2e' => 'Tonnes CO2 Equivalent',
            'kgCO2e' => 'Kilograms CO2 Equivalent',
            'MtCO2e' => 'Megatonnes CO2 Equivalent',
            'GtCO2e' => 'Gigatonnes CO2 Equivalent',
        ];

        return $mapping[$unit] ?? $unit;
    }
}
