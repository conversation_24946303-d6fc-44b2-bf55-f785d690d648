<?php

namespace App\Services;

use App\Models\Organization;
use App\Models\Facility;
use App\Models\ActivityData;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class MarginalAbatementCostService
{
    protected array $abatementOptions = [];
    
    public function __construct()
    {
        $this->loadAbatementOptions();
    }

    /**
     * Generate marginal abatement cost curve (MACC)
     */
    public function generateMACCurve(Organization $organization, array $options = []): array
    {
        $targetReduction = $options['target_reduction'] ?? 0.5; // 50% default
        $timeframe = $options['timeframe'] ?? 10; // 10 years
        $currency = $options['currency'] ?? 'USD';
        
        try {
            // Get current emissions baseline
            $baseline = $this->calculateEmissionsBaseline($organization);
            
            if (!$baseline['success']) {
                return [
                    'success' => false,
                    'error' => 'Unable to calculate emissions baseline',
                ];
            }

            // Identify applicable abatement options
            $applicableOptions = $this->identifyApplicableOptions($organization, $baseline);
            
            // Calculate costs and potentials for each option
            $costAnalysis = $this->calculateAbatementCosts($applicableOptions, $baseline, $timeframe);
            
            // Sort by cost-effectiveness ($/tCO2e)
            $sortedOptions = $this->sortByCostEffectiveness($costAnalysis);
            
            // Generate cumulative curve data
            $curveData = $this->generateCurveData($sortedOptions, $baseline['total_emissions'], $targetReduction);
            
            // Identify optimal portfolio
            $optimalPortfolio = $this->identifyOptimalPortfolio($sortedOptions, $targetReduction, $baseline['total_emissions']);

            return [
                'success' => true,
                'baseline' => $baseline,
                'target_reduction' => $targetReduction,
                'timeframe' => $timeframe,
                'currency' => $currency,
                'abatement_options' => $sortedOptions,
                'curve_data' => $curveData,
                'optimal_portfolio' => $optimalPortfolio,
                'summary' => $this->generateSummary($optimalPortfolio, $baseline),
            ];

        } catch (\Exception $e) {
            Log::error('MACC generation failed', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Calculate emissions baseline
     */
    protected function calculateEmissionsBaseline(Organization $organization): array
    {
        $currentYear = now()->year;
        
        $emissions = ActivityData::where('organization_id', $organization->id)
            ->whereYear('date_recorded', $currentYear - 1) // Use previous year
            ->with(['emissionCalculation', 'facility'])
            ->get();

        if ($emissions->isEmpty()) {
            return [
                'success' => false,
                'error' => 'No recent emissions data found',
            ];
        }

        $scope1 = $emissions->where('scope', '1')->sum('calculated_emissions');
        $scope2 = $emissions->where('scope', '2')->sum('calculated_emissions');
        $scope3 = $emissions->where('scope', '3')->sum('calculated_emissions');
        $total = $scope1 + $scope2 + $scope3;

        // Breakdown by facility
        $byFacility = $emissions->groupBy('facility_id')->map(function ($facilityEmissions) {
            return [
                'facility_name' => $facilityEmissions->first()->facility->name ?? 'Unknown',
                'emissions' => $facilityEmissions->sum('calculated_emissions'),
                'scope_1' => $facilityEmissions->where('scope', '1')->sum('calculated_emissions'),
                'scope_2' => $facilityEmissions->where('scope', '2')->sum('calculated_emissions'),
                'scope_3' => $facilityEmissions->where('scope', '3')->sum('calculated_emissions'),
            ];
        });

        return [
            'success' => true,
            'year' => $currentYear - 1,
            'total_emissions' => $total,
            'scope_1' => $scope1,
            'scope_2' => $scope2,
            'scope_3' => $scope3,
            'by_facility' => $byFacility->toArray(),
        ];
    }

    /**
     * Identify applicable abatement options
     */
    protected function identifyApplicableOptions(Organization $organization, array $baseline): array
    {
        $applicable = [];
        $sector = $organization->industry_sector ?? 'general';
        $totalEmissions = $baseline['total_emissions'];

        foreach ($this->abatementOptions as $optionId => $option) {
            // Check sector applicability
            if (!in_array($sector, $option['applicable_sectors']) && !in_array('all', $option['applicable_sectors'])) {
                continue;
            }

            // Check minimum emissions threshold
            if ($totalEmissions < $option['minimum_emissions_threshold']) {
                continue;
            }

            // Check scope applicability
            $scopeEmissions = 0;
            foreach ($option['applicable_scopes'] as $scope) {
                $scopeEmissions += $baseline["scope_{$scope}"] ?? 0;
            }

            if ($scopeEmissions < $option['minimum_scope_emissions']) {
                continue;
            }

            $applicable[$optionId] = array_merge($option, [
                'applicable_emissions' => $scopeEmissions,
                'organization_specific' => $this->customizeForOrganization($option, $organization, $baseline),
            ]);
        }

        return $applicable;
    }

    /**
     * Calculate abatement costs for each option
     */
    protected function calculateAbatementCosts(array $options, array $baseline, int $timeframe): array
    {
        $analysis = [];

        foreach ($options as $optionId => $option) {
            $applicableEmissions = $option['applicable_emissions'];
            
            // Calculate reduction potential
            $reductionPotential = min(
                $applicableEmissions * $option['max_reduction_percentage'],
                $option['max_absolute_reduction']
            );

            // Calculate costs
            $capitalCost = $this->calculateCapitalCost($option, $reductionPotential, $baseline);
            $operationalCost = $this->calculateOperationalCost($option, $reductionPotential, $timeframe);
            $totalCost = $capitalCost + $operationalCost;

            // Calculate cost per tonne
            $costPerTonne = $reductionPotential > 0 ? $totalCost / $reductionPotential : 0;

            // Calculate payback period
            $annualSavings = $this->calculateAnnualSavings($option, $reductionPotential);
            $paybackPeriod = $annualSavings > 0 ? $capitalCost / $annualSavings : null;

            $analysis[$optionId] = [
                'option_id' => $optionId,
                'name' => $option['name'],
                'category' => $option['category'],
                'reduction_potential' => $reductionPotential,
                'capital_cost' => $capitalCost,
                'operational_cost' => $operationalCost,
                'total_cost' => $totalCost,
                'cost_per_tonne' => $costPerTonne,
                'annual_savings' => $annualSavings,
                'payback_period' => $paybackPeriod,
                'implementation_complexity' => $option['implementation_complexity'],
                'implementation_time' => $option['implementation_time'],
                'co_benefits' => $option['co_benefits'],
                'risks' => $option['risks'],
            ];
        }

        return $analysis;
    }

    /**
     * Sort options by cost-effectiveness
     */
    protected function sortByCostEffectiveness(array $analysis): array
    {
        // Sort by cost per tonne (ascending), with negative costs first
        uasort($analysis, function ($a, $b) {
            return $a['cost_per_tonne'] <=> $b['cost_per_tonne'];
        });

        return array_values($analysis);
    }

    /**
     * Generate cumulative curve data
     */
    protected function generateCurveData(array $sortedOptions, float $totalEmissions, float $targetReduction): array
    {
        $curveData = [];
        $cumulativeReduction = 0;
        $cumulativeCost = 0;

        foreach ($sortedOptions as $option) {
            $cumulativeReduction += $option['reduction_potential'];
            $cumulativeCost += $option['total_cost'];
            
            $reductionPercentage = ($cumulativeReduction / $totalEmissions) * 100;
            
            $curveData[] = [
                'option_name' => $option['name'],
                'cumulative_reduction_tonnes' => $cumulativeReduction,
                'cumulative_reduction_percentage' => $reductionPercentage,
                'cumulative_cost' => $cumulativeCost,
                'marginal_cost' => $option['cost_per_tonne'],
                'option_reduction' => $option['reduction_potential'],
                'option_cost' => $option['total_cost'],
            ];

            // Stop if we've reached the target reduction
            if ($reductionPercentage >= $targetReduction * 100) {
                break;
            }
        }

        return $curveData;
    }

    /**
     * Identify optimal portfolio to meet target
     */
    protected function identifyOptimalPortfolio(array $sortedOptions, float $targetReduction, float $totalEmissions): array
    {
        $portfolio = [];
        $cumulativeReduction = 0;
        $cumulativeCost = 0;
        $targetReductionAbsolute = $totalEmissions * $targetReduction;

        foreach ($sortedOptions as $option) {
            if ($cumulativeReduction >= $targetReductionAbsolute) {
                break;
            }

            $remainingReduction = $targetReductionAbsolute - $cumulativeReduction;
            $optionReduction = min($option['reduction_potential'], $remainingReduction);
            
            // Scale costs proportionally if using partial reduction
            $scaleFactor = $optionReduction / $option['reduction_potential'];
            $scaledCost = $option['total_cost'] * $scaleFactor;

            $portfolio[] = [
                'option_id' => $option['option_id'],
                'name' => $option['name'],
                'category' => $option['category'],
                'reduction_used' => $optionReduction,
                'reduction_percentage' => ($optionReduction / $option['reduction_potential']) * 100,
                'cost' => $scaledCost,
                'cost_per_tonne' => $option['cost_per_tonne'],
                'implementation_priority' => count($portfolio) + 1,
            ];

            $cumulativeReduction += $optionReduction;
            $cumulativeCost += $scaledCost;
        }

        return [
            'options' => $portfolio,
            'total_reduction' => $cumulativeReduction,
            'total_cost' => $cumulativeCost,
            'average_cost_per_tonne' => $cumulativeReduction > 0 ? $cumulativeCost / $cumulativeReduction : 0,
            'target_achieved' => $cumulativeReduction >= $targetReductionAbsolute,
        ];
    }

    /**
     * Calculate capital cost for option
     */
    protected function calculateCapitalCost(array $option, float $reductionPotential, array $baseline): float
    {
        $baseCost = $option['capital_cost_per_tonne'] * $reductionPotential;
        
        // Apply scaling factors
        $scalingFactor = $this->getScalingFactor($option, $baseline['total_emissions']);
        
        return $baseCost * $scalingFactor;
    }

    /**
     * Calculate operational cost for option
     */
    protected function calculateOperationalCost(array $option, float $reductionPotential, int $timeframe): float
    {
        $annualOpCost = $option['operational_cost_per_tonne'] * $reductionPotential;
        
        // Present value of operational costs over timeframe
        $discountRate = 0.08; // 8% discount rate
        $presentValue = 0;
        
        for ($year = 1; $year <= $timeframe; $year++) {
            $presentValue += $annualOpCost / pow(1 + $discountRate, $year);
        }
        
        return $presentValue;
    }

    /**
     * Calculate annual savings from option
     */
    protected function calculateAnnualSavings(array $option, float $reductionPotential): float
    {
        $energySavings = $option['energy_savings_per_tonne'] * $reductionPotential * 50; // $50/MWh
        $carbonPriceSavings = $reductionPotential * 25; // $25/tCO2e carbon price
        $otherSavings = $option['other_savings_per_tonne'] * $reductionPotential;
        
        return $energySavings + $carbonPriceSavings + $otherSavings;
    }

    /**
     * Get scaling factor based on organization size
     */
    protected function getScalingFactor(array $option, float $totalEmissions): float
    {
        // Economies of scale for larger organizations
        if ($totalEmissions > 100000) { // >100k tCO2e
            return 0.8; // 20% cost reduction
        } elseif ($totalEmissions > 10000) { // >10k tCO2e
            return 0.9; // 10% cost reduction
        } else {
            return 1.1; // 10% cost premium for small organizations
        }
    }

    /**
     * Customize option for specific organization
     */
    protected function customizeForOrganization(array $option, Organization $organization, array $baseline): array
    {
        // Organization-specific customizations would go here
        return [
            'feasibility_score' => $this->calculateFeasibilityScore($option, $organization),
            'implementation_barriers' => $this->identifyImplementationBarriers($option, $organization),
            'local_incentives' => $this->identifyLocalIncentives($option, $organization),
        ];
    }

    /**
     * Calculate feasibility score
     */
    protected function calculateFeasibilityScore(array $option, Organization $organization): float
    {
        $score = 100;
        
        // Reduce score based on complexity
        $score -= $option['implementation_complexity'] * 10;
        
        // Adjust for organization size
        $facilityCount = $organization->facilities()->count();
        if ($facilityCount < 5 && $option['implementation_complexity'] > 7) {
            $score -= 20; // Penalty for complex options in small organizations
        }
        
        return max(0, min(100, $score));
    }

    /**
     * Identify implementation barriers
     */
    protected function identifyImplementationBarriers(array $option, Organization $organization): array
    {
        $barriers = [];
        
        if ($option['capital_cost_per_tonne'] > 1000) {
            $barriers[] = 'High capital investment required';
        }
        
        if ($option['implementation_complexity'] > 8) {
            $barriers[] = 'High technical complexity';
        }
        
        if ($option['implementation_time'] > 24) {
            $barriers[] = 'Long implementation timeline';
        }
        
        return $barriers;
    }

    /**
     * Identify local incentives
     */
    protected function identifyLocalIncentives(array $option, Organization $organization): array
    {
        // This would integrate with local incentive databases
        return [
            'tax_credits' => 'Available for renewable energy projects',
            'grants' => 'Energy efficiency grants available',
            'rebates' => 'Utility rebates for demand response',
        ];
    }

    /**
     * Generate summary
     */
    protected function generateSummary(array $portfolio, array $baseline): array
    {
        $totalCost = $portfolio['total_cost'];
        $totalReduction = $portfolio['total_reduction'];
        $avgCostPerTonne = $portfolio['average_cost_per_tonne'];
        
        return [
            'total_investment_required' => $totalCost,
            'total_emissions_reduced' => $totalReduction,
            'reduction_percentage' => ($totalReduction / $baseline['total_emissions']) * 100,
            'average_cost_per_tonne' => $avgCostPerTonne,
            'number_of_initiatives' => count($portfolio['options']),
            'cost_effectiveness_rating' => $this->getCostEffectivenessRating($avgCostPerTonne),
            'implementation_timeline' => $this->estimateImplementationTimeline($portfolio['options']),
        ];
    }

    /**
     * Get cost effectiveness rating
     */
    protected function getCostEffectivenessRating(float $costPerTonne): string
    {
        if ($costPerTonne < 0) return 'Highly Profitable';
        if ($costPerTonne < 50) return 'Very Cost Effective';
        if ($costPerTonne < 100) return 'Cost Effective';
        if ($costPerTonne < 200) return 'Moderately Cost Effective';
        return 'High Cost';
    }

    /**
     * Estimate implementation timeline
     */
    protected function estimateImplementationTimeline(array $options): array
    {
        $phases = [
            'immediate' => [], // 0-12 months
            'short_term' => [], // 1-3 years
            'medium_term' => [], // 3-7 years
            'long_term' => [], // 7+ years
        ];

        foreach ($options as $option) {
            $implementationTime = $this->abatementOptions[$option['option_id']]['implementation_time'] ?? 12;
            
            if ($implementationTime <= 12) {
                $phases['immediate'][] = $option['name'];
            } elseif ($implementationTime <= 36) {
                $phases['short_term'][] = $option['name'];
            } elseif ($implementationTime <= 84) {
                $phases['medium_term'][] = $option['name'];
            } else {
                $phases['long_term'][] = $option['name'];
            }
        }

        return $phases;
    }

    /**
     * Load abatement options database
     */
    protected function loadAbatementOptions(): void
    {
        $this->abatementOptions = [
            'led_lighting' => [
                'name' => 'LED Lighting Retrofit',
                'category' => 'Energy Efficiency',
                'applicable_sectors' => ['all'],
                'applicable_scopes' => [2],
                'minimum_emissions_threshold' => 100,
                'minimum_scope_emissions' => 50,
                'max_reduction_percentage' => 0.05, // 5% of scope 2
                'max_absolute_reduction' => 1000,
                'capital_cost_per_tonne' => 200,
                'operational_cost_per_tonne' => -50, // Savings
                'energy_savings_per_tonne' => 2.5, // MWh per tonne
                'other_savings_per_tonne' => 10,
                'implementation_complexity' => 3, // 1-10 scale
                'implementation_time' => 6, // months
                'co_benefits' => ['Improved lighting quality', 'Reduced maintenance'],
                'risks' => ['Technology obsolescence'],
            ],
            'renewable_energy' => [
                'name' => 'On-site Renewable Energy',
                'category' => 'Renewable Energy',
                'applicable_sectors' => ['all'],
                'applicable_scopes' => [2],
                'minimum_emissions_threshold' => 500,
                'minimum_scope_emissions' => 200,
                'max_reduction_percentage' => 0.8, // 80% of scope 2
                'max_absolute_reduction' => 10000,
                'capital_cost_per_tonne' => 800,
                'operational_cost_per_tonne' => 20,
                'energy_savings_per_tonne' => 4.0,
                'other_savings_per_tonne' => 0,
                'implementation_complexity' => 7,
                'implementation_time' => 18,
                'co_benefits' => ['Energy independence', 'Brand value'],
                'risks' => ['Weather dependency', 'Technology risk'],
            ],
            'hvac_optimization' => [
                'name' => 'HVAC System Optimization',
                'category' => 'Energy Efficiency',
                'applicable_sectors' => ['services', 'manufacturing'],
                'applicable_scopes' => [2],
                'minimum_emissions_threshold' => 200,
                'minimum_scope_emissions' => 100,
                'max_reduction_percentage' => 0.15, // 15% of scope 2
                'max_absolute_reduction' => 2000,
                'capital_cost_per_tonne' => 300,
                'operational_cost_per_tonne' => -20,
                'energy_savings_per_tonne' => 3.0,
                'other_savings_per_tonne' => 5,
                'implementation_complexity' => 5,
                'implementation_time' => 12,
                'co_benefits' => ['Improved comfort', 'Better air quality'],
                'risks' => ['Disruption during installation'],
            ],
            'process_efficiency' => [
                'name' => 'Industrial Process Efficiency',
                'category' => 'Process Optimization',
                'applicable_sectors' => ['manufacturing', 'energy'],
                'applicable_scopes' => [1, 2],
                'minimum_emissions_threshold' => 1000,
                'minimum_scope_emissions' => 500,
                'max_reduction_percentage' => 0.25, // 25% of applicable scopes
                'max_absolute_reduction' => 5000,
                'capital_cost_per_tonne' => 500,
                'operational_cost_per_tonne' => -30,
                'energy_savings_per_tonne' => 3.5,
                'other_savings_per_tonne' => 15,
                'implementation_complexity' => 8,
                'implementation_time' => 24,
                'co_benefits' => ['Increased productivity', 'Reduced waste'],
                'risks' => ['Production disruption', 'Technical complexity'],
            ],
            'electrification' => [
                'name' => 'Fleet Electrification',
                'category' => 'Transportation',
                'applicable_sectors' => ['transportation', 'services', 'manufacturing'],
                'applicable_scopes' => [1, 2],
                'minimum_emissions_threshold' => 300,
                'minimum_scope_emissions' => 150,
                'max_reduction_percentage' => 0.7, // 70% of transport emissions
                'max_absolute_reduction' => 3000,
                'capital_cost_per_tonne' => 1200,
                'operational_cost_per_tonne' => -40,
                'energy_savings_per_tonne' => 0,
                'other_savings_per_tonne' => 25,
                'implementation_complexity' => 6,
                'implementation_time' => 36,
                'co_benefits' => ['Reduced air pollution', 'Lower fuel costs'],
                'risks' => ['Charging infrastructure', 'Range limitations'],
            ],
        ];
    }

    /**
     * Get available abatement options
     */
    public function getAbatementOptions(): array
    {
        return $this->abatementOptions;
    }

    /**
     * Add custom abatement option
     */
    public function addCustomOption(string $id, array $option): void
    {
        $this->abatementOptions[$id] = $option;
    }
}
