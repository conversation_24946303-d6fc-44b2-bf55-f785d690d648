<?php

namespace App\Services;

use App\Models\Organization;
use App\Models\Facility;
use App\Models\ActivityData;
use App\Models\Supplier;
use App\Models\EmissionCalculation;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class DrillDownAnalysisService
{
    /**
     * Generate comprehensive drill-down analysis from corporate totals to individual sources
     */
    public function generateDrillDownAnalysis(Organization $organization, array $options = []): array
    {
        $reportingYear = $options['reporting_year'] ?? now()->year - 1;
        $includeSuppliers = $options['include_suppliers'] ?? true;
        $includeCalculationDetails = $options['include_calculation_details'] ?? true;
        
        try {
            Log::info("Starting drill-down analysis", [
                'organization_id' => $organization->id,
                'reporting_year' => $reportingYear,
            ]);

            // Level 1: Corporate Total
            $corporateTotal = $this->calculateCorporateTotal($organization, $reportingYear);
            
            // Level 2: Scope Breakdown
            $scopeBreakdown = $this->generateScopeBreakdown($organization, $reportingYear);
            
            // Level 3: Facility Breakdown
            $facilityBreakdown = $this->generateFacilityBreakdown($organization, $reportingYear);
            
            // Level 4: Source Breakdown
            $sourceBreakdown = $this->generateSourceBreakdown($organization, $reportingYear);
            
            // Level 5: Activity-Level Detail
            $activityDetail = $this->generateActivityDetail($organization, $reportingYear, $includeCalculationDetails);
            
            // Level 6: Supplier Breakdown (if requested)
            $supplierBreakdown = $includeSuppliers ? 
                $this->generateSupplierBreakdown($organization, $reportingYear) : null;

            // Generate traceability matrix
            $traceabilityMatrix = $this->generateTraceabilityMatrix($organization, $reportingYear);
            
            // Generate drill-down paths
            $drillDownPaths = $this->generateDrillDownPaths($organization, $reportingYear);

            return [
                'success' => true,
                'organization_id' => $organization->id,
                'organization_name' => $organization->name,
                'reporting_year' => $reportingYear,
                'generated_at' => now(),
                'drill_down_levels' => [
                    'level_1_corporate' => $corporateTotal,
                    'level_2_scope' => $scopeBreakdown,
                    'level_3_facility' => $facilityBreakdown,
                    'level_4_source' => $sourceBreakdown,
                    'level_5_activity' => $activityDetail,
                    'level_6_supplier' => $supplierBreakdown,
                ],
                'traceability_matrix' => $traceabilityMatrix,
                'drill_down_paths' => $drillDownPaths,
                'summary_statistics' => $this->generateSummaryStatistics($corporateTotal, $facilityBreakdown),
            ];

        } catch (\Exception $e) {
            Log::error("Drill-down analysis failed", [
                'organization_id' => $organization->id,
                'reporting_year' => $reportingYear,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'organization_id' => $organization->id,
                'reporting_year' => $reportingYear,
            ];
        }
    }

    /**
     * Calculate corporate total emissions
     */
    protected function calculateCorporateTotal(Organization $organization, int $reportingYear): array
    {
        $emissions = ActivityData::where('organization_id', $organization->id)
            ->whereYear('date_recorded', $reportingYear)
            ->with(['emissionCalculation'])
            ->get();

        $scope1 = $emissions->where('scope', '1')->sum('calculated_emissions');
        $scope2 = $emissions->where('scope', '2')->sum('calculated_emissions');
        $scope3 = $emissions->where('scope', '3')->sum('calculated_emissions');
        $total = $scope1 + $scope2 + $scope3;

        return [
            'level' => 1,
            'description' => 'Corporate Total Emissions',
            'total_emissions' => $total,
            'scope_1' => $scope1,
            'scope_2' => $scope2,
            'scope_3' => $scope3,
            'unit' => 'tCO2e',
            'data_points' => $emissions->count(),
            'facilities_count' => $emissions->pluck('facility_id')->unique()->count(),
            'reporting_boundary' => $this->describeCorporateBoundary($organization),
            'consolidation_approach' => 'Operational Control',
            'base_year_comparison' => $this->getBaseYearComparison($organization, $reportingYear),
        ];
    }

    /**
     * Generate scope-level breakdown
     */
    protected function generateScopeBreakdown(Organization $organization, int $reportingYear): array
    {
        $emissions = ActivityData::where('organization_id', $organization->id)
            ->whereYear('date_recorded', $reportingYear)
            ->with(['emissionCalculation', 'facility', 'emissionSource'])
            ->get();

        $scopeBreakdown = [];

        foreach (['1', '2', '3'] as $scope) {
            $scopeEmissions = $emissions->where('scope', $scope);
            $scopeTotal = $scopeEmissions->sum('calculated_emissions');

            $breakdown = [
                'scope' => $scope,
                'scope_name' => $this->getScopeName($scope),
                'total_emissions' => $scopeTotal,
                'percentage_of_total' => $this->calculatePercentage($scopeTotal, $emissions->sum('calculated_emissions')),
                'data_points' => $scopeEmissions->count(),
                'facilities_involved' => $scopeEmissions->pluck('facility_id')->unique()->count(),
                'emission_sources' => $scopeEmissions->groupBy('emission_source_id')->map(function ($sourceEmissions) {
                    return [
                        'source_name' => $sourceEmissions->first()->emissionSource->name ?? 'Unknown',
                        'emissions' => $sourceEmissions->sum('calculated_emissions'),
                        'data_points' => $sourceEmissions->count(),
                    ];
                })->values()->toArray(),
            ];

            // Add scope-specific details
            if ($scope === '2') {
                $breakdown['scope_2_methods'] = $this->analyzeScope2Methods($scopeEmissions);
            } elseif ($scope === '3') {
                $breakdown['scope_3_categories'] = $this->analyzeScope3Categories($scopeEmissions);
            }

            $scopeBreakdown[] = $breakdown;
        }

        return [
            'level' => 2,
            'description' => 'Scope-Level Breakdown',
            'scopes' => $scopeBreakdown,
        ];
    }

    /**
     * Generate facility-level breakdown
     */
    protected function generateFacilityBreakdown(Organization $organization, int $reportingYear): array
    {
        $emissions = ActivityData::where('organization_id', $organization->id)
            ->whereYear('date_recorded', $reportingYear)
            ->with(['emissionCalculation', 'facility', 'emissionSource'])
            ->get();

        $facilityBreakdown = $emissions->groupBy('facility_id')->map(function ($facilityEmissions, $facilityId) {
            $facility = $facilityEmissions->first()->facility;
            
            $scope1 = $facilityEmissions->where('scope', '1')->sum('calculated_emissions');
            $scope2 = $facilityEmissions->where('scope', '2')->sum('calculated_emissions');
            $scope3 = $facilityEmissions->where('scope', '3')->sum('calculated_emissions');
            $total = $scope1 + $scope2 + $scope3;

            return [
                'facility_id' => $facilityId,
                'facility_name' => $facility->name ?? 'Unknown',
                'facility_type' => $facility->facility_type ?? 'Unknown',
                'location' => [
                    'address' => $facility->address ?? 'Unknown',
                    'city' => $facility->city ?? 'Unknown',
                    'country' => $facility->country ?? 'Unknown',
                ],
                'total_emissions' => $total,
                'scope_1' => $scope1,
                'scope_2' => $scope2,
                'scope_3' => $scope3,
                'percentage_of_corporate' => $this->calculatePercentage($total, $emissions->sum('calculated_emissions')),
                'data_points' => $facilityEmissions->count(),
                'emission_sources' => $facilityEmissions->groupBy('emission_source_id')->map(function ($sourceEmissions) {
                    return [
                        'source_name' => $sourceEmissions->first()->emissionSource->name ?? 'Unknown',
                        'scope' => $sourceEmissions->first()->scope,
                        'emissions' => $sourceEmissions->sum('calculated_emissions'),
                        'data_points' => $sourceEmissions->count(),
                    ];
                })->values()->toArray(),
                'operational_metrics' => $this->getFacilityOperationalMetrics($facility, $facilityEmissions),
            ];
        })->values();

        return [
            'level' => 3,
            'description' => 'Facility-Level Breakdown',
            'facilities' => $facilityBreakdown->toArray(),
            'facility_ranking' => $this->rankFacilitiesByEmissions($facilityBreakdown),
        ];
    }

    /**
     * Generate emission source breakdown
     */
    protected function generateSourceBreakdown(Organization $organization, int $reportingYear): array
    {
        $emissions = ActivityData::where('organization_id', $organization->id)
            ->whereYear('date_recorded', $reportingYear)
            ->with(['emissionCalculation', 'facility', 'emissionSource', 'emissionFactor'])
            ->get();

        $sourceBreakdown = $emissions->groupBy('emission_source_id')->map(function ($sourceEmissions, $sourceId) {
            $source = $sourceEmissions->first()->emissionSource;
            
            return [
                'source_id' => $sourceId,
                'source_name' => $source->name ?? 'Unknown',
                'source_category' => $source->category ?? 'Unknown',
                'total_emissions' => $sourceEmissions->sum('calculated_emissions'),
                'scope_distribution' => [
                    'scope_1' => $sourceEmissions->where('scope', '1')->sum('calculated_emissions'),
                    'scope_2' => $sourceEmissions->where('scope', '2')->sum('calculated_emissions'),
                    'scope_3' => $sourceEmissions->where('scope', '3')->sum('calculated_emissions'),
                ],
                'facility_distribution' => $sourceEmissions->groupBy('facility_id')->map(function ($facilityEmissions, $facilityId) {
                    return [
                        'facility_id' => $facilityId,
                        'facility_name' => $facilityEmissions->first()->facility->name ?? 'Unknown',
                        'emissions' => $facilityEmissions->sum('calculated_emissions'),
                        'data_points' => $facilityEmissions->count(),
                    ];
                })->values()->toArray(),
                'emission_factors_used' => $sourceEmissions->groupBy('emission_factor_id')->map(function ($factorEmissions, $factorId) {
                    $factor = $factorEmissions->first()->emissionFactor;
                    return [
                        'factor_id' => $factorId,
                        'factor_name' => $factor->name ?? 'Unknown',
                        'factor_value' => $factor->factor_value ?? 0,
                        'factor_unit' => $factor->unit ?? 'Unknown',
                        'data_source' => $factor->data_source ?? 'Unknown',
                        'usage_count' => $factorEmissions->count(),
                        'total_activity' => $factorEmissions->sum('quantity'),
                        'total_emissions' => $factorEmissions->sum('calculated_emissions'),
                    ];
                })->values()->toArray(),
                'data_points' => $sourceEmissions->count(),
                'date_range' => [
                    'first_record' => $sourceEmissions->min('date_recorded'),
                    'last_record' => $sourceEmissions->max('date_recorded'),
                ],
            ];
        })->values();

        return [
            'level' => 4,
            'description' => 'Emission Source Breakdown',
            'sources' => $sourceBreakdown->toArray(),
            'source_ranking' => $this->rankSourcesByEmissions($sourceBreakdown),
        ];
    }

    /**
     * Generate activity-level detail
     */
    protected function generateActivityDetail(Organization $organization, int $reportingYear, bool $includeCalculationDetails): array
    {
        $activities = ActivityData::where('organization_id', $organization->id)
            ->whereYear('date_recorded', $reportingYear)
            ->with(['emissionCalculation', 'facility', 'emissionSource', 'emissionFactor', 'activityUnit'])
            ->orderBy('calculated_emissions', 'desc')
            ->limit(100) // Limit to top 100 for performance
            ->get();

        $activityDetail = $activities->map(function ($activity) use ($includeCalculationDetails) {
            $detail = [
                'activity_id' => $activity->id,
                'facility_name' => $activity->facility->name ?? 'Unknown',
                'emission_source' => $activity->emissionSource->name ?? 'Unknown',
                'scope' => $activity->scope,
                'date_recorded' => $activity->date_recorded,
                'quantity' => $activity->quantity,
                'unit' => $activity->activityUnit->symbol ?? 'Unknown',
                'calculated_emissions' => $activity->calculated_emissions,
                'emission_factor' => [
                    'name' => $activity->emissionFactor->name ?? 'Unknown',
                    'value' => $activity->emissionFactor->factor_value ?? 0,
                    'unit' => $activity->emissionFactor->unit ?? 'Unknown',
                    'data_source' => $activity->emissionFactor->data_source ?? 'Unknown',
                ],
                'data_quality' => $activity->data_quality_rating ?? 'Unknown',
                'recorded_by' => $activity->recorded_by ?? 'Unknown',
            ];

            if ($includeCalculationDetails && $activity->emissionCalculation) {
                $detail['calculation_details'] = [
                    'calculation_method' => $activity->emissionCalculation->calculation_method,
                    'co2_emissions' => $activity->emissionCalculation->co2_emissions,
                    'ch4_emissions' => $activity->emissionCalculation->ch4_emissions,
                    'n2o_emissions' => $activity->emissionCalculation->n2o_emissions,
                    'total_co2e' => $activity->emissionCalculation->total_co2e,
                    'uncertainty_percentage' => $activity->emissionCalculation->uncertainty_percentage,
                    'calculation_timestamp' => $activity->emissionCalculation->created_at,
                ];
            }

            return $detail;
        });

        return [
            'level' => 5,
            'description' => 'Activity-Level Detail (Top 100 by Emissions)',
            'activities' => $activityDetail->toArray(),
            'total_activities' => ActivityData::where('organization_id', $organization->id)
                ->whereYear('date_recorded', $reportingYear)
                ->count(),
        ];
    }

    /**
     * Generate supplier breakdown for Scope 3
     */
    protected function generateSupplierBreakdown(Organization $organization, int $reportingYear): array
    {
        $scope3Emissions = ActivityData::where('organization_id', $organization->id)
            ->where('scope', '3')
            ->whereYear('date_recorded', $reportingYear)
            ->whereNotNull('supplier_id')
            ->with(['supplier', 'scope3Category'])
            ->get();

        if ($scope3Emissions->isEmpty()) {
            return [
                'level' => 6,
                'description' => 'Supplier-Level Breakdown',
                'message' => 'No supplier-specific Scope 3 data available',
                'suppliers' => [],
            ];
        }

        $supplierBreakdown = $scope3Emissions->groupBy('supplier_id')->map(function ($supplierEmissions, $supplierId) {
            $supplier = $supplierEmissions->first()->supplier;
            
            return [
                'supplier_id' => $supplierId,
                'supplier_name' => $supplier->name ?? 'Unknown',
                'supplier_type' => $supplier->supplier_type ?? 'Unknown',
                'industry_sector' => $supplier->industry_sector ?? 'Unknown',
                'country' => $supplier->country ?? 'Unknown',
                'total_emissions' => $supplierEmissions->sum('calculated_emissions'),
                'scope3_categories' => $supplierEmissions->groupBy('scope3_category_id')->map(function ($categoryEmissions, $categoryId) {
                    $category = $categoryEmissions->first()->scope3Category;
                    return [
                        'category_id' => $categoryId,
                        'category_name' => $category->name ?? 'Unknown',
                        'emissions' => $categoryEmissions->sum('calculated_emissions'),
                        'data_points' => $categoryEmissions->count(),
                    ];
                })->values()->toArray(),
                'data_quality_rating' => $supplier->data_quality_rating ?? 'Unknown',
                'carbon_disclosure_level' => $supplier->carbon_disclosure_level ?? 'Unknown',
                'annual_spend' => $supplier->annual_spend ?? 0,
                'data_points' => $supplierEmissions->count(),
            ];
        })->values();

        return [
            'level' => 6,
            'description' => 'Supplier-Level Breakdown',
            'suppliers' => $supplierBreakdown->toArray(),
            'supplier_ranking' => $this->rankSuppliersByEmissions($supplierBreakdown),
        ];
    }

    /**
     * Generate traceability matrix
     */
    protected function generateTraceabilityMatrix(Organization $organization, int $reportingYear): array
    {
        return [
            'corporate_to_facility' => $this->generateCorporateToFacilityTrace($organization, $reportingYear),
            'facility_to_source' => $this->generateFacilityToSourceTrace($organization, $reportingYear),
            'source_to_activity' => $this->generateSourceToActivityTrace($organization, $reportingYear),
            'activity_to_calculation' => $this->generateActivityToCalculationTrace($organization, $reportingYear),
        ];
    }

    /**
     * Generate drill-down paths
     */
    protected function generateDrillDownPaths(Organization $organization, int $reportingYear): array
    {
        return [
            'highest_emitting_facility_path' => $this->getHighestEmittingFacilityPath($organization, $reportingYear),
            'highest_emitting_source_path' => $this->getHighestEmittingSourcePath($organization, $reportingYear),
            'scope_3_supplier_path' => $this->getScope3SupplierPath($organization, $reportingYear),
            'data_quality_concern_path' => $this->getDataQualityConcernPath($organization, $reportingYear),
        ];
    }

    /**
     * Helper methods
     */
    protected function getScopeName(string $scope): string
    {
        return match($scope) {
            '1' => 'Direct Emissions',
            '2' => 'Indirect Energy Emissions',
            '3' => 'Other Indirect Emissions',
            default => 'Unknown Scope',
        };
    }

    protected function calculatePercentage(float $value, float $total): float
    {
        return $total > 0 ? round(($value / $total) * 100, 2) : 0;
    }

    protected function describeCorporateBoundary(Organization $organization): array
    {
        return [
            'approach' => 'Operational Control',
            'facilities_included' => $organization->facilities()->count(),
            'subsidiaries_included' => 'All majority-owned subsidiaries',
            'joint_ventures' => 'Proportional consolidation based on ownership percentage',
        ];
    }

    protected function getBaseYearComparison(Organization $organization, int $reportingYear): array
    {
        // This would compare with base year emissions
        return [
            'base_year' => 2019,
            'base_year_emissions' => 0, // Placeholder
            'change_percentage' => 0, // Placeholder
            'change_absolute' => 0, // Placeholder
        ];
    }

    protected function analyzeScope2Methods(Collection $scope2Emissions): array
    {
        return [
            'location_based' => $scope2Emissions->where('scope2_method', 'location_based')->sum('calculated_emissions'),
            'market_based' => $scope2Emissions->where('scope2_method', 'market_based')->sum('calculated_emissions'),
            'dual_reporting' => $scope2Emissions->where('scope2_method', 'dual_reporting')->sum('calculated_emissions'),
        ];
    }

    protected function analyzeScope3Categories(Collection $scope3Emissions): array
    {
        return $scope3Emissions->groupBy('scope3_category_id')->map(function ($categoryEmissions, $categoryId) {
            $category = $categoryEmissions->first()->scope3Category ?? null;
            return [
                'category_id' => $categoryId,
                'category_name' => $category->name ?? 'Unknown',
                'emissions' => $categoryEmissions->sum('calculated_emissions'),
                'data_points' => $categoryEmissions->count(),
            ];
        })->values()->toArray();
    }

    protected function getFacilityOperationalMetrics(object $facility, Collection $facilityEmissions): array
    {
        return [
            'emissions_intensity' => 'TBD', // Would calculate based on production data
            'energy_consumption' => 'TBD', // Would aggregate energy data
            'production_volume' => 'TBD', // Would get from facility data
        ];
    }

    protected function rankFacilitiesByEmissions(Collection $facilities): array
    {
        return $facilities->sortByDesc('total_emissions')->take(10)->values()->toArray();
    }

    protected function rankSourcesByEmissions(Collection $sources): array
    {
        return $sources->sortByDesc('total_emissions')->take(10)->values()->toArray();
    }

    protected function rankSuppliersByEmissions(Collection $suppliers): array
    {
        return $suppliers->sortByDesc('total_emissions')->take(10)->values()->toArray();
    }

    protected function generateSummaryStatistics(array $corporateTotal, array $facilityBreakdown): array
    {
        return [
            'total_emissions' => $corporateTotal['total_emissions'],
            'total_facilities' => count($facilityBreakdown['facilities']),
            'total_data_points' => $corporateTotal['data_points'],
            'largest_facility_percentage' => $this->getLargestFacilityPercentage($facilityBreakdown),
            'top_5_facilities_percentage' => $this->getTop5FacilitiesPercentage($facilityBreakdown),
        ];
    }

    protected function getLargestFacilityPercentage(array $facilityBreakdown): float
    {
        if (empty($facilityBreakdown['facilities'])) return 0;
        
        $largest = collect($facilityBreakdown['facilities'])->max('percentage_of_corporate');
        return $largest ?? 0;
    }

    protected function getTop5FacilitiesPercentage(array $facilityBreakdown): float
    {
        if (empty($facilityBreakdown['facilities'])) return 0;
        
        return collect($facilityBreakdown['facilities'])
            ->sortByDesc('total_emissions')
            ->take(5)
            ->sum('percentage_of_corporate');
    }

    // Placeholder methods for traceability matrix and drill-down paths
    protected function generateCorporateToFacilityTrace(Organization $organization, int $reportingYear): array
    {
        return ['trace' => 'Corporate to facility traceability - implementation pending'];
    }

    protected function generateFacilityToSourceTrace(Organization $organization, int $reportingYear): array
    {
        return ['trace' => 'Facility to source traceability - implementation pending'];
    }

    protected function generateSourceToActivityTrace(Organization $organization, int $reportingYear): array
    {
        return ['trace' => 'Source to activity traceability - implementation pending'];
    }

    protected function generateActivityToCalculationTrace(Organization $organization, int $reportingYear): array
    {
        return ['trace' => 'Activity to calculation traceability - implementation pending'];
    }

    protected function getHighestEmittingFacilityPath(Organization $organization, int $reportingYear): array
    {
        return ['path' => 'Highest emitting facility path - implementation pending'];
    }

    protected function getHighestEmittingSourcePath(Organization $organization, int $reportingYear): array
    {
        return ['path' => 'Highest emitting source path - implementation pending'];
    }

    protected function getScope3SupplierPath(Organization $organization, int $reportingYear): array
    {
        return ['path' => 'Scope 3 supplier path - implementation pending'];
    }

    protected function getDataQualityConcernPath(Organization $organization, int $reportingYear): array
    {
        return ['path' => 'Data quality concern path - implementation pending'];
    }
}
