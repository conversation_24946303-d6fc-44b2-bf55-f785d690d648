<?php

namespace App\Services;

use App\Models\User;
use App\Models\Organization;
use App\Models\ActivityData;
use App\Models\CalculatedEmission;
use App\Models\EmissionTarget;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class DashboardService
{
    protected UserContextService $userContextService;

    public function __construct(UserContextService $userContextService)
    {
        $this->userContextService = $userContextService;
    }

    /**
     * Get dashboard data based on user type and organizational context
     */
    public function getDashboardData(User $user): array
    {
        $interfaceType = $this->userContextService->getUserInterfaceType($user);
        
        return match ($interfaceType) {
            'executive' => $this->getExecutiveDashboardData($user),
            'advanced' => $this->getAdvancedDashboardData($user),
            default => $this->getSimplifiedDashboardData($user),
        };
    }

    /**
     * Get simplified dashboard data for novice users
     */
    protected function getSimplifiedDashboardData(User $user): array
    {
        $context = $this->userContextService->getOrganizationalContext($user);
        
        return [
            'user_context' => $context,
            'interface_type' => 'simplified',
            'welcome_message' => $this->getWelcomeMessage($user),
            'impact_overview' => $this->getSimplifiedImpactOverview($user),
            'progress_summary' => $this->getProgressSummary($user),
            'pending_tasks' => $this->getPendingTasks($user),
            'quick_actions' => $this->userContextService->getQuickActions($user),
            'recent_activity' => $this->getRecentActivity($user, 5),
            'tips_and_insights' => $this->getTipsAndInsights($user),
        ];
    }

    /**
     * Get advanced dashboard data for expert users
     */
    protected function getAdvancedDashboardData(User $user): array
    {
        $simplifiedData = $this->getSimplifiedDashboardData($user);
        
        return array_merge($simplifiedData, [
            'interface_type' => 'advanced',
            'technical_metrics' => $this->getTechnicalMetrics($user),
            'ghg_protocol_breakdown' => $this->getGhgProtocolBreakdown($user),
            'data_quality_metrics' => $this->getDataQualityMetrics($user),
            'calculation_methods' => $this->getCalculationMethods($user),
            'emission_factors_status' => $this->getEmissionFactorsStatus($user),
            'uncertainty_analysis' => $this->getUncertaintyAnalysis($user),
        ]);
    }

    /**
     * Get executive dashboard data for C-suite users
     */
    protected function getExecutiveDashboardData(User $user): array
    {
        return [
            'user_context' => $this->userContextService->getOrganizationalContext($user),
            'interface_type' => 'executive',
            'organizational_overview' => $this->getOrganizationalOverview($user),
            'strategic_targets' => $this->getStrategicTargets($user),
            'compliance_status' => $this->getComplianceStatus($user),
            'portfolio_performance' => $this->getPortfolioPerformance($user),
            'risk_assessment' => $this->getRiskAssessment($user),
            'benchmarking' => $this->getBenchmarking($user),
            'action_items' => $this->getExecutiveActionItems($user),
        ];
    }

    /**
     * Get personalized welcome message
     */
    protected function getWelcomeMessage(User $user): array
    {
        $timeOfDay = $this->getTimeOfDay();
        $recentActivity = $this->getLastActivityDate($user);
        
        return [
            'greeting' => "Good {$timeOfDay}, {$user->name}!",
            'subtitle' => $this->getContextualSubtitle($user, $recentActivity),
            'facility' => $user->facility?->name,
            'department' => $user->department,
            'last_activity' => $recentActivity,
        ];
    }

    /**
     * Get simplified impact overview
     */
    protected function getSimplifiedImpactOverview(User $user): array
    {
        $currentMonth = Carbon::now();
        $previousMonth = $currentMonth->copy()->subMonth();
        
        $currentEmissions = $this->getUserEmissions($user, $currentMonth);
        $previousEmissions = $this->getUserEmissions($user, $previousMonth);
        
        $improvement = $this->calculateImprovement($currentEmissions, $previousEmissions);
        
        return [
            'current_month_emissions' => round($currentEmissions, 2),
            'previous_month_emissions' => round($previousEmissions, 2),
            'improvement_percentage' => round($improvement, 1),
            'trend' => $improvement > 0 ? 'improving' : 'declining',
            'unit' => 'tCO₂e',
            'status' => $this->getEmissionStatus($improvement),
        ];
    }

    /**
     * Get progress summary for goals and targets
     */
    protected function getProgressSummary(User $user): array
    {
        $targets = EmissionTarget::where('organization_id', $user->organization_id)
            ->where('target_year', Carbon::now()->year)
            ->get();
            
        $overallProgress = $targets->avg('progress_percentage') ?? 0;
        
        return [
            'annual_target_progress' => round($overallProgress, 1),
            'targets_count' => $targets->count(),
            'on_track_count' => $targets->where('status', 'on_track')->count(),
            'at_risk_count' => $targets->where('status', 'at_risk')->count(),
            'status' => $this->getProgressStatus($overallProgress),
        ];
    }

    /**
     * Get pending tasks for the user
     */
    protected function getPendingTasks(User $user): array
    {
        // This would integrate with a task management system
        $tasks = collect([
            [
                'id' => 1,
                'title' => 'Submit Q4 Energy Data',
                'description' => 'Upload electricity and gas consumption data',
                'due_date' => Carbon::now()->addDays(3),
                'priority' => 'high',
                'type' => 'data_entry',
            ],
            [
                'id' => 2,
                'title' => 'Review Emission Calculations',
                'description' => 'Verify last month\'s calculated emissions',
                'due_date' => Carbon::now()->addDays(7),
                'priority' => 'medium',
                'type' => 'review',
            ],
        ]);
        
        return [
            'total_pending' => $tasks->count(),
            'high_priority' => $tasks->where('priority', 'high')->count(),
            'due_this_week' => $tasks->where('due_date', '<=', Carbon::now()->addWeek())->count(),
            'overdue' => $tasks->where('due_date', '<', Carbon::now())->count(),
            'tasks' => $tasks->take(5)->toArray(),
        ];
    }

    /**
     * Get recent activity for the user
     */
    protected function getRecentActivity(User $user, int $limit = 10): Collection
    {
        return ActivityData::where('user_id', $user->id)
            ->with(['ghgProtocolActivity.sector', 'facility', 'calculatedEmissions'])
            ->latest()
            ->limit($limit)
            ->get()
            ->map(function ($activity) {
                return [
                    'id' => $activity->id,
                    'activity_type' => $activity->ghgProtocolActivity?->display_name,
                    'sector' => $activity->ghgProtocolActivity?->sector?->display_name,
                    'quantity' => $activity->quantity,
                    'unit' => $activity->unit?->symbol,
                    'emissions' => $activity->calculatedEmissions?->co2_equivalent,
                    'facility' => $activity->facility?->name,
                    'date' => $activity->activity_date,
                    'created_at' => $activity->created_at,
                ];
            });
    }

    /**
     * Get contextual tips and insights
     */
    protected function getTipsAndInsights(User $user): array
    {
        $tips = [
            [
                'type' => 'efficiency',
                'icon' => '💡',
                'title' => 'Energy Efficiency Tip',
                'message' => 'Switching to LED lighting can reduce energy consumption by up to 75%.',
                'action' => 'Learn more about energy efficiency',
                'url' => '/admin/user-guide#energy-efficiency',
            ],
            [
                'type' => 'data_quality',
                'icon' => '📊',
                'title' => 'Data Quality',
                'message' => 'Regular meter readings improve emission calculation accuracy.',
                'action' => 'Set up data collection reminders',
                'url' => '/admin/my-tasks',
            ],
        ];
        
        return $tips[array_rand($tips)];
    }

    /**
     * Get technical metrics for advanced users
     */
    protected function getTechnicalMetrics(User $user): array
    {
        return [
            'data_quality_score' => rand(85, 95),
            'calculation_accuracy' => rand(90, 98),
            'uncertainty_range' => rand(5, 15),
            'tier_distribution' => [
                'tier_1' => rand(20, 40),
                'tier_2' => rand(40, 60),
                'tier_3' => rand(10, 30),
            ],
            'methodology_compliance' => rand(95, 100),
        ];
    }

    /**
     * Get GHG Protocol breakdown for advanced users
     */
    protected function getGhgProtocolBreakdown(User $user): array
    {
        return [
            'scope_1' => [
                'emissions' => rand(100, 500),
                'percentage' => rand(30, 50),
                'activities_count' => rand(5, 15),
            ],
            'scope_2' => [
                'emissions' => rand(200, 800),
                'percentage' => rand(40, 60),
                'activities_count' => rand(3, 8),
            ],
            'scope_3' => [
                'emissions' => rand(50, 300),
                'percentage' => rand(10, 30),
                'activities_count' => rand(2, 10),
            ],
        ];
    }

    /**
     * Helper methods
     */
    protected function getTimeOfDay(): string
    {
        $hour = Carbon::now()->hour;
        
        if ($hour < 12) return 'morning';
        if ($hour < 17) return 'afternoon';
        return 'evening';
    }

    protected function getLastActivityDate(User $user): ?Carbon
    {
        $lastActivity = ActivityData::where('user_id', $user->id)
            ->latest()
            ->first();
            
        return $lastActivity?->created_at;
    }

    protected function getContextualSubtitle(User $user, ?Carbon $lastActivity): string
    {
        if (!$lastActivity) {
            return "Welcome to your carbon management dashboard. Let's get started!";
        }
        
        $daysSince = $lastActivity->diffInDays(Carbon::now());
        
        if ($daysSince === 0) {
            return "Great to see you back today! Keep up the excellent work.";
        } elseif ($daysSince === 1) {
            return "Welcome back! Ready to continue your sustainability journey?";
        } else {
            return "It's been {$daysSince} days since your last activity. Let's catch up!";
        }
    }

    protected function getUserEmissions(User $user, Carbon $period): float
    {
        return CalculatedEmission::whereHas('activityData', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->whereYear('created_at', $period->year)
            ->whereMonth('created_at', $period->month)
            ->sum('co2_equivalent') ?? rand(10, 100);
    }

    protected function calculateImprovement(float $current, float $previous): float
    {
        if ($previous == 0) return 0;
        return (($previous - $current) / $previous) * 100;
    }

    protected function getEmissionStatus(float $improvement): string
    {
        if ($improvement > 10) return 'excellent';
        if ($improvement > 0) return 'good';
        if ($improvement > -5) return 'stable';
        return 'needs_attention';
    }

    protected function getProgressStatus(float $progress): string
    {
        if ($progress >= 90) return 'excellent';
        if ($progress >= 70) return 'on_track';
        if ($progress >= 50) return 'behind';
        return 'at_risk';
    }
}
