<?php

namespace App\Services;

use App\Models\GhgProtocolEmissionFactor;
use App\Models\EmissionFactorVariant;
use App\Models\GhgProtocolRegion;
use App\Models\GhgProtocolActivity;
use App\Models\GreenhouseGas;
use Carbon\Carbon;

class EmissionFactorLookupService
{
    /**
     * Find the best emission factor for given criteria
     */
    public function findBestFactor(array $criteria): ?GhgProtocolEmissionFactor
    {
        $query = GhgProtocolEmissionFactor::query()->active();

        // Apply required criteria
        if (isset($criteria['sector_id'])) {
            $query->where('sector_id', $criteria['sector_id']);
        }

        if (isset($criteria['activity_id'])) {
            $query->where('activity_id', $criteria['activity_id']);
        }

        if (isset($criteria['gas_id'])) {
            $query->where('gas_id', $criteria['gas_id']);
        }

        // Apply optional criteria with fallbacks
        $this->applyRegionCriteria($query, $criteria);
        $this->applyTemporalCriteria($query, $criteria);
        $this->applyMethodologyCriteria($query, $criteria);

        // Order by priority and tier level
        $query->byPriority();

        return $query->first();
    }

    /**
     * Find the best variant for a given factor and conditions
     */
    public function findBestVariant(GhgProtocolEmissionFactor $factor, array $conditions): ?EmissionFactorVariant
    {
        return $factor->getBestVariant($conditions);
    }

    /**
     * Get all applicable factors for given criteria with ranking
     */
    public function getApplicableFactors(array $criteria): array
    {
        $factors = [];
        $baseQuery = GhgProtocolEmissionFactor::query()->active();

        // Apply required criteria
        if (isset($criteria['sector_id'])) {
            $baseQuery->where('sector_id', $criteria['sector_id']);
        }

        if (isset($criteria['activity_id'])) {
            $baseQuery->where('activity_id', $criteria['activity_id']);
        }

        if (isset($criteria['gas_id'])) {
            $baseQuery->where('gas_id', $criteria['gas_id']);
        }

        // Get factors with different levels of specificity
        $factors = array_merge(
            $this->getExactMatches($baseQuery, $criteria),
            $this->getRegionalFallbacks($baseQuery, $criteria),
            $this->getDefaultFactors($baseQuery, $criteria)
        );

        return $this->rankFactors($factors, $criteria);
    }

    /**
     * Apply region criteria with hierarchical fallback
     */
    protected function applyRegionCriteria($query, array $criteria)
    {
        if (!isset($criteria['region_id'])) {
            return;
        }

        $region = GhgProtocolRegion::find($criteria['region_id']);
        if (!$region) {
            return;
        }

        // Build region hierarchy for fallback
        $regionIds = [$region->id];
        
        // Add parent regions
        $ancestors = $region->getAncestors();
        foreach ($ancestors as $ancestor) {
            $regionIds[] = $ancestor->id;
        }

        $query->where(function ($q) use ($regionIds) {
            $q->whereIn('region_id', $regionIds)
              ->orWhereNull('region_id'); // Global factors as fallback
        });
    }

    /**
     * Apply temporal criteria
     */
    protected function applyTemporalCriteria($query, array $criteria)
    {
        $date = isset($criteria['date']) ? Carbon::parse($criteria['date']) : Carbon::now();

        $query->where(function ($q) use ($date) {
            $q->where(function ($subQ) use ($date) {
                $subQ->where('valid_from', '<=', $date)
                     ->where(function ($validQ) use ($date) {
                         $validQ->where('valid_until', '>=', $date)
                                ->orWhereNull('valid_until');
                     });
            })->orWhere(function ($subQ) {
                $subQ->whereNull('valid_from')
                     ->whereNull('valid_until');
            });
        });

        // Prefer factors with vintage year close to the target year
        if (isset($criteria['vintage_year'])) {
            $query->orderByRaw('ABS(vintage_year - ?) ASC', [$criteria['vintage_year']]);
        }
    }

    /**
     * Apply methodology criteria
     */
    protected function applyMethodologyCriteria($query, array $criteria)
    {
        if (isset($criteria['calculation_method'])) {
            $query->where('calculation_method', $criteria['calculation_method']);
        }

        if (isset($criteria['tier_level'])) {
            $query->where('tier_level', $criteria['tier_level']);
        }
    }

    /**
     * Get exact matches for all criteria
     */
    protected function getExactMatches($baseQuery, array $criteria): array
    {
        $query = clone $baseQuery;
        
        if (isset($criteria['region_id'])) {
            $query->where('region_id', $criteria['region_id']);
        }

        return $query->get()->map(function ($factor) {
            $factor->match_score = 100; // Highest score for exact matches
            return $factor;
        })->toArray();
    }

    /**
     * Get regional fallbacks
     */
    protected function getRegionalFallbacks($baseQuery, array $criteria): array
    {
        if (!isset($criteria['region_id'])) {
            return [];
        }

        $region = GhgProtocolRegion::find($criteria['region_id']);
        if (!$region) {
            return [];
        }

        $factors = [];
        $score = 80;

        // Try parent regions
        foreach ($region->getAncestors() as $ancestor) {
            $query = clone $baseQuery;
            $query->where('region_id', $ancestor->id);
            
            $ancestorFactors = $query->get()->map(function ($factor) use ($score) {
                $factor->match_score = $score;
                return $factor;
            });

            $factors = array_merge($factors, $ancestorFactors->toArray());
            $score -= 10; // Decrease score for each level up
        }

        return $factors;
    }

    /**
     * Get default/global factors
     */
    protected function getDefaultFactors($baseQuery, array $criteria): array
    {
        $query = clone $baseQuery;
        $query->where(function ($q) {
            $q->where('is_default', true)
              ->orWhereNull('region_id');
        });

        return $query->get()->map(function ($factor) {
            $factor->match_score = 50; // Lower score for defaults
            return $factor;
        })->toArray();
    }

    /**
     * Rank factors by match quality
     */
    protected function rankFactors(array $factors, array $criteria): array
    {
        // Remove duplicates
        $uniqueFactors = [];
        foreach ($factors as $factor) {
            $key = $factor['id'];
            if (!isset($uniqueFactors[$key]) || $uniqueFactors[$key]['match_score'] < $factor['match_score']) {
                $uniqueFactors[$key] = $factor;
            }
        }

        // Sort by match score and priority
        usort($uniqueFactors, function ($a, $b) {
            if ($a['match_score'] === $b['match_score']) {
                return $b['priority'] <=> $a['priority'];
            }
            return $b['match_score'] <=> $a['match_score'];
        });

        return array_values($uniqueFactors);
    }

    /**
     * Get factor selection explanation
     */
    public function getSelectionExplanation(GhgProtocolEmissionFactor $factor, array $criteria): string
    {
        $explanations = [];

        if ($factor->region) {
            $explanations[] = "Regional factor for {$factor->region->name}";
        } else {
            $explanations[] = "Global/default factor";
        }

        if ($factor->tier_level) {
            $explanations[] = "IPCC Tier {$factor->tier_level}";
        }

        if ($factor->calculation_method) {
            $explanations[] = "Method: {$factor->calculation_method}";
        }

        if ($factor->vintage_year) {
            $explanations[] = "Data vintage: {$factor->vintage_year}";
        }

        return implode(', ', $explanations);
    }
}
