<?php

namespace App\Services;

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class GhgProtocolExcelImportService
{
    protected GhgProtocolDataValidator $validator;
    protected ElectricityFactorsImportService $electricityImporter;
    
    protected array $importStats = [
        'files_processed' => 0,
        'sheets_processed' => 0,
        'tables_found' => 0,
        'rows_processed' => 0,
        'errors' => [],
        'warnings' => []
    ];

    public function __construct()
    {
        $this->validator = new GhgProtocolDataValidator();
        $this->electricityImporter = new ElectricityFactorsImportService();
    }

    /**
     * Import from Excel file
     */
    public function importFromExcel(string $filePath, array $options = []): array
    {
        try {
            if (!file_exists($filePath)) {
                throw new \Exception("File not found: {$filePath}");
            }

            $spreadsheet = IOFactory::load($filePath);
            $this->importStats['files_processed']++;

            // Determine sector from filename
            $sector = $this->determineSectorFromFilename($filePath);
            
            if ($sector === 'electricity') {
                return $this->importElectricityFromSpreadsheet($spreadsheet, $options);
            } else {
                return $this->importGenericFromSpreadsheet($spreadsheet, $sector, $options);
            }

        } catch (\Exception $e) {
            Log::error('Excel import failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'stats' => $this->importStats
            ];
        }
    }

    /**
     * Import electricity data from spreadsheet
     */
    protected function importElectricityFromSpreadsheet(Spreadsheet $spreadsheet, array $options): array
    {
        $electricityData = [];
        
        foreach ($spreadsheet->getWorksheetIterator() as $worksheet) {
            $this->importStats['sheets_processed']++;
            
            $sheetName = $worksheet->getTitle();
            Log::info("Processing sheet: {$sheetName}");

            // Skip table of contents and summary sheets
            if ($this->shouldSkipSheet($sheetName)) {
                continue;
            }

            $tables = $this->extractTablesFromWorksheet($worksheet);
            
            foreach ($tables as $table) {
                if ($this->isElectricityTable($table)) {
                    $electricityData[] = $this->parseElectricityTable($table, $sheetName);
                    $this->importStats['tables_found']++;
                }
            }
        }

        // Validate the extracted data
        $validation = $this->validator->validateElectricityData($electricityData);
        
        if (!$validation['is_valid']) {
            return [
                'success' => false,
                'error' => 'Data validation failed',
                'validation' => $validation,
                'stats' => $this->importStats
            ];
        }

        // Import the validated data
        $importResult = $this->electricityImporter->importElectricityFactors($electricityData);
        
        // Merge statistics
        $this->importStats = array_merge($this->importStats, $importResult['stats'] ?? []);
        
        return [
            'success' => $importResult['success'],
            'validation' => $validation,
            'stats' => $this->importStats,
            'data_preview' => array_slice($electricityData, 0, 3) // First 3 tables for preview
        ];
    }

    /**
     * Extract tables from worksheet
     */
    protected function extractTablesFromWorksheet(Worksheet $worksheet): array
    {
        $tables = [];
        $highestRow = $worksheet->getHighestRow();
        $highestColumn = $worksheet->getHighestColumn();
        
        $currentTable = null;
        $tableStartRow = null;
        
        for ($row = 1; $row <= $highestRow; $row++) {
            $rowData = [];
            
            // Read the entire row
            for ($col = 'A'; $col <= $highestColumn; $col++) {
                $cellValue = $worksheet->getCell($col . $row)->getCalculatedValue();
                if (!empty($cellValue)) {
                    $rowData[$col] = $cellValue;
                }
            }
            
            // Check if this row starts a new table
            if ($this->isTableHeaderRow($rowData)) {
                // Save previous table if exists
                if ($currentTable !== null) {
                    $tables[] = $currentTable;
                }
                
                // Start new table
                $currentTable = [
                    'title' => $this->extractTableTitle($worksheet, $row),
                    'headers' => $rowData,
                    'data' => [],
                    'start_row' => $row,
                    'notes' => []
                ];
                $tableStartRow = $row;
                
            } elseif ($currentTable !== null && $this->isDataRow($rowData)) {
                // Add data row to current table
                $currentTable['data'][] = $rowData;
                $this->importStats['rows_processed']++;
                
            } elseif ($currentTable !== null && $this->isNotesRow($rowData)) {
                // Add notes to current table
                $currentTable['notes'][] = implode(' ', $rowData);
            }
        }
        
        // Add the last table
        if ($currentTable !== null) {
            $tables[] = $currentTable;
        }
        
        return $tables;
    }

    /**
     * Parse electricity table into standardized format
     */
    protected function parseElectricityTable(array $table, string $sheetName): array
    {
        $region = $this->extractRegionFromTable($table);
        $tableName = $table['title'] ?? "Table from {$sheetName}";
        $notes = implode(' ', $table['notes'] ?? []);
        
        $data = [];
        
        foreach ($table['data'] as $rowData) {
            $parsedRow = $this->parseElectricityDataRow($rowData, $table['headers']);
            if ($parsedRow) {
                $data[] = $parsedRow;
            }
        }
        
        return [
            'region' => $region,
            'table_name' => $tableName,
            'notes' => $notes,
            'data' => $data
        ];
    }

    /**
     * Parse a single electricity data row
     */
    protected function parseElectricityDataRow(array $rowData, array $headers): ?array
    {
        $year = null;
        $factors = [];
        $unit = null;
        
        // Map headers to data
        $headerMap = $this->createHeaderMap($headers);
        
        foreach ($rowData as $col => $value) {
            $headerKey = $headerMap[$col] ?? null;
            
            if (!$headerKey) continue;
            
            if (stripos($headerKey, 'year') !== false) {
                $year = (int) $value;
            } elseif (stripos($headerKey, 'emission') !== false || stripos($headerKey, 'factor') !== false) {
                if (stripos($headerKey, 'co2') !== false || stripos($headerKey, 'co₂') !== false) {
                    $factors['co2_factor'] = (float) $value;
                } elseif (stripos($headerKey, 'ch4') !== false || stripos($headerKey, 'ch₄') !== false) {
                    $factors['ch4_factor'] = (float) $value;
                } elseif (stripos($headerKey, 'n2o') !== false || stripos($headerKey, 'n₂o') !== false) {
                    $factors['n2o_factor'] = (float) $value;
                } else {
                    $factors['emission_factor'] = (float) $value;
                }
            } elseif (stripos($headerKey, 'unit') !== false || preg_match('/\(.*\)/', $headerKey)) {
                $unit = $this->extractUnitFromHeader($headerKey);
            }
        }
        
        // Try to extract unit from header if not found in data
        if (!$unit) {
            $unit = $this->extractUnitFromHeaders($headers);
        }
        
        if ($year && !empty($factors) && $unit) {
            return array_merge(['year' => $year, 'unit' => $unit], $factors);
        }
        
        return null;
    }

    /**
     * Helper methods for data extraction
     */
    protected function determineSectorFromFilename(string $filePath): string
    {
        $filename = basename($filePath);
        
        if (stripos($filename, 'electricity') !== false || stripos($filename, 'elec') !== false) {
            return 'electricity';
        } elseif (stripos($filename, 'transport') !== false) {
            return 'transport';
        } elseif (stripos($filename, 'stationary') !== false) {
            return 'stationary_combustion';
        } elseif (stripos($filename, 'mobile') !== false) {
            return 'mobile_combustion';
        }
        
        return 'unknown';
    }

    protected function shouldSkipSheet(string $sheetName): bool
    {
        $skipPatterns = ['toc', 'contents', 'summary', 'index', 'cover'];
        
        foreach ($skipPatterns as $pattern) {
            if (stripos($sheetName, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }

    protected function isTableHeaderRow(array $rowData): bool
    {
        if (empty($rowData)) return false;
        
        $firstCell = reset($rowData);
        
        // Look for table indicators
        return stripos($firstCell, 'table') !== false || 
               stripos($firstCell, 'year') !== false ||
               (count($rowData) >= 2 && stripos($firstCell, 'emission') !== false);
    }

    protected function isDataRow(array $rowData): bool
    {
        if (empty($rowData)) return false;
        
        $firstCell = reset($rowData);
        
        // Check if first cell looks like a year
        return is_numeric($firstCell) && $firstCell >= 1990 && $firstCell <= 2030;
    }

    protected function isNotesRow(array $rowData): bool
    {
        if (empty($rowData)) return false;
        
        $firstCell = reset($rowData);
        
        return stripos($firstCell, 'note') !== false || 
               stripos($firstCell, 'source') !== false ||
               strlen($firstCell) > 50; // Long text likely to be notes
    }

    protected function isElectricityTable(array $table): bool
    {
        $title = strtolower($table['title'] ?? '');
        $headers = strtolower(implode(' ', $table['headers'] ?? []));
        
        return stripos($title, 'electricity') !== false || 
               stripos($headers, 'electricity') !== false ||
               stripos($headers, 'kwh') !== false ||
               stripos($headers, 'mwh') !== false;
    }

    protected function extractRegionFromTable(array $table): string
    {
        $title = $table['title'] ?? '';
        
        // Common region patterns
        $regions = ['China', 'Taiwan', 'Brazil', 'Thailand', 'U.K.', 'United Kingdom', 'USA', 'Canada'];
        
        foreach ($regions as $region) {
            if (stripos($title, $region) !== false) {
                return $region;
            }
        }
        
        // Extract from "for [Region]" pattern
        if (preg_match('/for\s+([A-Za-z\s\.]+)/i', $title, $matches)) {
            return trim($matches[1]);
        }
        
        return 'Unknown Region';
    }

    protected function extractTableTitle(Worksheet $worksheet, int $headerRow): string
    {
        // Look for title in rows above the header
        for ($row = max(1, $headerRow - 5); $row < $headerRow; $row++) {
            $cellValue = $worksheet->getCell('A' . $row)->getCalculatedValue();
            if (!empty($cellValue) && stripos($cellValue, 'table') !== false) {
                return $cellValue;
            }
        }
        
        return "Table starting at row {$headerRow}";
    }

    protected function createHeaderMap(array $headers): array
    {
        $map = [];
        foreach ($headers as $col => $header) {
            $map[$col] = strtolower(trim($header));
        }
        return $map;
    }

    protected function extractUnitFromHeader(string $header): ?string
    {
        if (preg_match('/\(([^)]+)\)/', $header, $matches)) {
            return $matches[1];
        }
        return null;
    }

    protected function extractUnitFromHeaders(array $headers): ?string
    {
        foreach ($headers as $header) {
            $unit = $this->extractUnitFromHeader($header);
            if ($unit) return $unit;
        }
        return null;
    }

    /**
     * Import generic sector data (placeholder for future sectors)
     */
    protected function importGenericFromSpreadsheet(Spreadsheet $spreadsheet, string $sector, array $options): array
    {
        // Placeholder for other sectors
        return [
            'success' => false,
            'error' => "Import for sector '{$sector}' not yet implemented",
            'stats' => $this->importStats
        ];
    }

    /**
     * Get import statistics
     */
    public function getImportStats(): array
    {
        return $this->importStats;
    }
}
