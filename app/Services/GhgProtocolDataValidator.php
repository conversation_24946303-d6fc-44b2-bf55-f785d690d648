<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class GhgProtocolDataValidator
{
    protected array $validationErrors = [];
    protected array $validationWarnings = [];

    /**
     * Validate electricity factors data structure
     */
    public function validateElectricityData(array $electricityData): array
    {
        $this->validationErrors = [];
        $this->validationWarnings = [];

        foreach ($electricityData as $index => $tableData) {
            $this->validateElectricityTable($tableData, $index);
        }

        return [
            'is_valid' => empty($this->validationErrors),
            'errors' => $this->validationErrors,
            'warnings' => $this->validationWarnings,
        ];
    }

    /**
     * Validate a single electricity table
     */
    protected function validateElectricityTable(array $tableData, int $index): void
    {
        $tablePrefix = "Table {$index}";

        // Required fields
        if (!isset($tableData['region']) || empty($tableData['region'])) {
            $this->validationErrors[] = "{$tablePrefix}: Missing or empty 'region' field";
        }

        if (!isset($tableData['table_name']) || empty($tableData['table_name'])) {
            $this->validationErrors[] = "{$tablePrefix}: Missing or empty 'table_name' field";
        }

        if (!isset($tableData['data']) || !is_array($tableData['data'])) {
            $this->validationErrors[] = "{$tablePrefix}: Missing or invalid 'data' field";
            return;
        }

        if (empty($tableData['data'])) {
            $this->validationWarnings[] = "{$tablePrefix}: No data rows found";
            return;
        }

        // Validate each data row
        foreach ($tableData['data'] as $rowIndex => $rowData) {
            $this->validateElectricityRow($rowData, $tablePrefix, $rowIndex);
        }
    }

    /**
     * Validate a single electricity data row
     */
    protected function validateElectricityRow(array $rowData, string $tablePrefix, int $rowIndex): void
    {
        $rowPrefix = "{$tablePrefix}, Row {$rowIndex}";

        // Year validation
        if (!isset($rowData['year']) || !is_numeric($rowData['year'])) {
            $this->validationErrors[] = "{$rowPrefix}: Missing or invalid 'year' field";
        } else {
            $year = (int) $rowData['year'];
            if ($year < 1990 || $year > 2030) {
                $this->validationWarnings[] = "{$rowPrefix}: Year {$year} is outside typical range (1990-2030)";
            }
        }

        // Factor value validation
        $hasValidFactor = false;
        $factorFields = ['emission_factor', 'co2_factor', 'ch4_factor', 'n2o_factor'];

        foreach ($factorFields as $field) {
            if (isset($rowData[$field])) {
                if (!is_numeric($rowData[$field])) {
                    $this->validationErrors[] = "{$rowPrefix}: Invalid {$field} value (not numeric)";
                } else {
                    $value = (float) $rowData[$field];
                    if ($value < 0) {
                        $this->validationErrors[] = "{$rowPrefix}: Negative {$field} value ({$value})";
                    } elseif ($value > 10) { // Reasonable upper bound for most electricity factors
                        $this->validationWarnings[] = "{$rowPrefix}: Unusually high {$field} value ({$value})";
                    }
                    $hasValidFactor = true;
                }
            }
        }

        if (!$hasValidFactor) {
            $this->validationErrors[] = "{$rowPrefix}: No valid emission factor found";
        }

        // Unit validation
        if (!isset($rowData['unit']) || empty($rowData['unit'])) {
            $this->validationErrors[] = "{$rowPrefix}: Missing 'unit' field";
        } else {
            $this->validateUnit($rowData['unit'], $rowPrefix);
        }
    }

    /**
     * Validate unit format
     */
    protected function validateUnit(string $unit, string $rowPrefix): void
    {
        $validUnits = [
            'tCO₂/MWh', 'tCO2/MWh',
            'kgCO₂e/kWh', 'kgCO2e/kWh',
            'CO₂ (kg/kWh)', 'CO2 (kg/kWh)',
            'kg/kWh'
        ];

        // Normalize the unit for comparison (convert subscripts to regular numbers)
        $normalizedUnit = str_replace(['₂', '₄', '₂O'], ['2', '4', '2O'], $unit);

        $unitFound = false;
        foreach ($validUnits as $validUnit) {
            $normalizedValidUnit = str_replace(['₂', '₄', '₂O'], ['2', '4', '2O'], $validUnit);

            if (stripos($normalizedUnit, $normalizedValidUnit) !== false ||
                stripos($normalizedValidUnit, $normalizedUnit) !== false) {
                $unitFound = true;
                break;
            }
        }

        if (!$unitFound) {
            $this->validationWarnings[] = "{$rowPrefix}: Unrecognized unit format '{$unit}'";
        }
    }

    /**
     * Validate region name
     */
    public function validateRegionName(string $regionName): array
    {
        $knownRegions = [
            'China', 'Taiwan', 'Brazil', 'Thailand', 'U.K.', 'United Kingdom',
            'United States', 'USA', 'Canada', 'Australia', 'India', 'Japan',
            'Germany', 'France', 'Italy', 'Spain', 'Netherlands', 'Sweden',
            'Norway', 'Denmark', 'Finland'
        ];

        $isKnown = in_array($regionName, $knownRegions);

        return [
            'is_known' => $isKnown,
            'suggestions' => $isKnown ? [] : $this->suggestSimilarRegions($regionName, $knownRegions)
        ];
    }

    /**
     * Suggest similar region names
     */
    protected function suggestSimilarRegions(string $regionName, array $knownRegions): array
    {
        $suggestions = [];
        $regionLower = strtolower($regionName);

        foreach ($knownRegions as $knownRegion) {
            $knownLower = strtolower($knownRegion);

            // Check for partial matches
            if (strpos($knownLower, $regionLower) !== false || strpos($regionLower, $knownLower) !== false) {
                $suggestions[] = $knownRegion;
            }

            // Check for similar length and characters
            if (abs(strlen($regionLower) - strlen($knownLower)) <= 2) {
                $similarity = similar_text($regionLower, $knownLower);
                if ($similarity >= strlen($regionLower) * 0.7) {
                    $suggestions[] = $knownRegion;
                }
            }
        }

        return array_unique($suggestions);
    }

    /**
     * Validate data consistency across years
     */
    public function validateDataConsistency(array $electricityData): array
    {
        $consistencyIssues = [];

        foreach ($electricityData as $tableData) {
            if (!isset($tableData['data']) || !is_array($tableData['data'])) {
                continue;
            }

            $region = $tableData['region'] ?? 'Unknown';
            $values = [];

            // Collect all factor values by year
            foreach ($tableData['data'] as $rowData) {
                if (isset($rowData['year']) && isset($rowData['emission_factor'])) {
                    $values[$rowData['year']] = $rowData['emission_factor'];
                } elseif (isset($rowData['year']) && isset($rowData['co2_factor'])) {
                    $values[$rowData['year']] = $rowData['co2_factor'];
                }
            }

            // Check for unusual year-over-year changes
            $years = array_keys($values);
            sort($years);

            for ($i = 1; $i < count($years); $i++) {
                $prevYear = $years[$i - 1];
                $currentYear = $years[$i];
                $prevValue = $values[$prevYear];
                $currentValue = $values[$currentYear];

                if ($prevValue > 0) {
                    $changePercent = abs(($currentValue - $prevValue) / $prevValue) * 100;

                    if ($changePercent > 50) {
                        $consistencyIssues[] = [
                            'region' => $region,
                            'issue' => 'Large year-over-year change',
                            'details' => "From {$prevYear} to {$currentYear}: {$prevValue} → {$currentValue} (" . number_format($changePercent, 1) . "% change)"
                        ];
                    }
                }
            }
        }

        return $consistencyIssues;
    }

    /**
     * Get validation summary
     */
    public function getValidationSummary(): array
    {
        return [
            'total_errors' => count($this->validationErrors),
            'total_warnings' => count($this->validationWarnings),
            'errors' => $this->validationErrors,
            'warnings' => $this->validationWarnings,
        ];
    }

    /**
     * Check if data passes validation
     */
    public function isValid(): bool
    {
        return empty($this->validationErrors);
    }

    /**
     * Log validation results
     */
    public function logValidationResults(string $context = 'Data Validation'): void
    {
        if (!empty($this->validationErrors)) {
            Log::error("{$context} - Validation Errors:", $this->validationErrors);
        }

        if (!empty($this->validationWarnings)) {
            Log::warning("{$context} - Validation Warnings:", $this->validationWarnings);
        }

        if (empty($this->validationErrors) && empty($this->validationWarnings)) {
            Log::info("{$context} - All validations passed");
        }
    }
}
