<?php

namespace App\Services;

use App\Models\User;
use App\Models\ActivityData;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolSector;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class UserContextService
{
    /**
     * Get user's interface preference based on experience and settings
     */
    public function getUserInterfaceType(User $user): string
    {
        if ($user->preferred_interface === 'auto') {
            return $user->getDefaultDashboardType();
        }
        
        return $user->preferred_interface;
    }

    /**
     * Get user's organizational context for data entry
     */
    public function getOrganizationalContext(User $user): array
    {
        return [
            'organization_id' => $user->organization_id,
            'organizational_unit_id' => $user->organizational_unit_id,
            'facility_id' => $user->facility_id,
            'department' => $user->department,
            'default_scope' => $user->default_scope,
            'user_type' => $user->user_type,
            'experience_level' => $user->experience_level,
        ];
    }

    /**
     * Get user's frequently used activities for quick access
     */
    public function getFrequentActivities(User $user, int $limit = 5): Collection
    {
        $cacheKey = "user_frequent_activities_{$user->id}";
        
        return Cache::remember($cacheKey, 3600, function () use ($user, $limit) {
            return ActivityData::where('user_id', $user->id)
                ->with(['ghgProtocolActivity.sector'])
                ->selectRaw('ghg_protocol_activity_id, COUNT(*) as usage_count')
                ->groupBy('ghg_protocol_activity_id')
                ->orderByDesc('usage_count')
                ->limit($limit)
                ->get()
                ->map(function ($item) {
                    return [
                        'activity' => $item->ghgProtocolActivity,
                        'usage_count' => $item->usage_count,
                        'sector' => $item->ghgProtocolActivity?->sector,
                    ];
                });
        });
    }

    /**
     * Get recommended activities based on user's role and facility type
     */
    public function getRecommendedActivities(User $user): Collection
    {
        $recommendations = collect();
        
        // Role-based recommendations
        switch ($user->user_type) {
            case 'facility_operator':
                $recommendations = $this->getFacilityOperatorActivities($user);
                break;
            case 'sustainability_manager':
                $recommendations = $this->getSustainabilityManagerActivities($user);
                break;
            case 'department_manager':
                $recommendations = $this->getDepartmentManagerActivities($user);
                break;
            default:
                $recommendations = $this->getGeneralActivities();
        }
        
        return $recommendations;
    }

    /**
     * Get smart defaults for data entry based on user context
     */
    public function getSmartDefaults(User $user, ?string $activityType = null): array
    {
        $defaults = [
            'organization_id' => $user->organization_id,
            'facility_id' => $user->facility_id,
            'user_id' => $user->id,
            'activity_date' => now()->format('Y-m-d'),
        ];

        // Add activity-specific defaults
        if ($activityType) {
            $defaults = array_merge($defaults, $this->getActivityDefaults($user, $activityType));
        }

        return $defaults;
    }

    /**
     * Check if user should see simplified or advanced interface elements
     */
    public function shouldShowAdvancedFeatures(User $user): bool
    {
        return $user->isExpertUser() || 
               $user->experience_level === 'expert' ||
               $user->preferred_interface === 'advanced';
    }

    /**
     * Get user's dashboard configuration
     */
    public function getDashboardConfig(User $user): array
    {
        $interfaceType = $this->getUserInterfaceType($user);
        
        return [
            'interface_type' => $interfaceType,
            'show_advanced_features' => $this->shouldShowAdvancedFeatures($user),
            'default_widgets' => $this->getDefaultWidgets($interfaceType),
            'navigation_items' => $this->getNavigationItems($user),
            'quick_actions' => $this->getQuickActions($user),
        ];
    }

    /**
     * Get facility operator specific activities
     */
    protected function getFacilityOperatorActivities(User $user): Collection
    {
        return GhgProtocolActivity::whereHas('sector', function ($query) {
            $query->whereIn('name', [
                'Stationary Combustion',
                'Mobile Combustion',
                'Electricity',
                'Waste',
            ]);
        })
        ->where('is_active', true)
        ->orderBy('sort_order')
        ->limit(8)
        ->get();
    }

    /**
     * Get sustainability manager specific activities
     */
    protected function getSustainabilityManagerActivities(User $user): Collection
    {
        return GhgProtocolActivity::with('sector')
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get department manager specific activities
     */
    protected function getDepartmentManagerActivities(User $user): Collection
    {
        return GhgProtocolActivity::whereHas('sector', function ($query) {
            $query->whereIn('name', [
                'Business Travel',
                'Employee Commuting',
                'Electricity',
                'Waste',
            ]);
        })
        ->where('is_active', true)
        ->orderBy('sort_order')
        ->limit(6)
        ->get();
    }

    /**
     * Get general activities for all users
     */
    protected function getGeneralActivities(): Collection
    {
        return GhgProtocolActivity::whereHas('sector', function ($query) {
            $query->whereIn('name', [
                'Stationary Combustion',
                'Electricity',
                'Business Travel',
            ]);
        })
        ->where('is_active', true)
        ->orderBy('sort_order')
        ->limit(5)
        ->get();
    }

    /**
     * Get activity-specific defaults
     */
    protected function getActivityDefaults(User $user, string $activityType): array
    {
        $defaults = [];
        
        // Add common defaults based on user's facility and historical data
        $recentData = ActivityData::where('user_id', $user->id)
            ->whereHas('ghgProtocolActivity', function ($query) use ($activityType) {
                $query->where('activity_type', $activityType);
            })
            ->latest()
            ->first();
            
        if ($recentData) {
            $defaults['ghg_protocol_activity_id'] = $recentData->ghg_protocol_activity_id;
            $defaults['emission_factor_id'] = $recentData->emission_factor_id;
        }
        
        return $defaults;
    }

    /**
     * Get default widgets based on interface type
     */
    protected function getDefaultWidgets(string $interfaceType): array
    {
        switch ($interfaceType) {
            case 'executive':
                return [
                    'ExecutiveOverviewWidget',
                    'ComplianceStatusWidget',
                    'TargetProgressWidget',
                    'TrendAnalysisWidget',
                ];
            case 'advanced':
                return [
                    'UserWelcomeWidget',
                    'UserImpactOverviewWidget',
                    'UserProgressWidget',
                    'UserTasksWidget',
                    'UserQuickActionsWidget',
                    'TechnicalMetricsWidget',
                ];
            default: // simplified
                return [
                    'UserWelcomeWidget',
                    'UserImpactOverviewWidget',
                    'UserProgressWidget',
                    'UserTasksWidget',
                    'UserQuickActionsWidget',
                ];
        }
    }

    /**
     * Get navigation items based on user permissions and type
     */
    protected function getNavigationItems(User $user): array
    {
        $items = [
            'dashboard' => true,
            'add_data' => true,
            'my_progress' => true,
            'my_tasks' => true,
            'reports' => true,
        ];

        if ($this->shouldShowAdvancedFeatures($user)) {
            $items = array_merge($items, [
                'emission_factors' => true,
                'ghg_protocol' => true,
                'advanced_calculations' => true,
            ]);
        }

        if ($user->isExecutive()) {
            $items = array_merge($items, [
                'executive_dashboard' => true,
                'compliance_reports' => true,
                'strategic_planning' => true,
            ]);
        }

        return $items;
    }

    /**
     * Get quick actions based on user type and context
     */
    protected function getQuickActions(User $user): array
    {
        $actions = [
            [
                'title' => 'Add Emissions Data',
                'description' => 'Record your carbon-generating activities',
                'icon' => '📝',
                'url' => '/admin/add-data-page',
                'color' => 'blue',
                'priority' => 1,
            ],
        ];

        if ($user->isExpertUser()) {
            $actions[] = [
                'title' => 'Advanced Calculator',
                'description' => 'Use GHG Protocol methodologies',
                'icon' => '🧮',
                'url' => '/admin/advanced-calculator',
                'color' => 'purple',
                'priority' => 2,
            ];
        }

        if ($user->isExecutive()) {
            $actions[] = [
                'title' => 'Executive Summary',
                'description' => 'View organizational performance',
                'icon' => '📊',
                'url' => '/admin/executive-dashboard',
                'color' => 'green',
                'priority' => 1,
            ];
        }

        return collect($actions)->sortBy('priority')->values()->all();
    }
}
