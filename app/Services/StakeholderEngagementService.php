<?php

namespace App\Services;

use App\Models\User;
use App\Models\Organization;
use App\Models\Supplier;
use App\Models\StakeholderAction;
use App\Models\SupplierPortalAccess;
use App\Models\StakeholderDashboard;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class StakeholderEngagementService
{
    protected array $stakeholderRoles = [
        'finance_team' => 'Finance Team Member',
        'sustainability_lead' => 'Sustainability Lead',
        'executive' => 'Executive',
        'facility_manager' => 'Facility Manager',
        'supplier_admin' => 'Supplier Administrator',
        'supplier_user' => 'Supplier User',
        'auditor' => 'External Auditor',
        'consultant' => 'Sustainability Consultant',
    ];

    protected array $rolePermissions = [
        'finance_team' => [
            'view_cost_data',
            'view_financial_metrics',
            'view_investment_analysis',
            'view_roi_calculations',
            'manage_carbon_pricing',
            'view_budget_allocations',
        ],
        'sustainability_lead' => [
            'manage_targets',
            'view_all_emissions',
            'manage_initiatives',
            'view_progress_tracking',
            'manage_suppliers',
            'view_compliance_reports',
            'assign_actions',
            'approve_initiatives',
        ],
        'executive' => [
            'view_executive_dashboard',
            'view_kpi_summary',
            'view_strategic_metrics',
            'approve_targets',
            'view_risk_assessment',
            'view_board_reports',
        ],
        'facility_manager' => [
            'manage_facility_data',
            'view_facility_emissions',
            'manage_facility_actions',
            'view_facility_targets',
            'submit_activity_data',
        ],
        'supplier_admin' => [
            'manage_supplier_data',
            'submit_emissions_data',
            'view_supplier_dashboard',
            'manage_supplier_users',
            'view_engagement_metrics',
        ],
        'supplier_user' => [
            'submit_supplier_data',
            'view_supplier_progress',
            'view_assigned_actions',
            'update_supplier_profile',
        ],
        'auditor' => [
            'view_audit_trails',
            'access_verification_data',
            'view_calculation_details',
            'generate_audit_reports',
        ],
        'consultant' => [
            'view_analysis_data',
            'generate_recommendations',
            'view_benchmarking_data',
            'access_methodology_details',
        ],
    ];

    /**
     * Initialize comprehensive stakeholder engagement system
     */
    public function initializeStakeholderSystem(): array
    {
        try {
            Log::info("Initializing stakeholder engagement system");

            // Create enhanced permissions
            $permissions = $this->createEnhancedPermissions();

            // Create stakeholder roles
            $roles = $this->createStakeholderRoles();

            // Assign permissions to roles
            $this->assignPermissionsToRoles($roles, $permissions);

            // Create default dashboards
            $dashboards = $this->createDefaultDashboards();

            return [
                'success' => true,
                'permissions_created' => count($permissions),
                'roles_created' => count($roles),
                'dashboards_created' => count($dashboards),
                'message' => 'Stakeholder engagement system initialized successfully',
            ];

        } catch (\Exception $e) {
            Log::error("Stakeholder system initialization failed", [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create supplier portal access
     */
    public function createSupplierPortalAccess(Supplier $supplier, array $options = []): array
    {
        try {
            // Generate secure access credentials
            $accessToken = Str::random(32);
            $portalPassword = $options['password'] ?? Str::random(12);

            // Create supplier user account
            $supplierUser = User::create([
                'name' => $supplier->name . ' Portal User',
                'email' => $supplier->contact_email,
                'password' => Hash::make($portalPassword),
                'email_verified_at' => now(),
            ]);

            // Assign supplier role
            $supplierRole = Role::findByName('supplier_admin');
            $supplierUser->assignRole($supplierRole);

            // Create portal access record
            $portalAccess = SupplierPortalAccess::create([
                'supplier_id' => $supplier->id,
                'user_id' => $supplierUser->id,
                'access_token' => $accessToken,
                'portal_url' => $this->generatePortalUrl($supplier),
                'access_level' => $options['access_level'] ?? 'standard',
                'data_submission_enabled' => $options['data_submission_enabled'] ?? true,
                'dashboard_access_enabled' => $options['dashboard_access_enabled'] ?? true,
                'expires_at' => $options['expires_at'] ?? now()->addYear(),
                'is_active' => true,
            ]);

            // Create supplier dashboard
            $dashboard = $this->createSupplierDashboard($supplier, $supplierUser);

            // Send welcome email with credentials
            $this->sendSupplierWelcomeEmail($supplier, $supplierUser, $portalPassword, $portalAccess);

            return [
                'success' => true,
                'supplier_user' => $supplierUser,
                'portal_access' => $portalAccess,
                'dashboard' => $dashboard,
                'portal_url' => $portalAccess->portal_url,
                'credentials' => [
                    'email' => $supplierUser->email,
                    'password' => $portalPassword,
                    'access_token' => $accessToken,
                ],
            ];

        } catch (\Exception $e) {
            Log::error("Supplier portal access creation failed", [
                'supplier_id' => $supplier->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create stakeholder action with assignment and tracking
     */
    public function createStakeholderAction(array $actionData): array
    {
        try {
            $action = StakeholderAction::create([
                'organization_id' => $actionData['organization_id'],
                'title' => $actionData['title'],
                'description' => $actionData['description'],
                'action_type' => $actionData['action_type'],
                'category' => $actionData['category'],
                'assigned_to_user_id' => $actionData['assigned_to_user_id'] ?? null,
                'assigned_to_supplier_id' => $actionData['assigned_to_supplier_id'] ?? null,
                'assigned_to_facility_id' => $actionData['assigned_to_facility_id'] ?? null,
                'priority' => $actionData['priority'] ?? 'medium',
                'due_date' => $actionData['due_date'],
                'estimated_emissions_reduction' => $actionData['estimated_emissions_reduction'] ?? null,
                'estimated_cost' => $actionData['estimated_cost'] ?? null,
                'estimated_savings' => $actionData['estimated_savings'] ?? null,
                'success_criteria' => $actionData['success_criteria'] ?? null,
                'approval_required' => $actionData['approval_required'] ?? false,
                'approver_user_id' => $actionData['approver_user_id'] ?? null,
                'status' => 'assigned',
                'created_by_user_id' => auth()->id(),
            ]);

            // Create initial progress entry
            $action->progressUpdates()->create([
                'update_date' => now(),
                'progress_percentage' => 0,
                'status' => 'assigned',
                'notes' => 'Action assigned',
                'updated_by_user_id' => auth()->id(),
            ]);

            // Send notification to assignee
            $this->sendActionAssignmentNotification($action);

            // Create calendar reminder if due date is set
            if ($action->due_date) {
                $this->createActionReminder($action);
            }

            return [
                'success' => true,
                'action' => $action,
                'message' => 'Stakeholder action created and assigned successfully',
            ];

        } catch (\Exception $e) {
            Log::error("Stakeholder action creation failed", [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Generate role-specific dashboard data
     */
    public function generateRoleSpecificDashboard(User $user, string $role): array
    {
        try {
            $dashboardData = match($role) {
                'finance_team' => $this->generateFinanceDashboard($user),
                'sustainability_lead' => $this->generateSustainabilityDashboard($user),
                'executive' => $this->generateExecutiveDashboard($user),
                'facility_manager' => $this->generateFacilityDashboard($user),
                'supplier_admin', 'supplier_user' => $this->generateSupplierDashboard($user),
                'auditor' => $this->generateAuditorDashboard($user),
                'consultant' => $this->generateConsultantDashboard($user),
                default => $this->generateDefaultDashboard($user),
            };

            return [
                'success' => true,
                'role' => $role,
                'dashboard_data' => $dashboardData,
                'generated_at' => now(),
            ];

        } catch (\Exception $e) {
            Log::error("Dashboard generation failed", [
                'user_id' => $user->id,
                'role' => $role,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Track action progress and send updates
     */
    public function updateActionProgress(StakeholderAction $action, array $progressData): array
    {
        try {
            // Create progress update
            $progressUpdate = $action->progressUpdates()->create([
                'update_date' => $progressData['update_date'] ?? now(),
                'progress_percentage' => $progressData['progress_percentage'],
                'status' => $progressData['status'] ?? $action->status,
                'notes' => $progressData['notes'] ?? null,
                'attachments' => $progressData['attachments'] ?? null,
                'actual_emissions_reduction' => $progressData['actual_emissions_reduction'] ?? null,
                'actual_cost' => $progressData['actual_cost'] ?? null,
                'actual_savings' => $progressData['actual_savings'] ?? null,
                'updated_by_user_id' => auth()->id(),
            ]);

            // Update action status if provided
            if (isset($progressData['status'])) {
                $action->update(['status' => $progressData['status']]);
            }

            // Update progress percentage
            $action->update(['progress_percentage' => $progressData['progress_percentage']]);

            // Check if action is completed
            if ($progressData['progress_percentage'] >= 100) {
                $action->update([
                    'status' => 'completed',
                    'completed_at' => now(),
                ]);

                $this->sendActionCompletionNotification($action);
            }

            // Send progress notification to stakeholders
            $this->sendProgressUpdateNotification($action, $progressUpdate);

            return [
                'success' => true,
                'action' => $action->fresh(),
                'progress_update' => $progressUpdate,
                'message' => 'Action progress updated successfully',
            ];

        } catch (\Exception $e) {
            Log::error("Action progress update failed", [
                'action_id' => $action->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Generate supplier engagement metrics
     */
    public function generateSupplierEngagementMetrics(Organization $organization): array
    {
        $suppliers = Supplier::where('organization_id', $organization->id)->get();

        $metrics = [
            'total_suppliers' => $suppliers->count(),
            'active_suppliers' => $suppliers->where('is_active', true)->count(),
            'suppliers_with_portal_access' => SupplierPortalAccess::whereIn('supplier_id', $suppliers->pluck('id'))->count(),
            'suppliers_with_data_submissions' => $suppliers->whereHas('supplierEmissions')->count(),
            'average_data_quality' => $this->calculateAverageDataQuality($suppliers),
            'engagement_by_category' => $this->getEngagementByCategory($suppliers),
            'carbon_disclosure_levels' => $this->getCarbonDisclosureLevels($suppliers),
            'recent_activity' => $this->getRecentSupplierActivity($suppliers),
        ];

        return $metrics;
    }

    /**
     * Create enhanced permissions
     */
    protected function createEnhancedPermissions(): array
    {
        $allPermissions = collect($this->rolePermissions)->flatten()->unique();
        $createdPermissions = [];

        foreach ($allPermissions as $permission) {
            $createdPermissions[] = Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web',
            ]);
        }

        return $createdPermissions;
    }

    /**
     * Create stakeholder roles
     */
    protected function createStakeholderRoles(): array
    {
        $createdRoles = [];

        foreach ($this->stakeholderRoles as $roleName => $displayName) {
            $createdRoles[] = Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'web',
            ]);
        }

        return $createdRoles;
    }

    /**
     * Assign permissions to roles
     */
    protected function assignPermissionsToRoles(array $roles, array $permissions): void
    {
        foreach ($roles as $role) {
            if (isset($this->rolePermissions[$role->name])) {
                $rolePermissions = $this->rolePermissions[$role->name];
                $role->syncPermissions($rolePermissions);
            }
        }
    }

    /**
     * Create default dashboards
     */
    protected function createDefaultDashboards(): array
    {
        $dashboards = [];

        foreach ($this->stakeholderRoles as $roleName => $displayName) {
            $dashboards[] = StakeholderDashboard::firstOrCreate([
                'role_name' => $roleName,
                'dashboard_name' => $displayName . ' Dashboard',
                'dashboard_config' => $this->getDefaultDashboardConfig($roleName),
                'is_active' => true,
            ]);
        }

        return $dashboards;
    }

    /**
     * Generate portal URL for supplier
     */
    protected function generatePortalUrl(Supplier $supplier): string
    {
        return config('app.url') . '/supplier-portal/' . $supplier->supplier_code;
    }

    /**
     * Create supplier dashboard
     */
    protected function createSupplierDashboard(Supplier $supplier, User $user): StakeholderDashboard
    {
        return StakeholderDashboard::create([
            'user_id' => $user->id,
            'supplier_id' => $supplier->id,
            'role_name' => 'supplier_admin',
            'dashboard_name' => $supplier->name . ' Dashboard',
            'dashboard_config' => $this->getSupplierDashboardConfig($supplier),
            'is_active' => true,
        ]);
    }

    /**
     * Get default dashboard configuration for role
     */
    protected function getDefaultDashboardConfig(string $role): array
    {
        switch ($role) {
            case 'finance_team':
                return [
                    'widgets' => ['cost_overview', 'investment_analysis', 'roi_metrics', 'budget_tracking'],
                    'charts' => ['cost_trends', 'savings_analysis', 'carbon_pricing'],
                    'tables' => ['investment_projects', 'cost_breakdown'],
                ];
            case 'sustainability_lead':
                return [
                    'widgets' => ['emissions_overview', 'target_progress', 'initiative_status', 'supplier_engagement'],
                    'charts' => ['emissions_trends', 'target_tracking', 'scope_breakdown'],
                    'tables' => ['active_initiatives', 'supplier_performance'],
                ];
            case 'executive':
                return [
                    'widgets' => ['kpi_summary', 'strategic_metrics', 'risk_overview', 'compliance_status'],
                    'charts' => ['performance_trends', 'benchmark_comparison'],
                    'tables' => ['key_initiatives', 'board_metrics'],
                ];
            default:
                return [
                    'widgets' => ['basic_overview', 'recent_activity'],
                    'charts' => ['basic_trends'],
                    'tables' => ['recent_data'],
                ];
        }
    }

    /**
     * Get supplier dashboard configuration
     */
    protected function getSupplierDashboardConfig(Supplier $supplier): array
    {
        return [
            'widgets' => ['emissions_summary', 'data_submission_status', 'engagement_score', 'action_items'],
            'charts' => ['emissions_trends', 'category_breakdown', 'progress_tracking'],
            'tables' => ['recent_submissions', 'assigned_actions', 'compliance_status'],
            'supplier_specific' => [
                'categories' => $supplier->scope3_categories,
                'data_quality_target' => 'B',
                'submission_frequency' => 'quarterly',
            ],
        ];
    }

    // Placeholder methods for dashboard generation
    protected function generateFinanceDashboard(User $user): array
    {
        return ['dashboard_type' => 'finance', 'user_id' => $user->id];
    }

    protected function generateSustainabilityDashboard(User $user): array
    {
        return ['dashboard_type' => 'sustainability', 'user_id' => $user->id];
    }

    protected function generateExecutiveDashboard(User $user): array
    {
        return ['dashboard_type' => 'executive', 'user_id' => $user->id];
    }

    protected function generateFacilityDashboard(User $user): array
    {
        return ['dashboard_type' => 'facility', 'user_id' => $user->id];
    }

    protected function generateSupplierDashboard(User $user): array
    {
        return ['dashboard_type' => 'supplier', 'user_id' => $user->id];
    }

    protected function generateAuditorDashboard(User $user): array
    {
        return ['dashboard_type' => 'auditor', 'user_id' => $user->id];
    }

    protected function generateConsultantDashboard(User $user): array
    {
        return ['dashboard_type' => 'consultant', 'user_id' => $user->id];
    }

    protected function generateDefaultDashboard(User $user): array
    {
        return ['dashboard_type' => 'default', 'user_id' => $user->id];
    }

    // Placeholder methods for notifications and metrics
    protected function sendSupplierWelcomeEmail(Supplier $supplier, User $user, string $password, SupplierPortalAccess $access): void
    {
        Log::info("Supplier welcome email sent", ['supplier_id' => $supplier->id, 'user_id' => $user->id]);
    }

    protected function sendActionAssignmentNotification(StakeholderAction $action): void
    {
        Log::info("Action assignment notification sent", ['action_id' => $action->id]);
    }

    protected function sendActionCompletionNotification(StakeholderAction $action): void
    {
        Log::info("Action completion notification sent", ['action_id' => $action->id]);
    }

    protected function sendProgressUpdateNotification(StakeholderAction $action, $progressUpdate): void
    {
        Log::info("Progress update notification sent", ['action_id' => $action->id]);
    }

    protected function createActionReminder(StakeholderAction $action): void
    {
        Log::info("Action reminder created", ['action_id' => $action->id, 'due_date' => $action->due_date]);
    }

    protected function calculateAverageDataQuality(Collection $suppliers): float
    {
        $qualityMap = ['A' => 4, 'B' => 3, 'C' => 2, 'D' => 1];
        $totalScore = $suppliers->sum(function ($supplier) use ($qualityMap) {
            return $qualityMap[$supplier->data_quality_rating] ?? 0;
        });

        return $suppliers->count() > 0 ? round($totalScore / $suppliers->count(), 2) : 0;
    }

    protected function getEngagementByCategory(Collection $suppliers): array
    {
        return $suppliers->groupBy('supplier_type')->map->count()->toArray();
    }

    protected function getCarbonDisclosureLevels(Collection $suppliers): array
    {
        return $suppliers->groupBy('carbon_disclosure_level')->map->count()->toArray();
    }

    protected function getRecentSupplierActivity(Collection $suppliers): array
    {
        return ['recent_submissions' => 0, 'recent_logins' => 0]; // Placeholder
    }
}
