<?php

namespace App\Services;

use App\Models\ActivityData;
use App\Models\GhgProtocolEmissionFactor;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class MethodologyFrameworkService
{
    protected array $methodologyConfigs = [];
    protected GranularCalculationEngine $calculationEngine;

    public function __construct(GranularCalculationEngine $calculationEngine)
    {
        $this->calculationEngine = $calculationEngine;
        $this->loadMethodologyConfigurations();
    }

    /**
     * Calculate emissions using specified methodology
     */
    public function calculateWithMethodology(ActivityData $activityData, string $methodology, array $options = []): array
    {
        if (!$this->isMethodologySupported($methodology)) {
            return [
                'success' => false,
                'error' => "Methodology not supported: {$methodology}",
                'supported_methodologies' => array_keys($this->methodologyConfigs),
            ];
        }

        $config = $this->methodologyConfigs[$methodology];
        $enhancedOptions = array_merge($options, $config['default_options'] ?? []);

        try {
            switch ($methodology) {
                case 'ghg_protocol':
                    return $this->calculateGHGProtocol($activityData, $enhancedOptions);
                case 'iso_14064':
                    return $this->calculateISO14064($activityData, $enhancedOptions);
                case 'pcaf':
                    return $this->calculatePCAF($activityData, $enhancedOptions);
                case 'custom_hybrid':
                    return $this->calculateCustomHybrid($activityData, $enhancedOptions);
                default:
                    return $this->calculateGHGProtocol($activityData, $enhancedOptions);
            }
        } catch (\Exception $e) {
            Log::error("Methodology calculation failed: {$methodology}", [
                'activity_data_id' => $activityData->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'methodology' => $methodology,
            ];
        }
    }

    /**
     * Calculate using GHG Protocol methodology
     */
    protected function calculateGHGProtocol(ActivityData $activityData, array $options): array
    {
        $options['methodology'] = 'ghg_protocol';
        
        // GHG Protocol specific configurations
        $options['gwp_values'] = $options['gwp_values'] ?? [
            'CO2' => 1,
            'CH4' => 25,  // AR4 100-year GWP
            'N2O' => 298, // AR4 100-year GWP
        ];

        $options['tier'] = $options['tier'] ?? $this->recommendTier($activityData, 'ghg_protocol');
        
        $result = $this->calculationEngine->calculateEmissions($activityData, $options);
        
        if ($result['success']) {
            $result['methodology_details'] = [
                'standard' => 'GHG Protocol Corporate Accounting and Reporting Standard',
                'version' => 'Revised Edition 2015',
                'gwp_source' => 'IPCC Fourth Assessment Report (AR4)',
                'tier_level' => $options['tier'],
                'compliance_notes' => $this->getGHGProtocolComplianceNotes($result),
            ];
        }

        return $result;
    }

    /**
     * Calculate using ISO 14064-1:2018 methodology
     */
    protected function calculateISO14064(ActivityData $activityData, array $options): array
    {
        $options['methodology'] = 'iso_14064';
        
        // ISO 14064 specific configurations
        $options['gwp_values'] = $options['gwp_values'] ?? [
            'CO2' => 1,
            'CH4' => 28,  // AR5 100-year GWP (ISO 14064 recommends latest IPCC)
            'N2O' => 265, // AR5 100-year GWP
        ];

        $options['uncertainty_assessment'] = true;
        $options['data_quality_assessment'] = true;
        
        $result = $this->calculationEngine->calculateEmissions($activityData, $options);
        
        if ($result['success']) {
            $result = $this->addISO14064Enhancements($result, $activityData, $options);
        }

        return $result;
    }

    /**
     * Calculate using PCAF methodology
     */
    protected function calculatePCAF(ActivityData $activityData, array $options): array
    {
        $options['methodology'] = 'pcaf';
        
        // PCAF specific configurations for financed emissions
        $options['data_quality_score'] = $this->calculatePCAFDataQuality($activityData, $options);
        $options['attribution_factor'] = $options['attribution_factor'] ?? 1.0;
        
        $result = $this->calculationEngine->calculateEmissions($activityData, $options);
        
        if ($result['success']) {
            $result = $this->addPCAFEnhancements($result, $activityData, $options);
        }

        return $result;
    }

    /**
     * Calculate using custom hybrid methodology
     */
    protected function calculateCustomHybrid(ActivityData $activityData, array $options): array
    {
        $options['methodology'] = 'custom_hybrid';
        
        // Custom hybrid approach - combines multiple methodologies
        $primaryMethodology = $options['primary_methodology'] ?? 'ghg_protocol';
        $secondaryMethodology = $options['secondary_methodology'] ?? 'iso_14064';
        
        // Calculate with primary methodology
        $primaryResult = $this->calculateWithMethodology($activityData, $primaryMethodology, $options);
        
        // Calculate with secondary methodology for comparison
        $secondaryResult = $this->calculateWithMethodology($activityData, $secondaryMethodology, $options);
        
        // Apply hybrid logic
        $hybridResult = $this->applyHybridLogic($primaryResult, $secondaryResult, $options);
        
        $hybridResult['methodology_details'] = [
            'approach' => 'custom_hybrid',
            'primary_methodology' => $primaryMethodology,
            'secondary_methodology' => $secondaryMethodology,
            'hybrid_logic' => $options['hybrid_logic'] ?? 'primary_with_secondary_validation',
        ];

        return $hybridResult;
    }

    /**
     * Add ISO 14064 specific enhancements
     */
    protected function addISO14064Enhancements(array $result, ActivityData $activityData, array $options): array
    {
        // Add uncertainty assessment
        $result['uncertainty_assessment'] = $this->calculateUncertainty($result, $options);
        
        // Add data quality assessment
        $result['data_quality_assessment'] = $this->assessDataQuality($activityData, $options);
        
        // Add ISO 14064 specific metadata
        $result['methodology_details'] = [
            'standard' => 'ISO 14064-1:2018',
            'gwp_source' => 'IPCC Fifth Assessment Report (AR5)',
            'uncertainty_level' => $result['uncertainty_assessment']['level'],
            'data_quality_rating' => $result['data_quality_assessment']['rating'],
            'verification_requirements' => $this->getISO14064VerificationRequirements($result),
        ];

        return $result;
    }

    /**
     * Add PCAF specific enhancements
     */
    protected function addPCAFEnhancements(array $result, ActivityData $activityData, array $options): array
    {
        $dataQualityScore = $options['data_quality_score'];
        $attributionFactor = $options['attribution_factor'];
        
        // Apply attribution factor for financed emissions
        if (isset($result['total_co2e'])) {
            $result['financed_emissions'] = $result['total_co2e'] * $attributionFactor;
        }
        
        $result['methodology_details'] = [
            'standard' => 'Partnership for Carbon Accounting Financials (PCAF)',
            'version' => 'Global GHG Accounting and Reporting Standard Part A',
            'data_quality_score' => $dataQualityScore,
            'attribution_factor' => $attributionFactor,
            'asset_class' => $options['asset_class'] ?? 'corporate_loans',
            'pcaf_requirements' => $this->getPCAFRequirements($dataQualityScore),
        ];

        return $result;
    }

    /**
     * Apply hybrid methodology logic
     */
    protected function applyHybridLogic(array $primaryResult, array $secondaryResult, array $options): array
    {
        $logic = $options['hybrid_logic'] ?? 'primary_with_secondary_validation';
        
        switch ($logic) {
            case 'primary_with_secondary_validation':
                return $this->validateWithSecondary($primaryResult, $secondaryResult);
            case 'average':
                return $this->averageResults($primaryResult, $secondaryResult);
            case 'conservative':
                return $this->selectConservativeResult($primaryResult, $secondaryResult);
            case 'best_data_quality':
                return $this->selectBestDataQuality($primaryResult, $secondaryResult);
            default:
                return $primaryResult;
        }
    }

    /**
     * Validate primary result with secondary methodology
     */
    protected function validateWithSecondary(array $primaryResult, array $secondaryResult): array
    {
        if (!$primaryResult['success'] || !$secondaryResult['success']) {
            return $primaryResult['success'] ? $primaryResult : $secondaryResult;
        }

        $primaryEmissions = $primaryResult['total_co2e'] ?? 0;
        $secondaryEmissions = $secondaryResult['total_co2e'] ?? 0;
        
        $variance = abs($primaryEmissions - $secondaryEmissions) / max($primaryEmissions, $secondaryEmissions, 1);
        
        $primaryResult['validation'] = [
            'secondary_methodology' => $secondaryResult['methodology'] ?? 'unknown',
            'secondary_emissions' => $secondaryEmissions,
            'variance_percentage' => $variance * 100,
            'validation_status' => $variance < 0.1 ? 'validated' : 'requires_review',
        ];

        return $primaryResult;
    }

    /**
     * Calculate PCAF data quality score
     */
    protected function calculatePCAFDataQuality(ActivityData $activityData, array $options): int
    {
        // PCAF data quality scoring (1-5, where 1 is highest quality)
        $score = 5; // Start with lowest quality
        
        // Improve score based on data characteristics
        if ($activityData->emissionFactor && $activityData->emissionFactor->data_quality_rating === 'High') {
            $score = min($score, 2);
        }
        
        if ($activityData->emissionFactor && $activityData->emissionFactor->tier_level >= 2) {
            $score = min($score, 3);
        }
        
        if (isset($options['primary_data']) && $options['primary_data']) {
            $score = 1;
        }

        return $score;
    }

    /**
     * Calculate uncertainty
     */
    protected function calculateUncertainty(array $result, array $options): array
    {
        // Simplified uncertainty calculation
        $baseUncertainty = 0.1; // 10% base uncertainty
        
        // Adjust based on data quality
        if (isset($options['data_quality_rating'])) {
            switch ($options['data_quality_rating']) {
                case 'High':
                    $baseUncertainty = 0.05;
                    break;
                case 'Medium':
                    $baseUncertainty = 0.15;
                    break;
                case 'Low':
                    $baseUncertainty = 0.25;
                    break;
            }
        }

        $emissions = $result['total_co2e'] ?? 0;
        $uncertainty = $emissions * $baseUncertainty;

        return [
            'level' => $baseUncertainty * 100 . '%',
            'absolute_uncertainty' => $uncertainty,
            'lower_bound' => $emissions - $uncertainty,
            'upper_bound' => $emissions + $uncertainty,
        ];
    }

    /**
     * Assess data quality
     */
    protected function assessDataQuality(ActivityData $activityData, array $options): array
    {
        $score = 0;
        $maxScore = 10;
        $factors = [];

        // Factor 1: Emission factor quality
        if ($activityData->emissionFactor) {
            switch ($activityData->emissionFactor->data_quality_rating) {
                case 'High':
                    $score += 3;
                    $factors[] = 'High quality emission factor';
                    break;
                case 'Medium':
                    $score += 2;
                    $factors[] = 'Medium quality emission factor';
                    break;
                case 'Low':
                    $score += 1;
                    $factors[] = 'Low quality emission factor';
                    break;
            }
        }

        // Factor 2: Data completeness
        if ($activityData->quantity && $activityData->activity_unit_id) {
            $score += 2;
            $factors[] = 'Complete activity data';
        }

        // Factor 3: Temporal relevance
        $dataAge = now()->diffInDays($activityData->date_recorded);
        if ($dataAge <= 365) {
            $score += 2;
            $factors[] = 'Recent data (within 1 year)';
        } elseif ($dataAge <= 1095) {
            $score += 1;
            $factors[] = 'Moderately recent data (within 3 years)';
        }

        // Factor 4: Geographic relevance
        if ($activityData->facility && $activityData->facility->region_id) {
            $score += 1;
            $factors[] = 'Geographic specificity available';
        }

        // Factor 5: Methodology tier
        if ($activityData->emissionFactor && $activityData->emissionFactor->tier_level >= 2) {
            $score += 2;
            $factors[] = 'Tier 2+ methodology';
        }

        $rating = $this->scoreToRating($score, $maxScore);

        return [
            'score' => $score,
            'max_score' => $maxScore,
            'percentage' => ($score / $maxScore) * 100,
            'rating' => $rating,
            'factors' => $factors,
        ];
    }

    /**
     * Convert score to rating
     */
    protected function scoreToRating(int $score, int $maxScore): string
    {
        $percentage = ($score / $maxScore) * 100;
        
        if ($percentage >= 80) return 'Excellent';
        if ($percentage >= 60) return 'Good';
        if ($percentage >= 40) return 'Fair';
        if ($percentage >= 20) return 'Poor';
        return 'Very Poor';
    }

    /**
     * Recommend tier level based on methodology and data
     */
    protected function recommendTier(ActivityData $activityData, string $methodology): int
    {
        // Default to Tier 1
        $tier = 1;
        
        // Upgrade to Tier 2 if we have regional factors
        if ($activityData->facility && $activityData->facility->region_id) {
            $tier = 2;
        }
        
        // Upgrade to Tier 3 if we have facility-specific factors
        if ($activityData->emissionFactor && $activityData->emissionFactor->tier_level >= 3) {
            $tier = 3;
        }

        return $tier;
    }

    /**
     * Load methodology configurations
     */
    protected function loadMethodologyConfigurations(): void
    {
        $this->methodologyConfigs = [
            'ghg_protocol' => [
                'name' => 'GHG Protocol Corporate Standard',
                'version' => 'Revised Edition 2015',
                'default_options' => [
                    'gwp_source' => 'AR4',
                    'tier' => 1,
                ],
                'supported_scopes' => ['1', '2', '3'],
                'required_data' => ['activity_data', 'emission_factors'],
            ],
            'iso_14064' => [
                'name' => 'ISO 14064-1:2018',
                'version' => '2018',
                'default_options' => [
                    'gwp_source' => 'AR5',
                    'uncertainty_assessment' => true,
                ],
                'supported_scopes' => ['1', '2', '3'],
                'required_data' => ['activity_data', 'emission_factors', 'uncertainty_data'],
            ],
            'pcaf' => [
                'name' => 'Partnership for Carbon Accounting Financials',
                'version' => 'Part A 2020',
                'default_options' => [
                    'data_quality_scoring' => true,
                    'attribution_required' => true,
                ],
                'supported_scopes' => ['3'],
                'required_data' => ['financial_data', 'emission_factors', 'attribution_factors'],
            ],
            'custom_hybrid' => [
                'name' => 'Custom Hybrid Methodology',
                'version' => '1.0',
                'default_options' => [
                    'primary_methodology' => 'ghg_protocol',
                    'validation_required' => true,
                ],
                'supported_scopes' => ['1', '2', '3'],
                'required_data' => ['activity_data', 'emission_factors'],
            ],
        ];
    }

    /**
     * Check if methodology is supported
     */
    public function isMethodologySupported(string $methodology): bool
    {
        return array_key_exists($methodology, $this->methodologyConfigs);
    }

    /**
     * Get methodology configuration
     */
    public function getMethodologyConfig(string $methodology): ?array
    {
        return $this->methodologyConfigs[$methodology] ?? null;
    }

    /**
     * Get all supported methodologies
     */
    public function getSupportedMethodologies(): array
    {
        return $this->methodologyConfigs;
    }

    /**
     * Get GHG Protocol compliance notes
     */
    protected function getGHGProtocolComplianceNotes(array $result): array
    {
        return [
            'scope_coverage' => 'Calculation covers required scope',
            'factor_source' => 'Uses approved emission factors',
            'methodology_tier' => 'Meets minimum tier requirements',
            'reporting_ready' => true,
        ];
    }

    /**
     * Get ISO 14064 verification requirements
     */
    protected function getISO14064VerificationRequirements(array $result): array
    {
        return [
            'internal_verification' => 'Required for all calculations',
            'external_verification' => 'Recommended for public reporting',
            'documentation_level' => 'Comprehensive',
            'uncertainty_reporting' => 'Required',
        ];
    }

    /**
     * Get PCAF requirements based on data quality score
     */
    protected function getPCAFRequirements(int $dataQualityScore): array
    {
        $requirements = [
            1 => ['disclosure' => 'Full disclosure recommended', 'verification' => 'High confidence'],
            2 => ['disclosure' => 'Full disclosure recommended', 'verification' => 'Good confidence'],
            3 => ['disclosure' => 'Disclosure with caveats', 'verification' => 'Medium confidence'],
            4 => ['disclosure' => 'Disclosure with significant caveats', 'verification' => 'Low confidence'],
            5 => ['disclosure' => 'Disclosure not recommended', 'verification' => 'Very low confidence'],
        ];

        return $requirements[$dataQualityScore] ?? $requirements[5];
    }
}
