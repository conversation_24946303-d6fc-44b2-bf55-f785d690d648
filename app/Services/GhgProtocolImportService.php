<?php

namespace App\Services;

use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\EmissionFactorVariant;
use App\Models\GreenhouseGas;
use App\Models\MeasurementUnit;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GhgProtocolImportService
{
    protected array $importStats = [
        'sectors' => 0,
        'activities' => 0,
        'regions' => 0,
        'factors' => 0,
        'variants' => 0,
        'errors' => []
    ];

    /**
     * Import GHG Protocol data from structured array
     */
    public function importFromArray(array $data): array
    {
        DB::beginTransaction();
        
        try {
            // Import in dependency order
            $this->importSectors($data['sectors'] ?? []);
            $this->importRegions($data['regions'] ?? []);
            $this->importActivities($data['activities'] ?? []);
            $this->importEmissionFactors($data['emission_factors'] ?? []);
            
            DB::commit();
            
            return [
                'success' => true,
                'stats' => $this->importStats
            ];
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('GHG Protocol import failed: ' . $e->getMessage());
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'stats' => $this->importStats
            ];
        }
    }

    /**
     * Import sectors
     */
    protected function importSectors(array $sectors): void
    {
        foreach ($sectors as $sectorData) {
            try {
                GhgProtocolSector::updateOrCreate(
                    ['name' => $sectorData['name']],
                    [
                        'display_name' => $sectorData['display_name'] ?? $sectorData['name'],
                        'description' => $sectorData['description'] ?? null,
                        'sort_order' => $sectorData['sort_order'] ?? 0,
                        'is_active' => $sectorData['is_active'] ?? true,
                    ]
                );
                
                $this->importStats['sectors']++;
                
            } catch (\Exception $e) {
                $this->importStats['errors'][] = "Sector import error: {$e->getMessage()}";
            }
        }
    }

    /**
     * Import regions
     */
    protected function importRegions(array $regions): void
    {
        foreach ($regions as $regionData) {
            try {
                GhgProtocolRegion::updateOrCreate(
                    ['code' => $regionData['code']],
                    [
                        'name' => $regionData['name'],
                        'region_type' => $regionData['region_type'],
                        'parent_code' => $regionData['parent_code'] ?? null,
                        'iso_country_code' => $regionData['iso_country_code'] ?? null,
                        'iso_subdivision_code' => $regionData['iso_subdivision_code'] ?? null,
                        'latitude' => $regionData['latitude'] ?? null,
                        'longitude' => $regionData['longitude'] ?? null,
                        'description' => $regionData['description'] ?? null,
                        'applicable_sectors' => $regionData['applicable_sectors'] ?? null,
                        'is_active' => $regionData['is_active'] ?? true,
                        'sort_order' => $regionData['sort_order'] ?? 0,
                    ]
                );
                
                $this->importStats['regions']++;
                
            } catch (\Exception $e) {
                $this->importStats['errors'][] = "Region import error: {$e->getMessage()}";
            }
        }
    }

    /**
     * Import activities
     */
    protected function importActivities(array $activities): void
    {
        foreach ($activities as $activityData) {
            try {
                $sector = GhgProtocolSector::where('name', $activityData['sector_name'])->first();
                
                if (!$sector) {
                    $this->importStats['errors'][] = "Sector not found for activity: {$activityData['name']}";
                    continue;
                }

                GhgProtocolActivity::updateOrCreate(
                    ['code' => $activityData['code']],
                    [
                        'sector_id' => $sector->id,
                        'name' => $activityData['name'],
                        'display_name' => $activityData['display_name'] ?? $activityData['name'],
                        'description' => $activityData['description'] ?? null,
                        'activity_type' => $activityData['activity_type'] ?? null,
                        'fuel_type' => $activityData['fuel_type'] ?? null,
                        'vehicle_type' => $activityData['vehicle_type'] ?? null,
                        'equipment_type' => $activityData['equipment_type'] ?? null,
                        'applicable_scopes' => $activityData['applicable_scopes'] ?? null,
                        'calculation_methods' => $activityData['calculation_methods'] ?? null,
                        'sort_order' => $activityData['sort_order'] ?? 0,
                        'is_active' => $activityData['is_active'] ?? true,
                        'notes' => $activityData['notes'] ?? null,
                    ]
                );
                
                $this->importStats['activities']++;
                
            } catch (\Exception $e) {
                $this->importStats['errors'][] = "Activity import error: {$e->getMessage()}";
            }
        }
    }

    /**
     * Import emission factors
     */
    protected function importEmissionFactors(array $factors): void
    {
        foreach ($factors as $factorData) {
            try {
                $factor = $this->createEmissionFactor($factorData);
                
                if ($factor) {
                    $this->importStats['factors']++;
                    
                    // Import variants if present
                    if (isset($factorData['variants'])) {
                        $this->importFactorVariants($factor, $factorData['variants']);
                    }
                }
                
            } catch (\Exception $e) {
                $this->importStats['errors'][] = "Factor import error: {$e->getMessage()}";
            }
        }
    }

    /**
     * Create a single emission factor
     */
    protected function createEmissionFactor(array $factorData): ?GhgProtocolEmissionFactor
    {
        // Resolve foreign key relationships
        $sector = GhgProtocolSector::where('name', $factorData['sector_name'])->first();
        $activity = GhgProtocolActivity::where('code', $factorData['activity_code'])->first();
        $gas = GreenhouseGas::where('chemical_formula', $factorData['gas_formula'])->first();
        $inputUnit = MeasurementUnit::where('symbol', $factorData['input_unit_symbol'])->first();
        $outputUnit = MeasurementUnit::where('symbol', $factorData['output_unit_symbol'])->first();
        
        $region = null;
        if (isset($factorData['region_code'])) {
            $region = GhgProtocolRegion::where('code', $factorData['region_code'])->first();
        }

        if (!$sector || !$activity || !$gas || !$inputUnit || !$outputUnit) {
            $missing = [];
            if (!$sector) $missing[] = 'sector';
            if (!$activity) $missing[] = 'activity';
            if (!$gas) $missing[] = 'gas';
            if (!$inputUnit) $missing[] = 'input_unit';
            if (!$outputUnit) $missing[] = 'output_unit';
            
            $this->importStats['errors'][] = "Missing references for factor {$factorData['name']}: " . implode(', ', $missing);
            return null;
        }

        return GhgProtocolEmissionFactor::updateOrCreate(
            ['code' => $factorData['code']],
            [
                'name' => $factorData['name'],
                'description' => $factorData['description'] ?? null,
                'sector_id' => $sector->id,
                'activity_id' => $activity->id,
                'region_id' => $region?->id,
                'gas_id' => $gas->id,
                'calculation_method' => $factorData['calculation_method'] ?? 'tier1',
                'methodology_reference' => $factorData['methodology_reference'] ?? null,
                'factor_value' => $factorData['factor_value'],
                'co2_factor' => $factorData['co2_factor'] ?? null,
                'ch4_factor' => $factorData['ch4_factor'] ?? null,
                'n2o_factor' => $factorData['n2o_factor'] ?? null,
                'carbon_content' => $factorData['carbon_content'] ?? null,
                'oxidation_factor' => $factorData['oxidation_factor'] ?? null,
                'heating_value' => $factorData['heating_value'] ?? null,
                'heating_value_type' => $factorData['heating_value_type'] ?? null,
                'input_unit_id' => $inputUnit->id,
                'output_unit_id' => $outputUnit->id,
                'data_quality_rating' => $factorData['data_quality_rating'] ?? null,
                'uncertainty_percentage' => $factorData['uncertainty_percentage'] ?? null,
                'uncertainty_lower' => $factorData['uncertainty_lower'] ?? null,
                'uncertainty_upper' => $factorData['uncertainty_upper'] ?? null,
                'uncertainty_type' => $factorData['uncertainty_type'] ?? null,
                'vintage_year' => $factorData['vintage_year'] ?? null,
                'valid_from' => $factorData['valid_from'] ?? null,
                'valid_until' => $factorData['valid_until'] ?? null,
                'data_source' => $factorData['data_source'] ?? 'GHG Protocol',
                'data_source_detail' => $factorData['data_source_detail'] ?? null,
                'reference_link' => $factorData['reference_link'] ?? null,
                'notes' => $factorData['notes'] ?? null,
                'metadata' => $factorData['metadata'] ?? null,
                'is_default' => $factorData['is_default'] ?? false,
                'is_active' => $factorData['is_active'] ?? true,
                'tier_level' => $factorData['tier_level'] ?? 1,
                'priority' => $factorData['priority'] ?? 0,
            ]
        );
    }

    /**
     * Import factor variants
     */
    protected function importFactorVariants(GhgProtocolEmissionFactor $baseFactor, array $variants): void
    {
        foreach ($variants as $variantData) {
            try {
                $region = null;
                if (isset($variantData['region_code'])) {
                    $region = GhgProtocolRegion::where('code', $variantData['region_code'])->first();
                }

                EmissionFactorVariant::updateOrCreate(
                    [
                        'base_factor_id' => $baseFactor->id,
                        'variant_code' => $variantData['variant_code']
                    ],
                    [
                        'variant_type' => $variantData['variant_type'],
                        'variant_name' => $variantData['variant_name'],
                        'variant_description' => $variantData['variant_description'] ?? null,
                        'region_id' => $region?->id,
                        'factor_value' => $variantData['factor_value'] ?? null,
                        'co2_factor' => $variantData['co2_factor'] ?? null,
                        'ch4_factor' => $variantData['ch4_factor'] ?? null,
                        'n2o_factor' => $variantData['n2o_factor'] ?? null,
                        'carbon_content' => $variantData['carbon_content'] ?? null,
                        'oxidation_factor' => $variantData['oxidation_factor'] ?? null,
                        'heating_value' => $variantData['heating_value'] ?? null,
                        'data_quality_rating' => $variantData['data_quality_rating'] ?? null,
                        'uncertainty_percentage' => $variantData['uncertainty_percentage'] ?? null,
                        'uncertainty_lower' => $variantData['uncertainty_lower'] ?? null,
                        'uncertainty_upper' => $variantData['uncertainty_upper'] ?? null,
                        'vintage_year' => $variantData['vintage_year'] ?? null,
                        'valid_from' => $variantData['valid_from'] ?? null,
                        'valid_until' => $variantData['valid_until'] ?? null,
                        'variant_conditions' => $variantData['variant_conditions'] ?? null,
                        'notes' => $variantData['notes'] ?? null,
                        'priority' => $variantData['priority'] ?? 0,
                        'is_active' => $variantData['is_active'] ?? true,
                    ]
                );
                
                $this->importStats['variants']++;
                
            } catch (\Exception $e) {
                $this->importStats['errors'][] = "Variant import error: {$e->getMessage()}";
            }
        }
    }

    /**
     * Get import statistics
     */
    public function getImportStats(): array
    {
        return $this->importStats;
    }

    /**
     * Reset import statistics
     */
    public function resetStats(): void
    {
        $this->importStats = [
            'sectors' => 0,
            'activities' => 0,
            'regions' => 0,
            'factors' => 0,
            'variants' => 0,
            'errors' => []
        ];
    }
}
