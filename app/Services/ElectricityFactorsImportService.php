<?php

namespace App\Services;

use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GreenhouseGas;
use App\Models\MeasurementUnit;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ElectricityFactorsImportService
{
    protected array $importStats = [
        'regions_created' => 0,
        'factors_created' => 0,
        'factors_updated' => 0,
        'errors' => []
    ];

    protected $electricitySector;
    protected $electricityActivity;
    protected $co2Gas;
    protected $ch4Gas;
    protected $n2oGas;
    protected $kwhUnit;
    protected $mwhUnit;
    protected $kgCo2eUnit;
    protected $tCo2eUnit;

    public function __construct()
    {
        $this->initializeReferences();
    }

    /**
     * Import electricity factors from structured data
     */
    public function importElectricityFactors(array $electricityData): array
    {
        DB::beginTransaction();
        
        try {
            foreach ($electricityData as $tableData) {
                $this->processElectricityTable($tableData);
            }
            
            DB::commit();
            
            return [
                'success' => true,
                'stats' => $this->importStats
            ];
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Electricity factors import failed: ' . $e->getMessage());
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'stats' => $this->importStats
            ];
        }
    }

    /**
     * Process a single electricity table (e.g., China, Taiwan, etc.)
     */
    protected function processElectricityTable(array $tableData): void
    {
        $regionName = $tableData['region'];
        $tableName = $tableData['table_name'];
        $data = $tableData['data'];
        $notes = $tableData['notes'] ?? '';

        // Create or get region
        $region = $this->createOrGetRegion($regionName, $notes);

        // Process each row of data
        foreach ($data as $rowData) {
            $this->processElectricityRow($region, $rowData, $tableName, $notes);
        }
    }

    /**
     * Process a single row of electricity data
     */
    protected function processElectricityRow($region, array $rowData, string $tableName, string $notes): void
    {
        $year = $rowData['year'];
        
        // Handle different column structures
        if (isset($rowData['co2_factor'])) {
            // Single CO2 factor
            $this->createElectricityFactor($region, $year, 'CO2', $rowData['co2_factor'], $rowData['unit'], $tableName, $notes);
        }
        
        if (isset($rowData['ch4_factor'])) {
            // CH4 factor
            $this->createElectricityFactor($region, $year, 'CH4', $rowData['ch4_factor'], $rowData['ch4_unit'] ?? $rowData['unit'], $tableName, $notes);
        }
        
        if (isset($rowData['n2o_factor'])) {
            // N2O factor
            $this->createElectricityFactor($region, $year, 'N2O', $rowData['n2o_factor'], $rowData['n2o_unit'] ?? $rowData['unit'], $tableName, $notes);
        }
        
        if (isset($rowData['emission_factor'])) {
            // Generic emission factor (assume CO2)
            $this->createElectricityFactor($region, $year, 'CO2', $rowData['emission_factor'], $rowData['unit'], $tableName, $notes);
        }
    }

    /**
     * Create electricity emission factor
     */
    protected function createElectricityFactor($region, int $year, string $gasType, float $factorValue, string $unit, string $tableName, string $notes): void
    {
        $gas = $this->getGasByType($gasType);
        $inputUnit = $this->getInputUnit($unit);
        $outputUnit = $this->getOutputUnit($unit, $gasType);

        if (!$gas || !$inputUnit || !$outputUnit) {
            $this->importStats['errors'][] = "Missing references for {$region->name} {$year} {$gasType}";
            return;
        }

        // Convert factor value to standard units if needed
        $standardizedValue = $this->standardizeFactorValue($factorValue, $unit, $gasType);

        $factorCode = "ELEC_GRID_{$gasType}_{$region->code}_{$year}";

        try {
            $factor = GhgProtocolEmissionFactor::updateOrCreate(
                ['code' => $factorCode],
                [
                    'name' => "Grid Electricity {$gasType} Factor - {$region->name} ({$year})",
                    'description' => "Grid electricity {$gasType} emission factor for {$region->name} in {$year}",
                    'sector_id' => $this->electricitySector->id,
                    'activity_id' => $this->electricityActivity->id,
                    'region_id' => $region->id,
                    'gas_id' => $gas->id,
                    'calculation_method' => 'location_based',
                    'methodology_reference' => $tableName,
                    'factor_value' => $standardizedValue,
                    'input_unit_id' => $inputUnit->id,
                    'output_unit_id' => $outputUnit->id,
                    'data_quality_rating' => $this->determineDataQuality($region->name, $gasType),
                    'uncertainty_percentage' => $this->estimateUncertainty($region->name, $gasType),
                    'vintage_year' => $year,
                    'valid_from' => "{$year}-01-01",
                    'valid_until' => "{$year}-12-31",
                    'data_source' => 'GHG Protocol Cross-Sector Tools',
                    'data_source_detail' => $tableName,
                    'notes' => $notes,
                    'metadata' => [
                        'original_value' => $factorValue,
                        'original_unit' => $unit,
                        'table_name' => $tableName
                    ],
                    'is_default' => false,
                    'is_active' => true,
                    'tier_level' => 1,
                    'priority' => $this->calculatePriority($region->name, $year),
                ]
            );

            if ($factor->wasRecentlyCreated) {
                $this->importStats['factors_created']++;
            } else {
                $this->importStats['factors_updated']++;
            }

        } catch (\Exception $e) {
            $this->importStats['errors'][] = "Failed to create factor {$factorCode}: {$e->getMessage()}";
        }
    }

    /**
     * Create or get region
     */
    protected function createOrGetRegion(string $regionName, string $notes): GhgProtocolRegion
    {
        $regionCode = $this->generateRegionCode($regionName);
        $regionType = $this->determineRegionType($regionName);
        $parentCode = $this->determineParentRegion($regionName);

        $region = GhgProtocolRegion::updateOrCreate(
            ['code' => $regionCode],
            [
                'name' => $regionName,
                'region_type' => $regionType,
                'parent_code' => $parentCode,
                'iso_country_code' => $this->getIsoCountryCode($regionName),
                'description' => "Electricity grid region for {$regionName}",
                'applicable_sectors' => ['electricity'],
                'is_active' => true,
                'sort_order' => 0,
            ]
        );

        if ($region->wasRecentlyCreated) {
            $this->importStats['regions_created']++;
        }

        return $region;
    }

    /**
     * Initialize model references
     */
    protected function initializeReferences(): void
    {
        $this->electricitySector = GhgProtocolSector::where('name', 'electricity')->first();
        $this->electricityActivity = GhgProtocolActivity::where('code', 'ELEC_GRID')->first();
        $this->co2Gas = GreenhouseGas::where('chemical_formula', 'CO₂')->first();
        $this->ch4Gas = GreenhouseGas::where('chemical_formula', 'CH₄')->first();
        $this->n2oGas = GreenhouseGas::where('chemical_formula', 'N₂O')->first();
        $this->kwhUnit = MeasurementUnit::where('symbol', 'kWh')->first();
        $this->mwhUnit = MeasurementUnit::where('symbol', 'MWh')->first();
        $this->kgCo2eUnit = MeasurementUnit::where('symbol', 'kg CO₂e')->first();
        $this->tCo2eUnit = MeasurementUnit::where('symbol', 't CO₂e')->first();
    }

    /**
     * Get gas by type
     */
    protected function getGasByType(string $gasType)
    {
        switch (strtoupper($gasType)) {
            case 'CO2':
                return $this->co2Gas;
            case 'CH4':
                return $this->ch4Gas;
            case 'N2O':
                return $this->n2oGas;
            default:
                return $this->co2Gas; // Default to CO2
        }
    }

    /**
     * Get input unit based on unit string
     */
    protected function getInputUnit(string $unit)
    {
        if (stripos($unit, 'MWh') !== false) {
            return $this->mwhUnit;
        }
        return $this->kwhUnit; // Default to kWh
    }

    /**
     * Get output unit based on unit string and gas type
     */
    protected function getOutputUnit(string $unit, string $gasType)
    {
        if (stripos($unit, 'tCO') !== false || stripos($unit, 't CO') !== false) {
            return $this->tCo2eUnit;
        }
        return $this->kgCo2eUnit; // Default to kg CO2e
    }

    /**
     * Standardize factor value to kg CO2e/kWh
     */
    protected function standardizeFactorValue(float $value, string $unit, string $gasType): float
    {
        // Convert tCO2/MWh to kgCO2e/kWh
        if (stripos($unit, 'tCO') !== false && stripos($unit, 'MWh') !== false) {
            return $value; // tCO2/MWh = kgCO2e/kWh
        }
        
        // Convert other units as needed
        return $value;
    }

    /**
     * Helper methods for region and data processing
     */
    protected function generateRegionCode(string $regionName): string
    {
        $codes = [
            'China' => 'CN',
            'Taiwan' => 'TW',
            'Brazil' => 'BR',
            'Thailand' => 'TH',
            'U.K.' => 'GB',
            'United Kingdom' => 'GB',
        ];
        
        return $codes[$regionName] ?? strtoupper(substr($regionName, 0, 2));
    }

    protected function determineRegionType(string $regionName): string
    {
        return 'country'; // All these appear to be countries
    }

    protected function determineParentRegion(string $regionName): ?string
    {
        return null; // These are top-level countries
    }

    protected function getIsoCountryCode(string $regionName): ?string
    {
        $codes = [
            'China' => 'CHN',
            'Taiwan' => 'TWN',
            'Brazil' => 'BRA',
            'Thailand' => 'THA',
            'U.K.' => 'GBR',
            'United Kingdom' => 'GBR',
        ];
        
        return $codes[$regionName] ?? null;
    }

    protected function determineDataQuality(string $regionName, string $gasType): string
    {
        // CO2 factors generally have better quality than CH4/N2O
        if ($gasType === 'CO2') {
            return 'A';
        }
        return 'B';
    }

    protected function estimateUncertainty(string $regionName, string $gasType): float
    {
        // Estimate uncertainty based on gas type
        switch ($gasType) {
            case 'CO2':
                return 10.0;
            case 'CH4':
                return 50.0;
            case 'N2O':
                return 100.0;
            default:
                return 20.0;
        }
    }

    protected function calculatePriority(string $regionName, int $year): int
    {
        // More recent years get higher priority
        $basePriority = 100;
        $yearBonus = max(0, $year - 2000); // Bonus for more recent years
        return $basePriority + $yearBonus;
    }

    /**
     * Get import statistics
     */
    public function getImportStats(): array
    {
        return $this->importStats;
    }
}
