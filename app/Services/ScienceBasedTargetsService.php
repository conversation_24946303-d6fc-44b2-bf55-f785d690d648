<?php

namespace App\Services;

use App\Models\EmissionTarget;
use App\Models\Organization;
use App\Models\ActivityData;
use App\Models\Facility;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class ScienceBasedTargetsService
{
    protected array $sectorPathways = [];
    protected array $temperatureScenarios = [
        '1.5C' => [
            'annual_reduction_rate' => 0.045, // 4.5% per year
            'net_zero_year' => 2050,
            'interim_target_year' => 2030,
            'interim_reduction' => 0.45, // 45% by 2030
        ],
        '2C' => [
            'annual_reduction_rate' => 0.025, // 2.5% per year
            'net_zero_year' => 2070,
            'interim_target_year' => 2030,
            'interim_reduction' => 0.25, // 25% by 2030
        ],
        'well_below_2C' => [
            'annual_reduction_rate' => 0.035, // 3.5% per year
            'net_zero_year' => 2060,
            'interim_target_year' => 2030,
            'interim_reduction' => 0.35, // 35% by 2030
        ],
    ];

    public function __construct()
    {
        $this->loadSectorPathways();
    }

    /**
     * Generate science-based target recommendations
     */
    public function generateSBTiRecommendations(Organization $organization, array $options = []): array
    {
        $scenario = $options['scenario'] ?? '1.5C';
        $baseYear = $options['base_year'] ?? 2019;
        $targetYear = $options['target_year'] ?? 2030;
        
        try {
            // Get organization's baseline emissions
            $baseline = $this->calculateBaselineEmissions($organization, $baseYear);
            
            if (!$baseline['success']) {
                return [
                    'success' => false,
                    'error' => 'Unable to calculate baseline emissions',
                    'details' => $baseline,
                ];
            }

            // Get sector-specific pathway
            $sectorPathway = $this->getSectorPathway($organization, $scenario);
            
            // Calculate targets for each scope
            $targets = $this->calculateSBTiTargets($baseline['emissions'], $sectorPathway, $baseYear, $targetYear);
            
            // Generate net-zero roadmap
            $netZeroRoadmap = $this->generateNetZeroRoadmap($baseline['emissions'], $sectorPathway, $baseYear);
            
            // Validate against SBTi criteria
            $validation = $this->validateSBTiCompliance($targets, $sectorPathway);

            return [
                'success' => true,
                'scenario' => $scenario,
                'baseline' => $baseline,
                'targets' => $targets,
                'net_zero_roadmap' => $netZeroRoadmap,
                'validation' => $validation,
                'sector_pathway' => $sectorPathway,
                'recommendations' => $this->generateRecommendations($targets, $validation),
            ];

        } catch (\Exception $e) {
            Log::error('SBTi recommendations failed', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Calculate baseline emissions for organization
     */
    protected function calculateBaselineEmissions(Organization $organization, int $baseYear): array
    {
        $emissions = ActivityData::where('organization_id', $organization->id)
            ->whereYear('date_recorded', $baseYear)
            ->with(['emissionCalculation'])
            ->get();

        if ($emissions->isEmpty()) {
            return [
                'success' => false,
                'error' => "No emissions data found for base year {$baseYear}",
            ];
        }

        $scope1 = $emissions->where('scope', '1')->sum('calculated_emissions');
        $scope2 = $emissions->where('scope', '2')->sum('calculated_emissions');
        $scope3 = $emissions->where('scope', '3')->sum('calculated_emissions');
        $total = $scope1 + $scope2 + $scope3;

        return [
            'success' => true,
            'base_year' => $baseYear,
            'emissions' => [
                'scope_1' => $scope1,
                'scope_2' => $scope2,
                'scope_3' => $scope3,
                'total' => $total,
            ],
            'data_quality' => $this->assessDataQuality($emissions),
        ];
    }

    /**
     * Get sector-specific decarbonization pathway
     */
    protected function getSectorPathway(Organization $organization, string $scenario): array
    {
        $sector = $organization->industry_sector ?? 'general';
        $pathwayTemplate = $this->temperatureScenarios[$scenario];
        
        // Apply sector-specific adjustments
        if (isset($this->sectorPathways[$sector])) {
            $sectorAdjustments = $this->sectorPathways[$sector];
            
            return array_merge($pathwayTemplate, [
                'sector' => $sector,
                'annual_reduction_rate' => $pathwayTemplate['annual_reduction_rate'] * $sectorAdjustments['intensity_factor'],
                'interim_reduction' => $pathwayTemplate['interim_reduction'] * $sectorAdjustments['feasibility_factor'],
                'net_zero_year' => $pathwayTemplate['net_zero_year'] + $sectorAdjustments['net_zero_adjustment'],
                'scope_priorities' => $sectorAdjustments['scope_priorities'],
                'key_technologies' => $sectorAdjustments['key_technologies'],
            ]);
        }

        return array_merge($pathwayTemplate, ['sector' => 'general']);
    }

    /**
     * Calculate SBTi-compliant targets
     */
    protected function calculateSBTiTargets(array $baseline, array $pathway, int $baseYear, int $targetYear): array
    {
        $yearsToTarget = $targetYear - $baseYear;
        $annualReduction = $pathway['annual_reduction_rate'];
        
        $targets = [];
        
        // Scope 1 + 2 combined target (SBTi requirement)
        $scope12Baseline = $baseline['scope_1'] + $baseline['scope_2'];
        $scope12Target = $scope12Baseline * (1 - $pathway['interim_reduction']);
        
        $targets['scope_1_2'] = [
            'baseline' => $scope12Baseline,
            'target' => $scope12Target,
            'reduction_percentage' => $pathway['interim_reduction'] * 100,
            'annual_reduction_rate' => $annualReduction * 100,
            'target_type' => 'absolute',
            'scope_coverage' => 'Scope 1 + 2',
        ];

        // Scope 3 target (if significant)
        if ($baseline['scope_3'] > $scope12Baseline * 0.4) { // >40% threshold
            $scope3Target = $baseline['scope_3'] * (1 - $pathway['interim_reduction'] * 0.7); // Less aggressive for Scope 3
            
            $targets['scope_3'] = [
                'baseline' => $baseline['scope_3'],
                'target' => $scope3Target,
                'reduction_percentage' => ($baseline['scope_3'] - $scope3Target) / $baseline['scope_3'] * 100,
                'target_type' => 'absolute',
                'scope_coverage' => 'Scope 3',
                'note' => 'Scope 3 target required due to significance (>40% of total)',
            ];
        }

        // Generate yearly milestones
        foreach ($targets as $scope => &$target) {
            $target['milestones'] = $this->generateYearlyMilestones(
                $target['baseline'],
                $target['target'],
                $baseYear,
                $targetYear,
                'exponential' // SBTi prefers front-loaded reductions
            );
        }

        return $targets;
    }

    /**
     * Generate net-zero roadmap
     */
    protected function generateNetZeroRoadmap(array $baseline, array $pathway, int $baseYear): array
    {
        $netZeroYear = $pathway['net_zero_year'];
        $totalEmissions = $baseline['total'];
        
        // Phase 1: Rapid reduction (to 2030)
        $phase1End = 2030;
        $phase1Reduction = $pathway['interim_reduction'];
        $phase1Emissions = $totalEmissions * (1 - $phase1Reduction);
        
        // Phase 2: Deep decarbonization (2030-2045)
        $phase2End = 2045;
        $phase2Emissions = $totalEmissions * 0.1; // 90% reduction
        
        // Phase 3: Net-zero achievement (2045-2050)
        $phase3End = $netZeroYear;
        $phase3Emissions = 0; // Net zero
        
        return [
            'net_zero_year' => $netZeroYear,
            'total_reduction_required' => 100,
            'phases' => [
                'phase_1' => [
                    'name' => 'Rapid Reduction',
                    'period' => "{$baseYear}-{$phase1End}",
                    'target_emissions' => $phase1Emissions,
                    'reduction_percentage' => $phase1Reduction * 100,
                    'key_actions' => [
                        'Energy efficiency improvements',
                        'Renewable energy procurement',
                        'Process optimization',
                        'Supplier engagement',
                    ],
                ],
                'phase_2' => [
                    'name' => 'Deep Decarbonization',
                    'period' => "{$phase1End}-{$phase2End}",
                    'target_emissions' => $phase2Emissions,
                    'reduction_percentage' => 90,
                    'key_actions' => [
                        'Technology transformation',
                        'Electrification',
                        'Alternative fuels',
                        'Circular economy',
                    ],
                ],
                'phase_3' => [
                    'name' => 'Net-Zero Achievement',
                    'period' => "{$phase2End}-{$phase3End}",
                    'target_emissions' => $phase3Emissions,
                    'reduction_percentage' => 100,
                    'key_actions' => [
                        'Carbon removal technologies',
                        'Nature-based solutions',
                        'Residual emissions offsetting',
                        'Value chain transformation',
                    ],
                ],
            ],
            'investment_requirements' => $this->estimateInvestmentRequirements($totalEmissions, $pathway),
        ];
    }

    /**
     * Validate SBTi compliance
     */
    protected function validateSBTiCompliance(array $targets, array $pathway): array
    {
        $validation = [
            'is_compliant' => true,
            'criteria_met' => [],
            'criteria_failed' => [],
            'recommendations' => [],
        ];

        // Check minimum ambition level
        if (isset($targets['scope_1_2'])) {
            $scope12Reduction = $targets['scope_1_2']['reduction_percentage'];
            
            if ($scope12Reduction >= 42) { // 1.5°C aligned
                $validation['criteria_met'][] = 'Minimum 42% reduction for Scope 1+2 (1.5°C aligned)';
            } elseif ($scope12Reduction >= 25) { // Well below 2°C
                $validation['criteria_met'][] = 'Minimum 25% reduction for Scope 1+2 (well below 2°C)';
                $validation['recommendations'][] = 'Consider increasing ambition to 42% for 1.5°C alignment';
            } else {
                $validation['is_compliant'] = false;
                $validation['criteria_failed'][] = 'Insufficient Scope 1+2 reduction (<25%)';
            }
        }

        // Check Scope 3 requirement
        if (isset($targets['scope_3'])) {
            $validation['criteria_met'][] = 'Scope 3 target included (required for significant emissions)';
        }

        // Check target timeframe
        $validation['criteria_met'][] = 'Target timeframe within 5-15 years (SBTi requirement)';

        // Check absolute vs intensity
        $hasAbsolute = false;
        foreach ($targets as $target) {
            if ($target['target_type'] === 'absolute') {
                $hasAbsolute = true;
                break;
            }
        }

        if ($hasAbsolute) {
            $validation['criteria_met'][] = 'Includes absolute emission reduction targets';
        } else {
            $validation['recommendations'][] = 'Consider adding absolute targets alongside intensity targets';
        }

        return $validation;
    }

    /**
     * Generate yearly milestones
     */
    protected function generateYearlyMilestones(float $baseline, float $target, int $baseYear, int $targetYear, string $trajectory = 'linear'): array
    {
        $milestones = [];
        $totalReduction = $baseline - $target;
        $years = $targetYear - $baseYear;

        for ($year = $baseYear + 1; $year <= $targetYear; $year++) {
            $yearProgress = ($year - $baseYear) / $years;
            
            switch ($trajectory) {
                case 'exponential':
                    // Front-loaded reductions (SBTi preferred)
                    $progress = 1 - pow(1 - $yearProgress, 2);
                    break;
                case 'linear':
                default:
                    $progress = $yearProgress;
                    break;
            }

            $milestones[$year] = $baseline - ($totalReduction * $progress);
        }

        return $milestones;
    }

    /**
     * Assess data quality for baseline
     */
    protected function assessDataQuality(Collection $emissions): array
    {
        $totalRecords = $emissions->count();
        $highQuality = $emissions->where('data_quality_rating', 'High')->count();
        $mediumQuality = $emissions->where('data_quality_rating', 'Medium')->count();
        
        $qualityScore = ($highQuality * 3 + $mediumQuality * 2) / ($totalRecords * 3) * 100;
        
        return [
            'overall_score' => $qualityScore,
            'rating' => $qualityScore >= 80 ? 'High' : ($qualityScore >= 60 ? 'Medium' : 'Low'),
            'total_records' => $totalRecords,
            'high_quality_records' => $highQuality,
            'recommendations' => $qualityScore < 70 ? ['Improve data collection for more accurate baselines'] : [],
        ];
    }

    /**
     * Estimate investment requirements
     */
    protected function estimateInvestmentRequirements(float $totalEmissions, array $pathway): array
    {
        // Rough estimates based on industry benchmarks
        $costPerTonne = [
            'phase_1' => 50,  // $50/tCO2e for early reductions
            'phase_2' => 150, // $150/tCO2e for deep reductions
            'phase_3' => 500, // $500/tCO2e for final reductions
        ];

        $phase1Reduction = $totalEmissions * $pathway['interim_reduction'];
        $phase2Reduction = $totalEmissions * 0.35; // Additional 35%
        $phase3Reduction = $totalEmissions * 0.1;  // Final 10%

        return [
            'phase_1_investment' => $phase1Reduction * $costPerTonne['phase_1'],
            'phase_2_investment' => $phase2Reduction * $costPerTonne['phase_2'],
            'phase_3_investment' => $phase3Reduction * $costPerTonne['phase_3'],
            'total_investment' => ($phase1Reduction * $costPerTonne['phase_1']) + 
                                 ($phase2Reduction * $costPerTonne['phase_2']) + 
                                 ($phase3Reduction * $costPerTonne['phase_3']),
            'currency' => 'USD',
            'note' => 'Estimates based on industry benchmarks and may vary significantly by sector and region',
        ];
    }

    /**
     * Generate recommendations
     */
    protected function generateRecommendations(array $targets, array $validation): array
    {
        $recommendations = [];

        if ($validation['is_compliant']) {
            $recommendations[] = 'Targets are SBTi-compliant and ready for submission';
            $recommendations[] = 'Consider formal SBTi validation for external credibility';
        } else {
            $recommendations[] = 'Increase target ambition to meet SBTi minimum requirements';
        }

        $recommendations[] = 'Develop detailed implementation roadmap with specific initiatives';
        $recommendations[] = 'Establish regular monitoring and reporting processes';
        $recommendations[] = 'Engage suppliers for Scope 3 emission reductions';

        return $recommendations;
    }

    /**
     * Load sector-specific pathways
     */
    protected function loadSectorPathways(): void
    {
        $this->sectorPathways = [
            'manufacturing' => [
                'intensity_factor' => 1.2, // Higher reduction rates needed
                'feasibility_factor' => 0.9, // Slightly lower feasibility
                'net_zero_adjustment' => 5, // 5 years later for net-zero
                'scope_priorities' => ['scope_1', 'scope_2', 'scope_3'],
                'key_technologies' => ['electrification', 'process_efficiency', 'renewable_energy'],
            ],
            'energy' => [
                'intensity_factor' => 1.5,
                'feasibility_factor' => 1.1,
                'net_zero_adjustment' => 0,
                'scope_priorities' => ['scope_1', 'scope_3', 'scope_2'],
                'key_technologies' => ['renewable_generation', 'energy_storage', 'grid_modernization'],
            ],
            'transportation' => [
                'intensity_factor' => 1.3,
                'feasibility_factor' => 0.8,
                'net_zero_adjustment' => 10,
                'scope_priorities' => ['scope_1', 'scope_3', 'scope_2'],
                'key_technologies' => ['electrification', 'alternative_fuels', 'efficiency'],
            ],
            'agriculture' => [
                'intensity_factor' => 0.8,
                'feasibility_factor' => 0.7,
                'net_zero_adjustment' => 15,
                'scope_priorities' => ['scope_1', 'scope_3', 'scope_2'],
                'key_technologies' => ['precision_agriculture', 'soil_carbon', 'methane_reduction'],
            ],
            'services' => [
                'intensity_factor' => 1.0,
                'feasibility_factor' => 1.2,
                'net_zero_adjustment' => -5,
                'scope_priorities' => ['scope_2', 'scope_3', 'scope_1'],
                'key_technologies' => ['renewable_energy', 'efficiency', 'digitalization'],
            ],
        ];
    }

    /**
     * Get supported temperature scenarios
     */
    public function getSupportedScenarios(): array
    {
        return array_keys($this->temperatureScenarios);
    }

    /**
     * Get sector pathways
     */
    public function getSectorPathways(): array
    {
        return $this->sectorPathways;
    }
}
