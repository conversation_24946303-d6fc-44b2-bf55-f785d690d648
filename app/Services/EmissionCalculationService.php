<?php

namespace App\Services;

use App\Models\EmissionFactor;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\EmissionFactorVariant;
use App\Models\ActivityData;
use App\Models\MeasurementUnit;

class EmissionCalculationService
{
    protected EmissionFactorLookupService $lookupService;

    public function __construct(EmissionFactorLookupService $lookupService)
    {
        $this->lookupService = $lookupService;
    }

    /**
     * Legacy calculation method (for backward compatibility)
     */
    public static function calculate($quantity, $emissionFactorId)
    {
        $emissionFactor = EmissionFactor::find($emissionFactorId);

        if (!$emissionFactor) {
            return null;
        }

        return $quantity * $emissionFactor->factor_value;
    }

    /**
     * Enhanced calculation using GHG Protocol factors
     */
    public function calculateWithGhgProtocol(ActivityData $activityData, array $options = []): array
    {
        $criteria = $this->buildCriteriaFromActivityData($activityData, $options);

        // Find the best emission factor
        $factor = $this->lookupService->findBestFactor($criteria);

        if (!$factor) {
            return [
                'success' => false,
                'error' => 'No suitable emission factor found',
                'criteria' => $criteria
            ];
        }

        // Find the best variant if applicable
        $variant = $this->lookupService->findBestVariant($factor, $criteria);

        // Perform the calculation
        $result = $this->performCalculation($activityData, $factor, $variant);

        return [
            'success' => true,
            'emissions' => $result['emissions'],
            'factor_used' => $factor,
            'variant_used' => $variant,
            'calculation_details' => $result['details'],
            'explanation' => $this->lookupService->getSelectionExplanation($factor, $criteria)
        ];
    }

    /**
     * Calculate emissions by gas type (CO2, CH4, N2O)
     */
    public function calculateByGasType(ActivityData $activityData, array $options = []): array
    {
        $results = [];
        $gasTypes = ['CO2', 'CH4', 'N2O'];

        foreach ($gasTypes as $gasType) {
            $criteria = $this->buildCriteriaFromActivityData($activityData, $options);
            $criteria['gas_type'] = $gasType;

            $factor = $this->lookupService->findBestFactor($criteria);

            if ($factor) {
                $variant = $this->lookupService->findBestVariant($factor, $criteria);
                $calculation = $this->performCalculation($activityData, $factor, $variant);

                $results[$gasType] = [
                    'emissions' => $calculation['emissions'],
                    'factor' => $factor,
                    'variant' => $variant,
                    'details' => $calculation['details']
                ];
            }
        }

        return $results;
    }

    /**
     * Build criteria from activity data
     */
    protected function buildCriteriaFromActivityData(ActivityData $activityData, array $options): array
    {
        $criteria = [
            'date' => $activityData->date_recorded,
            'facility_id' => $activityData->facility_id,
            'organization_id' => $activityData->organization_id,
        ];

        // Add source/activity mapping
        if ($activityData->source) {
            $criteria['activity_id'] = $this->mapSourceToActivity($activityData->source);
            $criteria['sector_id'] = $this->mapSourceToSector($activityData->source);
        }

        // Add region from facility
        if ($activityData->facility && $activityData->facility->region) {
            $criteria['region_id'] = $this->mapFacilityToRegion($activityData->facility);
        }

        // Merge with provided options
        return array_merge($criteria, $options);
    }

    /**
     * Perform the actual calculation
     */
    protected function performCalculation(ActivityData $activityData, GhgProtocolEmissionFactor $factor, ?EmissionFactorVariant $variant = null): array
    {
        $effectiveFactor = $variant ? $variant->getEffectiveFactorValue() : $factor->factor_value;
        $inputUnit = $variant ? $variant->getEffectiveInputUnit() : $factor->inputUnit;
        $outputUnit = $variant ? $variant->getEffectiveOutputUnit() : $factor->outputUnit;

        // Convert units if necessary
        $convertedQuantity = $this->convertUnits(
            $activityData->quantity,
            $activityData->unit,
            $inputUnit
        );

        $emissions = $convertedQuantity * $effectiveFactor;

        return [
            'emissions' => $emissions,
            'details' => [
                'original_quantity' => $activityData->quantity,
                'original_unit' => $activityData->unit->name ?? 'Unknown',
                'converted_quantity' => $convertedQuantity,
                'input_unit' => $inputUnit->name ?? 'Unknown',
                'factor_value' => $effectiveFactor,
                'output_unit' => $outputUnit->name ?? 'Unknown',
                'calculation' => "{$convertedQuantity} × {$effectiveFactor} = {$emissions}"
            ]
        ];
    }

    /**
     * Convert units between different measurement systems
     */
    protected function convertUnits($quantity, ?MeasurementUnit $fromUnit, ?MeasurementUnit $toUnit): float
    {
        if (!$fromUnit || !$toUnit || $fromUnit->id === $toUnit->id) {
            return $quantity;
        }

        // This is a simplified conversion - in a real system you'd have
        // a comprehensive unit conversion service
        if ($fromUnit->conversion_factor && $toUnit->conversion_factor) {
            return $quantity * ($fromUnit->conversion_factor / $toUnit->conversion_factor);
        }

        return $quantity; // No conversion available
    }

    /**
     * Map emission source to GHG Protocol activity
     */
    protected function mapSourceToActivity($source): ?int
    {
        // This would contain mapping logic from your existing emission sources
        // to GHG Protocol activities - implementation depends on your data structure
        return null;
    }

    /**
     * Map emission source to GHG Protocol sector
     */
    protected function mapSourceToSector($source): ?int
    {
        // This would contain mapping logic from your existing emission sources
        // to GHG Protocol sectors - implementation depends on your data structure
        return null;
    }

    /**
     * Map facility to GHG Protocol region
     */
    protected function mapFacilityToRegion($facility): ?int
    {
        // This would contain mapping logic from facility location
        // to GHG Protocol regions - implementation depends on your data structure
        return null;
    }
}
