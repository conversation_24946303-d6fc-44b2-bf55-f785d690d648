<?php

namespace App\Services;

use App\Models\ActivityData;
use App\Models\Scope3Category;
use App\Models\Supplier;
use App\Models\GhgProtocolEmissionFactor;
use App\Services\EmissionFactorLookupService;
use App\Services\AdvancedUnitConverter;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class GranularCalculationEngine
{
    protected EmissionFactorLookupService $lookupService;
    protected AdvancedUnitConverter $unitConverter;
    
    protected array $supportedMethodologies = [
        'ghg_protocol' => 'GHG Protocol Corporate Standard',
        'iso_14064' => 'ISO 14064-1:2018',
        'pcaf' => 'Partnership for Carbon Accounting Financials',
        'custom_hybrid' => 'Custom Hybrid Methodology',
    ];

    protected array $scope2Methods = [
        'location_based' => 'Location-based Method',
        'market_based' => 'Market-based Method',
    ];

    public function __construct(
        EmissionFactorLookupService $lookupService,
        AdvancedUnitConverter $unitConverter
    ) {
        $this->lookupService = $lookupService;
        $this->unitConverter = $unitConverter;
    }

    /**
     * Calculate emissions with granular methodology support
     */
    public function calculateEmissions(ActivityData $activityData, array $options = []): array
    {
        $methodology = $options['methodology'] ?? 'ghg_protocol';
        $scope = $activityData->scope ?? '1';

        try {
            switch ($scope) {
                case '1':
                    return $this->calculateScope1Emissions($activityData, $options);
                case '2':
                    return $this->calculateScope2Emissions($activityData, $options);
                case '3':
                    return $this->calculateScope3Emissions($activityData, $options);
                default:
                    throw new \InvalidArgumentException("Invalid scope: {$scope}");
            }
        } catch (\Exception $e) {
            Log::error('Granular calculation failed', [
                'activity_data_id' => $activityData->id,
                'scope' => $scope,
                'methodology' => $methodology,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'scope' => $scope,
                'methodology' => $methodology,
            ];
        }
    }

    /**
     * Calculate Scope 1 emissions (Direct emissions)
     */
    protected function calculateScope1Emissions(ActivityData $activityData, array $options): array
    {
        $emissionType = $options['emission_type'] ?? 'combustion';
        
        switch ($emissionType) {
            case 'combustion':
                return $this->calculateCombustionEmissions($activityData, $options);
            case 'fugitive':
                return $this->calculateFugitiveEmissions($activityData, $options);
            case 'process':
                return $this->calculateProcessEmissions($activityData, $options);
            default:
                return $this->calculateCombustionEmissions($activityData, $options);
        }
    }

    /**
     * Calculate Scope 2 emissions (Indirect energy emissions)
     */
    protected function calculateScope2Emissions(ActivityData $activityData, array $options): array
    {
        $method = $options['scope2_method'] ?? 'location_based';
        
        switch ($method) {
            case 'location_based':
                return $this->calculateLocationBasedEmissions($activityData, $options);
            case 'market_based':
                return $this->calculateMarketBasedEmissions($activityData, $options);
            case 'dual_reporting':
                return $this->calculateDualReportingEmissions($activityData, $options);
            default:
                return $this->calculateLocationBasedEmissions($activityData, $options);
        }
    }

    /**
     * Calculate Scope 3 emissions (Value chain emissions)
     */
    protected function calculateScope3Emissions(ActivityData $activityData, array $options): array
    {
        $categoryId = $options['scope3_category_id'] ?? $activityData->scope3_category_id;
        
        if (!$categoryId) {
            return [
                'success' => false,
                'error' => 'Scope 3 category not specified',
                'scope' => '3',
            ];
        }

        $category = Scope3Category::find($categoryId);
        if (!$category) {
            return [
                'success' => false,
                'error' => "Scope 3 category not found: {$categoryId}",
                'scope' => '3',
            ];
        }

        return $category->calculateEmissions($activityData->toArray(), $options);
    }

    /**
     * Calculate combustion emissions
     */
    protected function calculateCombustionEmissions(ActivityData $activityData, array $options): array
    {
        $methodology = $options['methodology'] ?? 'ghg_protocol';
        $tier = $options['tier'] ?? 1;

        // Find appropriate emission factors
        $criteria = [
            'activity_id' => $activityData->ghg_protocol_activity_id,
            'sector_id' => $activityData->ghg_protocol_sector_id,
            'region_id' => $options['region_id'] ?? null,
            'tier_level' => $tier,
            'date' => $activityData->date_recorded,
        ];

        $results = [];
        $gasTypes = ['CO2', 'CH4', 'N2O'];

        foreach ($gasTypes as $gasType) {
            $criteria['gas_type'] = $gasType;
            $factor = $this->lookupService->findBestFactor($criteria);

            if ($factor) {
                $emissions = $this->performCalculation($activityData, $factor, $options);
                $results[$gasType] = $emissions;
            }
        }

        // Calculate total CO2e
        $totalEmissions = $this->calculateCO2Equivalent($results, $options);

        return [
            'success' => true,
            'scope' => '1',
            'emission_type' => 'combustion',
            'methodology' => $methodology,
            'tier' => $tier,
            'by_gas' => $results,
            'total_co2e' => $totalEmissions,
            'calculation_details' => $this->buildCalculationDetails($results, $totalEmissions),
        ];
    }

    /**
     * Calculate fugitive emissions
     */
    protected function calculateFugitiveEmissions(ActivityData $activityData, array $options): array
    {
        // Fugitive emissions calculation (e.g., refrigerant leaks, natural gas leaks)
        $fugitiveType = $options['fugitive_type'] ?? 'refrigerant';
        
        switch ($fugitiveType) {
            case 'refrigerant':
                return $this->calculateRefrigerantEmissions($activityData, $options);
            case 'natural_gas_leak':
                return $this->calculateNaturalGasLeaks($activityData, $options);
            default:
                return $this->calculateGenericFugitiveEmissions($activityData, $options);
        }
    }

    /**
     * Calculate location-based Scope 2 emissions
     */
    protected function calculateLocationBasedEmissions(ActivityData $activityData, array $options): array
    {
        // Use grid average emission factors
        $region = $options['region_id'] ?? $activityData->facility->region_id ?? null;
        
        $criteria = [
            'activity_type' => 'electricity_consumption',
            'region_id' => $region,
            'gas_type' => 'CO2',
            'date' => $activityData->date_recorded,
        ];

        $factor = $this->lookupService->findBestFactor($criteria);
        
        if (!$factor) {
            return [
                'success' => false,
                'error' => 'No location-based emission factor found',
                'scope' => '2',
                'method' => 'location_based',
            ];
        }

        $emissions = $this->performCalculation($activityData, $factor, $options);

        return [
            'success' => true,
            'scope' => '2',
            'method' => 'location_based',
            'emissions' => $emissions['emissions'],
            'factor_used' => $factor,
            'calculation_details' => $emissions['details'],
        ];
    }

    /**
     * Calculate market-based Scope 2 emissions
     */
    protected function calculateMarketBasedEmissions(ActivityData $activityData, array $options): array
    {
        // Use supplier-specific or contractual emission factors
        $contractualFactor = $options['contractual_factor'] ?? null;
        $supplierFactor = $options['supplier_factor'] ?? null;

        if ($contractualFactor) {
            // Use contractual factor (e.g., renewable energy certificates)
            $emissions = $activityData->quantity * $contractualFactor;
            
            return [
                'success' => true,
                'scope' => '2',
                'method' => 'market_based',
                'emissions' => $emissions,
                'factor_type' => 'contractual',
                'factor_value' => $contractualFactor,
            ];
        }

        if ($supplierFactor) {
            // Use supplier-specific factor
            $emissions = $activityData->quantity * $supplierFactor;
            
            return [
                'success' => true,
                'scope' => '2',
                'method' => 'market_based',
                'emissions' => $emissions,
                'factor_type' => 'supplier_specific',
                'factor_value' => $supplierFactor,
            ];
        }

        // Fall back to residual mix or location-based
        return $this->calculateLocationBasedEmissions($activityData, array_merge($options, [
            'fallback_reason' => 'No market-based factors available',
        ]));
    }

    /**
     * Calculate dual reporting (both location and market-based)
     */
    protected function calculateDualReportingEmissions(ActivityData $activityData, array $options): array
    {
        $locationBased = $this->calculateLocationBasedEmissions($activityData, $options);
        $marketBased = $this->calculateMarketBasedEmissions($activityData, $options);

        return [
            'success' => true,
            'scope' => '2',
            'method' => 'dual_reporting',
            'location_based' => $locationBased,
            'market_based' => $marketBased,
            'reporting_emissions' => $marketBased['emissions'] ?? $locationBased['emissions'],
        ];
    }

    /**
     * Perform individual calculation
     */
    protected function performCalculation(ActivityData $activityData, $factor, array $options): array
    {
        $quantity = $activityData->quantity;
        $factorValue = $factor->factor_value;

        // Apply unit conversion if needed
        if ($activityData->activity_unit_id !== $factor->input_unit_id) {
            $quantity = $this->unitConverter->convertWithContext(
                $quantity,
                $activityData->activityUnit->symbol,
                $factor->inputUnit->symbol,
                $options['conversion_context'] ?? []
            );
        }

        $emissions = $quantity * $factorValue;

        return [
            'emissions' => $emissions,
            'details' => [
                'original_quantity' => $activityData->quantity,
                'converted_quantity' => $quantity,
                'factor_value' => $factorValue,
                'calculation' => "{$quantity} × {$factorValue} = {$emissions}",
                'input_unit' => $factor->inputUnit->symbol,
                'output_unit' => $factor->outputUnit->symbol,
            ],
        ];
    }

    /**
     * Calculate CO2 equivalent using GWP values
     */
    protected function calculateCO2Equivalent(array $gasResults, array $options): float
    {
        $gwpValues = $options['gwp_values'] ?? [
            'CO2' => 1,
            'CH4' => 25,  // 100-year GWP (AR4)
            'N2O' => 298, // 100-year GWP (AR4)
        ];

        $totalCO2e = 0;

        foreach ($gasResults as $gas => $result) {
            if (isset($result['emissions']) && isset($gwpValues[$gas])) {
                $totalCO2e += $result['emissions'] * $gwpValues[$gas];
            }
        }

        return $totalCO2e;
    }

    /**
     * Calculate refrigerant emissions
     */
    protected function calculateRefrigerantEmissions(ActivityData $activityData, array $options): array
    {
        $refrigerantType = $options['refrigerant_type'] ?? 'R-410A';
        $leakageRate = $options['leakage_rate'] ?? 0.05; // 5% default
        
        // Get GWP for refrigerant
        $gwpValues = [
            'R-410A' => 2088,
            'R-134a' => 1430,
            'R-22' => 1810,
            'R-404A' => 3922,
        ];

        $gwp = $gwpValues[$refrigerantType] ?? 1;
        $refrigerantAmount = $activityData->quantity * $leakageRate;
        $emissions = $refrigerantAmount * $gwp / 1000; // Convert to tCO2e

        return [
            'success' => true,
            'scope' => '1',
            'emission_type' => 'fugitive',
            'fugitive_type' => 'refrigerant',
            'emissions' => $emissions,
            'refrigerant_type' => $refrigerantType,
            'gwp' => $gwp,
            'leakage_rate' => $leakageRate,
            'calculation_details' => [
                'total_refrigerant' => $activityData->quantity,
                'leaked_amount' => $refrigerantAmount,
                'gwp_factor' => $gwp,
                'calculation' => "{$refrigerantAmount} kg × {$gwp} GWP ÷ 1000 = {$emissions} tCO2e",
            ],
        ];
    }

    /**
     * Calculate natural gas leak emissions
     */
    protected function calculateNaturalGasLeaks(ActivityData $activityData, array $options): array
    {
        $leakageRate = $options['leakage_rate'] ?? 0.02; // 2% default
        $methaneContent = $options['methane_content'] ?? 0.95; // 95% CH4
        $gwpCH4 = $options['gwp_ch4'] ?? 25;

        $leakedGas = $activityData->quantity * $leakageRate;
        $methaneEmissions = $leakedGas * $methaneContent;
        $co2eEmissions = $methaneEmissions * $gwpCH4 / 1000; // Convert to tCO2e

        return [
            'success' => true,
            'scope' => '1',
            'emission_type' => 'fugitive',
            'fugitive_type' => 'natural_gas_leak',
            'emissions' => $co2eEmissions,
            'leakage_rate' => $leakageRate,
            'methane_content' => $methaneContent,
            'calculation_details' => [
                'total_gas_consumed' => $activityData->quantity,
                'leaked_gas' => $leakedGas,
                'methane_emissions' => $methaneEmissions,
                'gwp_factor' => $gwpCH4,
                'calculation' => "{$leakedGas} × {$methaneContent} × {$gwpCH4} ÷ 1000 = {$co2eEmissions} tCO2e",
            ],
        ];
    }

    /**
     * Calculate generic fugitive emissions
     */
    protected function calculateGenericFugitiveEmissions(ActivityData $activityData, array $options): array
    {
        // Generic fugitive calculation using emission factors
        return $this->calculateCombustionEmissions($activityData, array_merge($options, [
            'emission_type' => 'fugitive',
        ]));
    }

    /**
     * Calculate process emissions
     */
    protected function calculateProcessEmissions(ActivityData $activityData, array $options): array
    {
        // Industrial process emissions (e.g., cement production, steel making)
        return $this->calculateCombustionEmissions($activityData, array_merge($options, [
            'emission_type' => 'process',
        ]));
    }

    /**
     * Build detailed calculation explanation
     */
    protected function buildCalculationDetails(array $gasResults, float $totalEmissions): array
    {
        $details = [
            'total_co2e' => $totalEmissions,
            'by_gas' => [],
            'methodology' => 'Multi-gas calculation with GWP factors',
        ];

        foreach ($gasResults as $gas => $result) {
            if (isset($result['details'])) {
                $details['by_gas'][$gas] = $result['details'];
            }
        }

        return $details;
    }

    /**
     * Get supported methodologies
     */
    public function getSupportedMethodologies(): array
    {
        return $this->supportedMethodologies;
    }

    /**
     * Get Scope 2 calculation methods
     */
    public function getScope2Methods(): array
    {
        return $this->scope2Methods;
    }

    /**
     * Validate calculation options
     */
    public function validateCalculationOptions(array $options): array
    {
        $errors = [];

        if (isset($options['methodology']) && !array_key_exists($options['methodology'], $this->supportedMethodologies)) {
            $errors[] = "Unsupported methodology: {$options['methodology']}";
        }

        if (isset($options['scope2_method']) && !array_key_exists($options['scope2_method'], $this->scope2Methods)) {
            $errors[] = "Unsupported Scope 2 method: {$options['scope2_method']}";
        }

        if (isset($options['tier']) && !in_array($options['tier'], [1, 2, 3])) {
            $errors[] = "Invalid tier level: {$options['tier']}";
        }

        return $errors;
    }
}
