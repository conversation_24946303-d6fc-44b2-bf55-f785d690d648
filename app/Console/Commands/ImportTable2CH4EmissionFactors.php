<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\MeasurementUnit;
use App\Models\GreenhouseGas;
use Illuminate\Support\Facades\DB;

class ImportTable2CH4EmissionFactors extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:table2-ch4-emission-factors';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Table 2: CH4 Emission Factors by Fuel';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of Table 2: CH4 Emission Factors by Fuel...');
        
        // Table 2 CH4 fuel data: [Fuel, Lower_Heating_Value, Primary_basis, Fuel_density, Liquid_basis, Gas_basis]
        $fuel_data = [
            // Oil products
            ['Crude oil', 42.3, 10, 0.42, null, 0.00030],
            ['Orimulsion', 27.5, 10, 0.28, null, null],
            ['Natural Gas Liquids', 44.2, 10, 0.44, null, null],
            ['Motor gasoline', 44.3, 10, 0.44, 0.33, 0.00033],
            ['Aviation gasoline', 44.3, 10, 0.44, 0.33, 0.00033],
            ['Jet gasoline', 43.8, 10, 0.44, 0.33, 0.00033],
            ['Jet kerosene', 43.8, 10, 0.44, 0.33, 0.00033],
            ['Other kerosene', 43.8, 10, 0.44, 0.33, 0.00033],
            ['Shale oil', 38.1, 10, 0.38, null, null],
            ['Gas/Diesel oil', 43.0, 10, 0.43, 0.33, 0.00033],
            ['Residual fuel oil', 40.4, 10, 0.40, 0.33, null],
            ['Liquefied Petroleum Gases', 47.3, 5, 0.24, 0.18, 0.00018],
            ['Ethane', 46.4, 5, 0.23, null, null],
            ['Naphtha', 44.5, 10, 0.45, 0.33, 0.00033],
            ['Bitumen', 40.2, 10, 0.40, null, null],
            ['Lubricants', 40.2, 10, 0.40, 0.30, 0.00030],
            ['Petroleum coke', 32.5, 10, 0.33, null, null],
            ['Refinery feedstocks', 43, 10, 0.43, null, null],
            ['Refinery gas', 49.5, 5, 0.25, null, null],
            ['Paraffin waxes', 40.2, 10, 0.40, null, null],
            ['White spirit/SBP', 40.2, 10, 0.40, null, null],
            ['Other petroleum products', 40.2, 10, 0.40, null, null],
            
            // Coal products
            ['Anthracite', 26.7, 10, 0.27, null, null],
            ['Coking coal', 28.2, 10, 0.28, null, null],
            ['Other bituminous coal', 25.8, 10, 0.26, null, null],
            ['Sub-bituminous coal', 18.9, 10, 0.19, null, null],
            ['Lignite', 11.9, 10, 0.12, null, null],
            ['Oil shale and tar sands', 8.9, 10, 0.09, null, null],
            ['Brown coal briquettes', 20.7, 10, 0.21, null, null],
            ['Patent fuel', 20.7, 10, 0.21, null, null],
            ['Coke oven coke', 28.2, 10, 0.28, null, null],
            ['Lignite coke', 28.2, 5, 0.14, null, null],
            ['Gas coke', 28.2, 5, 0.14, null, null],
            ['Coal tar', 28.2, 10, 0.28, null, null],
            ['Gas works gas', 38.7, 5, 0.19, null, null],
            ['Coke oven gas', 38.7, 5, 0.19, null, null],
            ['Blast furnace gas', 2.47, 2, 0.05, null, null],
            ['Oxygen steel furnace gas', 7.06, 2, 0.14, null, null],
            
            // Natural gas
            ['Natural gas', 48, 5, 0.24, null, 0.00150],
            ['Municipal waste (Non biomass fraction)', null, 300, null, null, null],
            
            // Other wastes
            ['Industrial waste', null, 300, null, null, null],
            ['Waste oils', 40.2, 300, 12.06, null, null],
            ['Wood/Wood waste', 15.6, 300, 4.68, null, null],
            ['Sulphite lyes (black liquor)', 11.2, 300, 3.36, null, null],
            ['Other primary solid biomass fuels', 11.6, 300, 3.48, null, null],
            ['Charcoal', 29.5, 200, 5.90, null, null],
            ['Biogasoline', 27, 10, 0.27, null, null],
            ['Biodiesels', 27, 10, 0.27, null, null],
            ['Other liquid biofuels', 27.4, 10, 0.27, null, null],
            ['Landfill gas', 50.4, 1, 0.05, null, null],
            ['Sludge gas', 50.4, 5, 0.25, null, null],
            ['Other biogas', 50.4, 5, 0.25, null, null],
            ['Municipal waste (Biomass fraction)', 11.6, 300, 3.48, null, null],
            ['Peat', null, 300, null, null, null]
        ];

        try {
            DB::beginTransaction();
            
            // Get fuel combustion sector
            $sector = GhgProtocolSector::where('name', 'fuel_combustion')->first();
            if (!$sector) {
                $this->error('Fuel combustion sector not found. Please run Table 1 import first.');
                return 1;
            }
            
            // Get CH4 gas
            $ch4_gas = GreenhouseGas::where('chemical_formula', 'CH4')->first();
            if (!$ch4_gas) {
                $this->error('CH4 gas record not found. Please ensure greenhouse gases are seeded.');
                return 1;
            }
            
            // Get or create measurement units
            $units = [
                'kg_ch4_gj' => MeasurementUnit::firstOrCreate([
                    'name' => 'kg CH4/GJ'
                ], [
                    'symbol' => 'kg CH4/GJ',
                    'description' => 'Kilograms CH4 per Gigajoule',
                    'unit_type' => 'emission_factor'
                ]),
                'kg_ch4_tonne' => MeasurementUnit::firstOrCreate([
                    'name' => 'kg CH4/tonne'
                ], [
                    'symbol' => 'kg CH4/t',
                    'description' => 'Kilograms CH4 per tonne',
                    'unit_type' => 'emission_factor'
                ]),
                'kg_ch4_litre' => MeasurementUnit::firstOrCreate([
                    'name' => 'kg CH4/litre'
                ], [
                    'symbol' => 'kg CH4/L',
                    'description' => 'Kilograms CH4 per litre',
                    'unit_type' => 'emission_factor'
                ]),
                'kg_ch4_m3' => MeasurementUnit::firstOrCreate([
                    'name' => 'kg CH4/m³'
                ], [
                    'symbol' => 'kg CH4/m³',
                    'description' => 'Kilograms CH4 per cubic metre',
                    'unit_type' => 'emission_factor'
                ])
            ];
            
            // Get global region
            $global_region = GhgProtocolRegion::where('code', 'GLOBAL')->first();
            if (!$global_region) {
                $this->error('Global region not found.');
                return 1;
            }
            
            $imported_count = 0;
            
            foreach ($fuel_data as $row) {
                [$fuel_name, $lower_heating_value, $primary_basis, $fuel_density, $liquid_basis, $gas_basis] = $row;
                
                // Find existing activity for this fuel
                $activity_name = "Fuel Combustion - {$fuel_name}";
                $activity = GhgProtocolActivity::where('name', $activity_name)
                    ->where('sector_id', $sector->id)
                    ->first();
                
                if (!$activity) {
                    $this->warn("Activity not found for fuel: {$fuel_name}. Skipping...");
                    continue;
                }
                
                // Create CH4 emission factors for different measurement bases
                
                // Primary basis (kg CH4/GJ)
                if ($primary_basis !== null) {
                    $factor_value = $primary_basis / 1000; // Convert from g/GJ to kg/GJ
                    
                    GhgProtocolEmissionFactor::create([
                        'code' => "FUEL_CH4_{$activity->code}_PRIMARY",
                        'name' => "Primary CH4 Factor - {$fuel_name}",
                        'description' => "Primary CH4 emission factor for {$fuel_name} (energy basis)",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $global_region->id,
                        'gas_id' => $ch4_gas->id,
                        'calculation_method' => 'Energy content basis',
                        'methodology_reference' => 'GHG Protocol Fuel Combustion Tool',
                        'factor_value' => $factor_value,
                        'ch4_factor' => $factor_value,
                        'input_unit_id' => $units['kg_ch4_gj']->id,
                        'output_unit_id' => $units['kg_ch4_gj']->id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 2. CH4 Emission Factors by Fuel',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'measurement_basis' => 'energy_content',
                            'lower_heating_value' => $lower_heating_value,
                            'table_source' => 'Table 2 - Fuel CH4 Factors',
                            'calculation_basis' => 'primary_energy',
                            'original_value_g_gj' => $primary_basis
                        ],
                        'is_default' => true,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 500
                    ]);
                    $imported_count++;
                }
                
                // Fuel density basis (kg CH4/tonne)
                if ($fuel_density !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "FUEL_CH4_{$activity->code}_MASS",
                        'name' => "Mass CH4 Factor - {$fuel_name}",
                        'description' => "Mass-based CH4 emission factor for {$fuel_name}",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $global_region->id,
                        'gas_id' => $ch4_gas->id,
                        'calculation_method' => 'Mass basis',
                        'methodology_reference' => 'GHG Protocol Fuel Combustion Tool',
                        'factor_value' => $fuel_density,
                        'ch4_factor' => $fuel_density,
                        'input_unit_id' => $units['kg_ch4_tonne']->id,
                        'output_unit_id' => $units['kg_ch4_tonne']->id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 2. CH4 Emission Factors by Fuel',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'measurement_basis' => 'mass',
                            'lower_heating_value' => $lower_heating_value,
                            'table_source' => 'Table 2 - Fuel CH4 Factors',
                            'calculation_basis' => 'fuel_mass'
                        ],
                        'is_default' => false,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 400
                    ]);
                    $imported_count++;
                }
                
                // Liquid basis (kg CH4/litre)
                if ($liquid_basis !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "FUEL_CH4_{$activity->code}_LIQUID",
                        'name' => "Liquid CH4 Factor - {$fuel_name}",
                        'description' => "Volume-based CH4 emission factor for {$fuel_name} (liquid)",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $global_region->id,
                        'gas_id' => $ch4_gas->id,
                        'calculation_method' => 'Volume basis (liquid)',
                        'methodology_reference' => 'GHG Protocol Fuel Combustion Tool',
                        'factor_value' => $liquid_basis / 1000, // Convert from g/L to kg/L
                        'ch4_factor' => $liquid_basis / 1000,
                        'input_unit_id' => $units['kg_ch4_litre']->id,
                        'output_unit_id' => $units['kg_ch4_litre']->id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 2. CH4 Emission Factors by Fuel',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'measurement_basis' => 'volume_liquid',
                            'lower_heating_value' => $lower_heating_value,
                            'table_source' => 'Table 2 - Fuel CH4 Factors',
                            'calculation_basis' => 'fuel_volume_liquid',
                            'original_value_g_l' => $liquid_basis
                        ],
                        'is_default' => false,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 300
                    ]);
                    $imported_count++;
                }
                
                // Gas basis (kg CH4/m³)
                if ($gas_basis !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "FUEL_CH4_{$activity->code}_GAS",
                        'name' => "Gas CH4 Factor - {$fuel_name}",
                        'description' => "Volume-based CH4 emission factor for {$fuel_name} (gas)",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $global_region->id,
                        'gas_id' => $ch4_gas->id,
                        'calculation_method' => 'Volume basis (gas)',
                        'methodology_reference' => 'GHG Protocol Fuel Combustion Tool',
                        'factor_value' => $gas_basis,
                        'ch4_factor' => $gas_basis,
                        'input_unit_id' => $units['kg_ch4_m3']->id,
                        'output_unit_id' => $units['kg_ch4_m3']->id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 2. CH4 Emission Factors by Fuel',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'measurement_basis' => 'volume_gas',
                            'lower_heating_value' => $lower_heating_value,
                            'table_source' => 'Table 2 - Fuel CH4 Factors',
                            'calculation_basis' => 'fuel_volume_gas'
                        ],
                        'is_default' => false,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 300
                    ]);
                    $imported_count++;
                }
                
                $this->info("Imported CH4 factors for: {$fuel_name}");
            }
            
            DB::commit();
            
            $this->info("\nSuccessfully imported {$imported_count} fuel CH4 emission factors from Table 2.");
            
        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Error importing data: " . $e->getMessage());
            return 1;
        }

        $this->info("\nImport completed successfully!");
        $this->info("Table 2 CH4 emission factors complement the CO2 factors from Table 1:");
        $this->info("- Methane emissions for all major fuel types");
        $this->info("- Multiple measurement bases: Energy (GJ), Mass (tonne), Volume (L/m³)");
        $this->info("- Higher factors for biomass and waste fuels");
        $this->info("- Essential for complete GHG calculations including CH4 impacts");
        return 0;
    }
}
