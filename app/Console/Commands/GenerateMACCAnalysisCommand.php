<?php

namespace App\Console\Commands;

use App\Models\Organization;
use App\Services\MarginalAbatementCostService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GenerateMACCAnalysisCommand extends Command
{
    protected $signature = 'targets:generate-macc 
                            {organization : Organization ID to generate MACC for}
                            {--target-reduction=0.5 : Target reduction percentage (0.0-1.0)}
                            {--timeframe=10 : Analysis timeframe in years}
                            {--currency=USD : Currency for cost analysis}
                            {--save : Save MACC analysis to database}
                            {--dry-run : Run without saving analysis}';

    protected $description = 'Generate Marginal Abatement Cost Curve (MACC) analysis';

    protected MarginalAbatementCostService $maccService;

    public function __construct(MarginalAbatementCostService $maccService)
    {
        parent::__construct();
        $this->maccService = $maccService;
    }

    public function handle(): int
    {
        $this->info('📊 Starting MACC analysis generation...');

        $organizationId = $this->argument('organization');
        $targetReduction = (float) $this->option('target-reduction');
        $timeframe = (int) $this->option('timeframe');
        $currency = $this->option('currency');
        $save = $this->option('save');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No analysis will be saved');
        }

        try {
            $organization = Organization::find($organizationId);
            
            if (!$organization) {
                $this->error("❌ Organization not found: {$organizationId}");
                return 1;
            }

            $this->line("  📍 Organization: {$organization->name}");
            $this->line("  🎯 Target Reduction: " . ($targetReduction * 100) . "%");
            $this->line("  📅 Timeframe: {$timeframe} years");
            $this->line("  💰 Currency: {$currency}");

            if ($dryRun) {
                $this->warn("  🧪 Dry run - would generate MACC analysis");
                return 0;
            }

            $options = [
                'target_reduction' => $targetReduction,
                'timeframe' => $timeframe,
                'currency' => $currency,
            ];

            $analysis = $this->maccService->generateMACCurve($organization, $options);

            if (!$analysis['success']) {
                $this->error("❌ MACC analysis failed: {$analysis['error']}");
                return 1;
            }

            $this->displayAnalysis($analysis);

            if ($save) {
                $savedAnalysis = $this->saveMACCAnalysis($organization, $analysis, $options);
                $this->info("💾 MACC analysis saved with ID: {$savedAnalysis->id}");
            }

            $this->info('✅ MACC analysis completed successfully');
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ MACC analysis failed: {$e->getMessage()}");
            Log::error('MACC analysis command failed', [
                'organization_id' => $organizationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }

    protected function displayAnalysis(array $analysis): void
    {
        $this->info('📈 MACC Analysis Results:');
        
        // Baseline information
        $baseline = $analysis['baseline'];
        $this->line("  📊 Baseline Emissions: " . number_format($baseline['total_emissions'], 2) . " tCO2e");
        $this->line("    Scope 1: " . number_format($baseline['scope_1'], 2) . " tCO2e");
        $this->line("    Scope 2: " . number_format($baseline['scope_2'], 2) . " tCO2e");
        $this->line("    Scope 3: " . number_format($baseline['scope_3'], 2) . " tCO2e");
        $this->line('');

        // Target information
        $targetReduction = $analysis['target_reduction'];
        $targetEmissions = $baseline['total_emissions'] * (1 - $targetReduction);
        $this->line("  🎯 Target Reduction: " . ($targetReduction * 100) . "%");
        $this->line("  🎯 Target Emissions: " . number_format($targetEmissions, 2) . " tCO2e");
        $this->line('');

        // Optimal portfolio
        $portfolio = $analysis['optimal_portfolio'];
        $this->info('  💼 Optimal Abatement Portfolio:');
        $this->line("    Total Investment: " . $analysis['currency'] . " " . number_format($portfolio['total_cost'], 0));
        $this->line("    Total Reduction: " . number_format($portfolio['total_reduction'], 2) . " tCO2e");
        $this->line("    Average Cost: " . $analysis['currency'] . " " . number_format($portfolio['average_cost_per_tonne'], 2) . "/tCO2e");
        $this->line("    Target Achieved: " . ($portfolio['target_achieved'] ? 'Yes' : 'No'));
        $this->line('');

        // Top abatement options
        $this->info('  🔝 Top Abatement Options:');
        foreach (array_slice($portfolio['options'], 0, 10) as $option) {
            $costColor = $option['cost_per_tonne'] < 0 ? 'green' : ($option['cost_per_tonne'] < 100 ? 'yellow' : 'red');
            $this->line("    {$option['implementation_priority']}. {$option['name']}");
            $this->line("       Reduction: " . number_format($option['reduction_used'], 2) . " tCO2e");
            $this->line("       Cost: " . $analysis['currency'] . " " . number_format($option['cost_per_tonne'], 2) . "/tCO2e");
            $this->line("       Category: {$option['category']}");
            $this->line('');
        }

        // Summary statistics
        $summary = $analysis['summary'];
        $this->info('  📊 Summary Statistics:');
        $this->line("    Number of Initiatives: {$summary['number_of_initiatives']}");
        $this->line("    Cost Effectiveness: {$summary['cost_effectiveness_rating']}");
        $this->line("    Reduction Achieved: " . number_format($summary['reduction_percentage'], 1) . "%");
        $this->line('');

        // Implementation timeline
        if (isset($summary['implementation_timeline'])) {
            $timeline = $summary['implementation_timeline'];
            $this->info('  📅 Implementation Timeline:');
            
            if (!empty($timeline['immediate'])) {
                $this->line("    Immediate (0-12 months): " . count($timeline['immediate']) . " initiatives");
            }
            if (!empty($timeline['short_term'])) {
                $this->line("    Short-term (1-3 years): " . count($timeline['short_term']) . " initiatives");
            }
            if (!empty($timeline['medium_term'])) {
                $this->line("    Medium-term (3-7 years): " . count($timeline['medium_term']) . " initiatives");
            }
            if (!empty($timeline['long_term'])) {
                $this->line("    Long-term (7+ years): " . count($timeline['long_term']) . " initiatives");
            }
        }

        // Cost breakdown by category
        $this->displayCostBreakdown($portfolio['options'], $analysis['currency']);

        // Recommendations
        $this->info('  💡 Recommendations:');
        foreach (array_slice($analysis['abatement_options'], 0, 3) as $option) {
            if ($option['cost_per_tonne'] < 0) {
                $this->line("    ✅ Prioritize {$option['name']} - generates savings");
            } elseif ($option['cost_per_tonne'] < 50) {
                $this->line("    ⭐ Consider {$option['name']} - highly cost-effective");
            }
        }
    }

    protected function displayCostBreakdown(array $options, string $currency): void
    {
        $this->info('  💰 Cost Breakdown by Category:');
        
        $categoryTotals = [];
        foreach ($options as $option) {
            $category = $option['category'];
            if (!isset($categoryTotals[$category])) {
                $categoryTotals[$category] = [
                    'cost' => 0,
                    'reduction' => 0,
                    'count' => 0,
                ];
            }
            $categoryTotals[$category]['cost'] += $option['cost'];
            $categoryTotals[$category]['reduction'] += $option['reduction_used'];
            $categoryTotals[$category]['count']++;
        }

        foreach ($categoryTotals as $category => $totals) {
            $avgCost = $totals['reduction'] > 0 ? $totals['cost'] / $totals['reduction'] : 0;
            $this->line("    {$category}:");
            $this->line("      Investment: {$currency} " . number_format($totals['cost'], 0));
            $this->line("      Reduction: " . number_format($totals['reduction'], 2) . " tCO2e");
            $this->line("      Avg Cost: {$currency} " . number_format($avgCost, 2) . "/tCO2e");
            $this->line("      Initiatives: {$totals['count']}");
            $this->line('');
        }
    }

    protected function saveMACCAnalysis(Organization $organization, array $analysis, array $options): \App\Models\MaccAnalysis
    {
        $analysisData = [
            'organization_id' => $organization->id,
            'analysis_name' => "MACC Analysis - " . now()->format('Y-m-d'),
            'description' => "Marginal Abatement Cost Curve analysis for {$analysis['target_reduction']}% reduction target",
            'target_reduction_percentage' => $analysis['target_reduction'] * 100,
            'timeframe_years' => $analysis['timeframe'],
            'currency' => $analysis['currency'],
            'baseline_emissions' => $analysis['baseline']['total_emissions'],
            'curve_data' => $analysis['curve_data'],
            'optimal_portfolio' => $analysis['optimal_portfolio'],
            'total_investment_required' => $analysis['optimal_portfolio']['total_cost'],
            'average_cost_per_tonne' => $analysis['optimal_portfolio']['average_cost_per_tonne'],
            'analysis_date' => now()->toDateString(),
            'status' => 'draft',
        ];

        $maccAnalysis = \App\Models\MaccAnalysis::create($analysisData);

        // Save option selections
        foreach ($analysis['optimal_portfolio']['options'] as $option) {
            \App\Models\MaccOptionSelection::create([
                'macc_analysis_id' => $maccAnalysis->id,
                'abatement_option_id' => $this->findOrCreateAbatementOption($option),
                'reduction_used' => $option['reduction_used'],
                'cost_allocated' => $option['cost'],
                'cost_per_tonne' => $option['cost_per_tonne'],
                'implementation_priority' => $option['implementation_priority'],
                'implementation_status' => 'planned',
            ]);
        }

        return $maccAnalysis;
    }

    protected function findOrCreateAbatementOption(array $optionData): int
    {
        // Try to find existing option by name
        $existingOption = \App\Models\AbatementOption::where('name', $optionData['name'])->first();
        
        if ($existingOption) {
            return $existingOption->id;
        }

        // Create new option if not found
        $newOption = \App\Models\AbatementOption::create([
            'option_code' => strtoupper(str_replace(' ', '_', $optionData['name'])),
            'name' => $optionData['name'],
            'description' => 'Auto-generated from MACC analysis',
            'category' => $optionData['category'],
            'applicable_sectors' => ['all'],
            'applicable_scopes' => [1, 2, 3],
            'capital_cost_per_tonne' => 0, // Would need to be calculated
            'operational_cost_per_tonne' => 0,
            'implementation_complexity' => 5,
            'implementation_time' => 12,
            'max_reduction_percentage' => 0.1,
            'is_active' => true,
        ]);

        return $newOption->id;
    }
}
