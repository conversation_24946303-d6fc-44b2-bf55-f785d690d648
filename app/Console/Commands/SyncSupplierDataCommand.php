<?php

namespace App\Console\Commands;

use App\Services\SupplyChainService;
use App\Services\DataLakeService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncSupplierDataCommand extends Command
{
    protected $signature = 'data:sync-suppliers 
                            {--supplier= : Specific supplier ID to sync}
                            {--portal= : Portal type to sync (cdp, ecovadis, supplier_portal)}
                            {--validate-only : Only validate data without importing}
                            {--dry-run : Run without making changes}';

    protected $description = 'Sync Scope 3 data from supplier portals';

    protected SupplyChainService $supplyChainService;
    protected DataLakeService $dataLakeService;

    public function __construct(SupplyChainService $supplyChainService, DataLakeService $dataLakeService)
    {
        parent::__construct();
        $this->supplyChainService = $supplyChainService;
        $this->dataLakeService = $dataLakeService;
    }

    public function handle(): int
    {
        $this->info('🔗 Starting supplier data synchronization...');

        $supplierId = $this->option('supplier');
        $portalType = $this->option('portal');
        $validateOnly = $this->option('validate-only');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        if ($validateOnly) {
            $this->info('✅ VALIDATION ONLY MODE - Data will not be imported');
        }

        try {
            if ($supplierId) {
                $this->info("📊 Syncing data for supplier: {$supplierId}");
                $results = $this->syncSpecificSupplier($supplierId, $validateOnly, $dryRun);
            } else {
                $this->info("📊 Syncing data from all connected suppliers");
                $results = $this->syncAllSuppliers($portalType, $validateOnly, $dryRun);
            }

            $this->displayResults($results);

            $this->info('✅ Supplier data synchronization completed successfully');
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Supplier sync failed: {$e->getMessage()}");
            Log::error('Supplier sync command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }

    protected function syncSpecificSupplier(string $supplierId, bool $validateOnly, bool $dryRun): array
    {
        if ($dryRun) {
            return [
                'supplier_id' => $supplierId,
                'dry_run' => true,
                'message' => 'Dry run - would sync supplier data'
            ];
        }

        // This would need to be implemented to sync a specific supplier
        // For now, return a placeholder
        return [
            'supplier_id' => $supplierId,
            'status' => 'not_implemented',
            'message' => 'Specific supplier sync not yet implemented'
        ];
    }

    protected function syncAllSuppliers(?string $portalType, bool $validateOnly, bool $dryRun): array
    {
        if ($dryRun) {
            return [
                'portal_type' => $portalType,
                'dry_run' => true,
                'message' => 'Dry run - would sync all supplier data'
            ];
        }

        $results = $this->supplyChainService->syncSupplierData();
        
        $summary = [
            'total_suppliers' => count($results),
            'successful_syncs' => 0,
            'failed_syncs' => 0,
            'total_records' => 0,
            'validation_errors' => 0,
            'suppliers' => [],
        ];

        foreach ($results as $supplierId => $result) {
            if ($result['status'] === 'success') {
                $summary['successful_syncs']++;
                $summary['total_records'] += $result['records_synced'];
                
                if (!$validateOnly) {
                    // Validate the data
                    $validation = $this->supplyChainService->validateScope3Data($result['data']);
                    
                    if (!$validation['valid']) {
                        $summary['validation_errors'] += count($validation['errors']);
                        $this->warn("  ⚠️ Validation errors for supplier {$supplierId}:");
                        foreach ($validation['errors'] as $error) {
                            $this->line("    - {$error}");
                        }
                    } else {
                        // Import the data
                        $importResult = $this->supplyChainService->importScope3Data(
                            $validation['processed_data'], 
                            $supplierId
                        );
                        
                        $result['imported'] = $importResult['total_imported'];
                        $result['import_errors'] = $importResult['total_errors'];
                    }
                }
            } else {
                $summary['failed_syncs']++;
                $this->warn("  ⚠️ Failed to sync supplier {$supplierId}: {$result['error']}");
            }
            
            $summary['suppliers'][] = [
                'supplier_id' => $supplierId,
                'result' => $result,
            ];
        }

        return $summary;
    }

    protected function displayResults(array $results): void
    {
        $this->info('📈 Synchronization Results:');
        
        if (isset($results['dry_run']) && $results['dry_run']) {
            $this->warn("  🧪 {$results['message']}");
            return;
        }

        if (isset($results['supplier_id'])) {
            // Single supplier results
            $this->line("  📍 Supplier: {$results['supplier_id']}");
            $this->line("  📊 Status: {$results['status']}");
            
            if (isset($results['message'])) {
                $this->line("  📋 Message: {$results['message']}");
            }
        } else {
            // Multiple suppliers results
            $this->line("  📊 Total Suppliers: {$results['total_suppliers']}");
            $this->line("  ✅ Successful Syncs: {$results['successful_syncs']}");
            $this->line("  ❌ Failed Syncs: {$results['failed_syncs']}");
            $this->line("  📋 Total Records: {$results['total_records']}");
            
            if (isset($results['validation_errors'])) {
                $this->line("  ⚠️ Validation Errors: {$results['validation_errors']}");
            }

            if (!empty($results['suppliers'])) {
                $this->info('  📋 Supplier Details:');
                foreach (array_slice($results['suppliers'], 0, 5) as $supplier) {
                    $supplierId = $supplier['supplier_id'];
                    $result = $supplier['result'];
                    
                    if ($result['status'] === 'success') {
                        $records = $result['records_synced'] ?? 0;
                        $imported = $result['imported'] ?? 'N/A';
                        $this->line("    ✅ {$supplierId}: {$records} records synced, {$imported} imported");
                    } else {
                        $this->line("    ❌ {$supplierId}: {$result['error']}");
                    }
                }
                
                if (count($results['suppliers']) > 5) {
                    $remaining = count($results['suppliers']) - 5;
                    $this->line("    ... and {$remaining} more suppliers");
                }
            }
        }

        // Show data lake statistics
        $stats = $this->dataLakeService->getStatistics();
        $this->info('  🏗️ Data Lake Stats:');
        $this->line("    Supplier Records: " . ($stats['by_source']['supplier'] ?? 0));
        $this->line("    Total Size: " . number_format($stats['total_size_mb'], 2) . " MB");
        $this->line("    Recent Activity (30 days): {$stats['recent_activity']}");
    }
}
