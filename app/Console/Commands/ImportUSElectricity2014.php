<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\MeasurementUnit;
use Illuminate\Support\Facades\DB;

class ImportUSElectricity2014 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:us-electricity-2014';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import 2014 US Regional Electricity Emission Factors from EPA eGRID';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of 2014 US Regional Electricity Emission Factors...');

        // 2014 US Regional Electricity Emission Factors Data
        $data_2014 = [
            ['ASCC Alaska Grid', 926.50, 46.60, 7.20],
            ['ASCC Miscellaneous', 680.50, 36.10, 6.00],
            ['ERCOT All', 1142.80, 81.80, 11.60],
            ['FRCC All', 1075.20, 87.80, 12.10],
            ['HICC Miscellaneous', 940.80, 95.30, 15.20],
            ['HICC Oahu', 1479.40, 159.40, 24.50],
            ['MRO East', 1663.80, 191.20, 28.20],
            ['MRO West', 1365.10, 161.40, 23.30],
            ['NPCC Long Island', 1196.20, 132.40, 17.20],
            ['NPCC New England', 570.90, 96.00, 12.80],
            ['NPCC NYC/Westchester', 665.50, 24.40, 3.00],
            ['NPCC Upstate NY', 365.70, 30.70, 4.10],
            ['RFC East', 829.40, 73.90, 11.20],
            ['RFC Michigan', 1531.50, 170.10, 24.50],
            ['RFC West', 1380.90, 150.20, 22.00],
            ['SERC Midwest', 1772.00, 208.60, 30.40],
            ['SERC Mississippi Valley', 1022.00, 78.60, 11.20],
            ['SERC South', 1143.80, 103.70, 15.30],
            ['SERC Tennessee Valley', 1336.30, 138.60, 20.20],
            ['SERC Virginia/Carolina', 856.60, 95.70, 13.80],
            ['SPP North', 1575.00, 173.80, 25.20],
            ['SPP South', 1475.90, 135.40, 19.70],
            ['WECC California', 568.60, 33.10, 4.00],
            ['WECC Northwest', 907.00, 97.80, 14.20],
            ['WECC Rockies', 1737.70, 178.20, 25.80],
            ['WECC Southwest', 875.60, 66.40, 9.30]
        ];

        try {
            DB::beginTransaction();

            // Get or create the electricity sector
            $sector = GhgProtocolSector::firstOrCreate([
                'name' => 'Electricity'
            ], [
                'code' => 'ELEC',
                'description' => 'Electricity generation and consumption',
                'scope' => 'Scope 2'
            ]);

            // Get or create the electricity activity
            $activity = GhgProtocolActivity::firstOrCreate([
                'name' => 'Electricity Consumption',
                'sector_id' => $sector->id
            ], [
                'code' => 'ELEC-CONS',
                'description' => 'Electricity consumption from grid',
                'scope' => 'Scope 2'
            ]);

            // Get or create US region
            $region = GhgProtocolRegion::firstOrCreate([
                'name' => 'United States'
            ], [
                'code' => 'US',
                'description' => 'United States of America'
            ]);

            // Get lb/MWh unit
            $unit = MeasurementUnit::firstOrCreate([
                'name' => 'lb/MWh'
            ], [
                'symbol' => 'lb/MWh',
                'description' => 'Pounds per Megawatt hour',
                'unit_type' => 'emission_factor'
            ]);

            $imported_count = 0;

            foreach ($data_2014 as $row) {
                [$region_name, $co2_rate, $ch4_rate, $n2o_rate] = $row;

                // Create emission factor record
                $emission_factor = GhgProtocolEmissionFactor::create([
                    'code' => "US-ELEC-{$region_name}-2014",
                    'name' => "US Electricity - {$region_name} (2014)",
                    'description' => "2014 US Regional Electricity Emission Factors for {$region_name}",
                    'sector_id' => $sector->id,
                    'activity_id' => $activity->id,
                    'region_id' => $region->id,
                    'gas_id' => 1, // Assuming CO2 is gas_id 1, this is a multi-gas factor
                    'calculation_method' => 'eGRID subregion annual output emission rates',
                    'methodology_reference' => 'EPA eGRID 2014',
                    'factor_value' => $co2_rate, // Primary factor is CO2
                    'co2_factor' => $co2_rate,
                    'ch4_factor' => $ch4_rate,
                    'n2o_factor' => $n2o_rate,
                    'input_unit_id' => $unit->id,
                    'output_unit_id' => $unit->id,
                    'data_quality_rating' => 'High',
                    'vintage_year' => 2014,
                    'valid_from' => '2014-01-01',
                    'valid_until' => '2014-12-31',
                    'data_source' => 'EPA eGRID',
                    'data_source_detail' => 'EPA eGRID 2014 - Year 2014 US Regional Electricity Emission Factors for CO2, CH4 and N2O',
                    'metadata' => [
                        'egrid_subregion' => $region_name,
                        'table_source' => 'Table 7. Year 2014 US Regional Electricity Emission Factors for CO2, CH4 and N2O'
                    ],
                    'is_default' => false,
                    'is_active' => true,
                    'tier_level' => 2,
                    'priority' => 100
                ]);

                $imported_count++;
                $this->info("Imported: {$region_name}");
            }

            DB::commit();

            $this->info("\nSuccessfully imported {$imported_count} emission factors for 2014 US Regional Electricity.");
            $this->info("Total records created:");
            $this->info("- Emission factors: {$imported_count}");
            $this->info("- Emission factor values: " . ($imported_count * 3) . " (CO2, CH4, N2O for each region)");
            $this->info("- Metadata records: " . ($imported_count * 2));

        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Error importing data: " . $e->getMessage());
            return 1;
        }

        $this->info("\nImport completed successfully!");
        return 0;
    }
}
