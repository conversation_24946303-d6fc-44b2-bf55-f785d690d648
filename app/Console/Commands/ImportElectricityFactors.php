<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ElectricityFactorsImportService;
use App\Services\GhgProtocolExcelImportService;
use App\Services\GhgProtocolDataValidator;

class ImportElectricityFactors extends Command
{
    protected $signature = 'ghg:import-electricity {file? : Excel file path} {--test : Run with test data} {--validate-only : Only validate data without importing}';
    protected $description = 'Import electricity emission factors from GHG Protocol Excel files or test data';

    public function handle()
    {
        $this->info('Importing Electricity Emission Factors...');

        $filePath = $this->argument('file');
        $useTestData = $this->option('test');
        $validateOnly = $this->option('validate-only');

        if ($filePath) {
            return $this->handleExcelImport($filePath, $validateOnly);
        } elseif ($useTestData) {
            return $this->handleTestDataImport($validateOnly);
        } else {
            $this->error('Please provide an Excel file path or use --test option');
            $this->line('Usage: php artisan ghg:import-electricity [file.xlsx] [--test] [--validate-only]');
            return 1;
        }
    }

    /**
     * Handle Excel file import
     */
    protected function handleExcelImport(string $filePath, bool $validateOnly): int
    {
        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $this->info("Processing Excel file: {$filePath}");

        $excelImporter = new GhgProtocolExcelImportService();
        $result = $excelImporter->importFromExcel($filePath, ['validate_only' => $validateOnly]);

        if ($result['success']) {
            $this->info($validateOnly ? 'Validation completed successfully!' : 'Import completed successfully!');
            $this->displayValidationResults($result['validation'] ?? []);
            $this->displayStats($result['stats']);

            if (isset($result['data_preview'])) {
                $this->displayDataPreview($result['data_preview']);
            }
        } else {
            $this->error($validateOnly ? 'Validation failed: ' . $result['error'] : 'Import failed: ' . $result['error']);

            if (isset($result['validation'])) {
                $this->displayValidationResults($result['validation']);
            }

            $this->displayStats($result['stats']);
            return 1;
        }

        return 0;
    }

    /**
     * Handle test data import
     */
    protected function handleTestDataImport(bool $validateOnly): int
    {
        $this->info('Using test electricity data...');

        $electricityData = $this->getTestElectricityData();

        if ($validateOnly) {
            $validator = new GhgProtocolDataValidator();
            $validation = $validator->validateElectricityData($electricityData);

            $this->displayValidationResults($validation);

            if ($validation['is_valid']) {
                $this->info('Test data validation passed!');
                return 0;
            } else {
                $this->error('Test data validation failed!');
                return 1;
            }
        }

        $importService = new ElectricityFactorsImportService();
        $result = $importService->importElectricityFactors($electricityData);

        if ($result['success']) {
            $this->info('Import completed successfully!');
            $this->displayStats($result['stats']);
        } else {
            $this->error('Import failed: ' . $result['error']);
            $this->displayStats($result['stats']);
            return 1;
        }

        return 0;
    }

    /**
     * Get test electricity data based on your screenshots
     */
    protected function getTestElectricityData(): array
    {
        return array_merge($this->getInternationalElectricityData(), $this->getUSElectricityData());
    }

    /**
     * Get international electricity data (previous screenshots)
     */
    protected function getInternationalElectricityData(): array
    {
        return [
            // Table 1: Chinese Electricity National Average
            [
                'region' => 'China',
                'table_name' => 'Table 1. Chinese Electricity National Average',
                'notes' => 'Original source does not provide EFs for non-CO₂ gases.',
                'data' => [
                    [
                        'year' => 2022,
                        'emission_factor' => 0.5703,
                        'unit' => 'tCO₂/MWh'
                    ]
                ]
            ],

            // Table 2: Electricity Emission Factors for Taiwan
            [
                'region' => 'Taiwan',
                'table_name' => 'Table 2. Electricity Emission Factors for Taiwan',
                'notes' => 'The original source for the above EFs for Taiwan does not indicate which GWP was used to convert gases to CO₂e. Note that the above values are given in CO₂e and are not disaggregated by gas.',
                'data' => [
                    ['year' => 2005, 'emission_factor' => 0.555, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2006, 'emission_factor' => 0.562, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2007, 'emission_factor' => 0.558, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2008, 'emission_factor' => 0.555, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2009, 'emission_factor' => 0.543, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2010, 'emission_factor' => 0.534, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2011, 'emission_factor' => 0.534, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2012, 'emission_factor' => 0.529, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2013, 'emission_factor' => 0.519, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2014, 'emission_factor' => 0.518, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2015, 'emission_factor' => 0.525, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2016, 'emission_factor' => 0.530, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2017, 'emission_factor' => 0.554, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2018, 'emission_factor' => 0.533, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2019, 'emission_factor' => 0.509, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2020, 'emission_factor' => 0.502, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2021, 'emission_factor' => 0.509, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2022, 'emission_factor' => 0.495, 'unit' => 'kgCO₂e/kWh'],
                ]
            ],

            // Table 3: Electricity Emission Factors for Brazil
            [
                'region' => 'Brazil',
                'table_name' => 'Table 3. Electricity Emission Factors for Brazil',
                'notes' => 'Pre-2016 EFs are not provided because Amazon\'s State had an independent grid system until its connection to the national grid in May 2015. EFs for the isolated Amazon grid pre-2016 are available at: https://caesp.fgv.br/centros/centro-estudos-sustentabilidade/projetos/programa-brasileiro-ghg-protocol. Original source did not provide EFs for non-CO₂ gases.',
                'data' => [
                    ['year' => 2016, 'emission_factor' => 0.0817, 'unit' => 'tCO₂/MWh'],
                    ['year' => 2017, 'emission_factor' => 0.0927, 'unit' => 'tCO₂/MWh'],
                    ['year' => 2018, 'emission_factor' => 0.0740, 'unit' => 'tCO₂/MWh'],
                    ['year' => 2019, 'emission_factor' => 0.0750, 'unit' => 'tCO₂/MWh'],
                    ['year' => 2020, 'emission_factor' => 0.0617, 'unit' => 'tCO₂/MWh'],
                    ['year' => 2021, 'emission_factor' => 0.1264, 'unit' => 'tCO₂/MWh'],
                    ['year' => 2022, 'emission_factor' => 0.0426, 'unit' => 'tCO₂/MWh'],
                    ['year' => 2023, 'emission_factor' => 0.0385, 'unit' => 'tCO₂/MWh'],
                ]
            ],

            // Table 4: Electricity Emission Factors for Thailand
            [
                'region' => 'Thailand',
                'table_name' => 'Table 4. Electricity Emission Factors for Thailand',
                'notes' => 'Source publishes EFs for generation and consumption. The above values are generation factors, not consumption. Consumption factors published by the source include transmission and distribution (T&D) emissions, which shouldn\'t be included in scope 2 estimations. Original source did not provide EFs for non-CO₂ gases.',
                'data' => [
                    ['year' => 2011, 'emission_factor' => 0.530, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2012, 'emission_factor' => 0.530, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2013, 'emission_factor' => 0.532, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2014, 'emission_factor' => 0.532, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2015, 'emission_factor' => 0.507, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2016, 'emission_factor' => 0.493, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2017, 'emission_factor' => 0.471, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2018, 'emission_factor' => 0.459, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2019, 'emission_factor' => 0.445, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2020, 'emission_factor' => 0.442, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2021, 'emission_factor' => 0.433, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2022, 'emission_factor' => 0.412, 'unit' => 'CO₂ (kg/kWh)'],
                ]
            ],

            // Table 5: Electricity Emission Factors for U.K.
            [
                'region' => 'U.K.',
                'table_name' => 'Table 5. Electricity Emission Factors for U.K.',
                'notes' => 'UK DEFRA only presented values in CO₂e, using GWP-100 from the IPCC\'s Fifth Assessment Report (AR5). CH₄ and N₂O values presented in this table were reverse calculated using the same GWP values in order to be provided per individual GHG.',
                'data' => [
                    [
                        'year' => 2023,
                        'co2_factor' => 0.20496,
                        'ch4_factor' => 0.000032,
                        'n2o_factor' => 0.0000046,
                        'unit' => 'kg/kWh'
                    ]
                ]
            ],
        ];
    }

    /**
     * Get US regional electricity data (from new screenshots)
     */
    protected function getUSElectricityData(): array
    {
        return [
            // Table 1: Year 2022 US Regional Electricity Emission Factors
            [
                'region' => 'United States',
                'table_name' => 'Table 1. Year 2022 US Regional Electricity Emission Factors for CO₂, CH₄, and N₂O',
                'notes' => 'United States electricity grid average emission output rates per year, per subregion. These emission factors can be used to estimate emissions from electricity purchased from the grid (location-based approach for scope 2 emissions). For technical reference see EPA Technical Guide.',
                'data' => [
                    // Sample of key regions - full data would include all 25+ subregions
                    ['year' => 2022, 'subregion' => 'ASCC Alaska Grid', 'co2_factor' => 1052.10, 'ch4_factor' => 88.0, 'n2o_factor' => 12.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'ASCC Miscellaneous', 'co2_factor' => 495.80, 'ch4_factor' => 23.0, 'n2o_factor' => 4.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'WECC Southwest', 'co2_factor' => 776.00, 'ch4_factor' => 51.0, 'n2o_factor' => 7.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'WECC California', 'co2_factor' => 497.44, 'ch4_factor' => 30.0, 'n2o_factor' => 4.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'ERCOT All', 'co2_factor' => 771.08, 'ch4_factor' => 49.0, 'n2o_factor' => 7.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'FRCC All', 'co2_factor' => 813.85, 'ch4_factor' => 48.0, 'n2o_factor' => 6.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'HICC Miscellaneous', 'co2_factor' => 1155.49, 'ch4_factor' => 124.0, 'n2o_factor' => 19.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'HICC Oahu', 'co2_factor' => 1575.41, 'ch4_factor' => 163.0, 'n2o_factor' => 25.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'MRO East', 'co2_factor' => 1479.62, 'ch4_factor' => 133.0, 'n2o_factor' => 19.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'MRO West', 'co2_factor' => 936.49, 'ch4_factor' => 102.0, 'n2o_factor' => 15.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'NPCC New England', 'co2_factor' => 536.43, 'ch4_factor' => 63.0, 'n2o_factor' => 8.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'WECC Northwest', 'co2_factor' => 602.09, 'ch4_factor' => 56.0, 'n2o_factor' => 8.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'NPCC NYC/Westchester', 'co2_factor' => 885.23, 'ch4_factor' => 23.0, 'n2o_factor' => 3.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'NPCC Long Island', 'co2_factor' => 1200.71, 'ch4_factor' => 135.0, 'n2o_factor' => 18.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'NPCC Upstate NY', 'co2_factor' => 274.56, 'ch4_factor' => 15.0, 'n2o_factor' => 2.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'Puerto Rico Miscellaneous', 'co2_factor' => 1593.48, 'ch4_factor' => 87.0, 'n2o_factor' => 14.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'RFC East', 'co2_factor' => 657.39, 'ch4_factor' => 45.0, 'n2o_factor' => 6.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'RFC Michigan', 'co2_factor' => 1216.40, 'ch4_factor' => 116.0, 'n2o_factor' => 16.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'RFC West', 'co2_factor' => 1000.05, 'ch4_factor' => 87.0, 'n2o_factor' => 12.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'WECC Rockies', 'co2_factor' => 1124.89, 'ch4_factor' => 101.0, 'n2o_factor' => 14.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'SPP North', 'co2_factor' => 952.58, 'ch4_factor' => 100.0, 'n2o_factor' => 14.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'SPP South', 'co2_factor' => 970.40, 'ch4_factor' => 72.0, 'n2o_factor' => 10.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'SERC Mississippi Valley', 'co2_factor' => 801.02, 'ch4_factor' => 40.0, 'n2o_factor' => 6.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'SERC Midwest', 'co2_factor' => 1369.89, 'ch4_factor' => 151.0, 'n2o_factor' => 22.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'SERC South', 'co2_factor' => 893.29, 'ch4_factor' => 64.0, 'n2o_factor' => 9.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'SERC Tennessee Valley', 'co2_factor' => 933.07, 'ch4_factor' => 82.0, 'n2o_factor' => 12.00, 'unit' => 'lb/MWh'],
                    ['year' => 2022, 'subregion' => 'SERC Virginia/Carolina', 'co2_factor' => 622.99, 'ch4_factor' => 47.0, 'n2o_factor' => 7.00, 'unit' => 'lb/MWh'],
                ]
            ],

            // Table 2: Year 2021 US Regional Electricity Emission Factors
            [
                'region' => 'United States',
                'table_name' => 'Table 2. Year 2021 US Regional Electricity Emission Factors for CO₂, CH₄, and N₂O',
                'notes' => 'United States electricity grid average emission output rates per year, per subregion.',
                'data' => [
                    // Sample of key regions for 2021
                    ['year' => 2021, 'subregion' => 'ASCC Alaska Grid', 'co2_factor' => 1067.68, 'ch4_factor' => 91.0, 'n2o_factor' => 12.00, 'unit' => 'lb/MWh'],
                    ['year' => 2021, 'subregion' => 'WECC California', 'co2_factor' => 531.68, 'ch4_factor' => 31.0, 'n2o_factor' => 4.00, 'unit' => 'lb/MWh'],
                    ['year' => 2021, 'subregion' => 'ERCOT All', 'co2_factor' => 813.55, 'ch4_factor' => 54.0, 'n2o_factor' => 8.00, 'unit' => 'lb/MWh'],
                    ['year' => 2021, 'subregion' => 'NPCC Upstate NY', 'co2_factor' => 233.08, 'ch4_factor' => 15.0, 'n2o_factor' => 2.00, 'unit' => 'lb/MWh'],
                    ['year' => 2021, 'subregion' => 'SERC Midwest', 'co2_factor' => 1543.03, 'ch4_factor' => 171.0, 'n2o_factor' => 25.00, 'unit' => 'lb/MWh'],
                ]
            ],

            // Table 3: Year 2020 US Regional Electricity Emission Factors
            [
                'region' => 'United States',
                'table_name' => 'Table 3. Year 2020 US Regional Electricity Emission Factors for CO₂, CH₄, and N₂O',
                'notes' => 'United States electricity grid average emission output rates per year, per subregion.',
                'data' => [
                    // Sample of key regions for 2020
                    ['year' => 2020, 'subregion' => 'ASCC Alaska Grid', 'co2_factor' => 1097.63, 'ch4_factor' => 100.0, 'n2o_factor' => 14.00, 'unit' => 'lb/MWh'],
                    ['year' => 2020, 'subregion' => 'WECC California', 'co2_factor' => 513.46, 'ch4_factor' => 32.0, 'n2o_factor' => 4.00, 'unit' => 'lb/MWh'],
                    ['year' => 2020, 'subregion' => 'ERCOT All', 'co2_factor' => 818.60, 'ch4_factor' => 52.0, 'n2o_factor' => 7.00, 'unit' => 'lb/MWh'],
                    ['year' => 2020, 'subregion' => 'NPCC Upstate NY', 'co2_factor' => 233.51, 'ch4_factor' => 16.0, 'n2o_factor' => 2.00, 'unit' => 'lb/MWh'],
                    ['year' => 2020, 'subregion' => 'SERC Midwest', 'co2_factor' => 1480.70, 'ch4_factor' => 156.0, 'n2o_factor' => 23.00, 'unit' => 'lb/MWh'],
                ]
            ],

            // Table 4: Year 2019 US Regional Electricity Emission Factors
            [
                'region' => 'United States',
                'table_name' => 'Table 4. Year 2019 US Regional Electricity Emission Factors for CO₂, CH₄, and N₂O',
                'notes' => 'United States electricity grid average emission output rates per year, per subregion.',
                'data' => [
                    // Sample of key regions for 2019
                    ['year' => 2019, 'subregion' => 'ASCC Alaska Grid', 'co2_factor' => 1114.40, 'ch4_factor' => 98.0, 'n2o_factor' => 13.00, 'unit' => 'lb/MWh'],
                    ['year' => 2019, 'subregion' => 'WECC California', 'co2_factor' => 453.21, 'ch4_factor' => 33.0, 'n2o_factor' => 4.00, 'unit' => 'lb/MWh'],
                    ['year' => 2019, 'subregion' => 'ERCOT All', 'co2_factor' => 868.64, 'ch4_factor' => 57.0, 'n2o_factor' => 8.00, 'unit' => 'lb/MWh'],
                    ['year' => 2019, 'subregion' => 'NPCC Upstate NY', 'co2_factor' => 232.31, 'ch4_factor' => 17.0, 'n2o_factor' => 2.00, 'unit' => 'lb/MWh'],
                    ['year' => 2019, 'subregion' => 'SERC Midwest', 'co2_factor' => 1584.39, 'ch4_factor' => 169.0, 'n2o_factor' => 25.00, 'unit' => 'lb/MWh'],
                ]
            ],

            // Table 5: Year 2018 US Regional Electricity Emission Factors
            [
                'region' => 'United States',
                'table_name' => 'Table 5. Year 2018 US Regional Electricity Emission Factors for CO₂, CH₄, and N₂O',
                'notes' => 'United States electricity grid average emission output rates per year, per subregion.',
                'data' => [
                    // Complete 2018 data from screenshot
                    ['year' => 2018, 'subregion' => 'ASCC Alaska Grid', 'co2_factor' => 1039.64, 'ch4_factor' => 82.0, 'n2o_factor' => 11.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'ASCC Miscellaneous', 'co2_factor' => 525.08, 'ch4_factor' => 24.0, 'n2o_factor' => 4.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'WECC Southwest', 'co2_factor' => 1022.36, 'ch4_factor' => 77.0, 'n2o_factor' => 11.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'WECC California', 'co2_factor' => 496.54, 'ch4_factor' => 34.0, 'n2o_factor' => 4.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'ERCOT All', 'co2_factor' => 931.67, 'ch4_factor' => 66.0, 'n2o_factor' => 9.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'FRCC All', 'co2_factor' => 931.84, 'ch4_factor' => 66.0, 'n2o_factor' => 9.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'HICC Miscellaneous', 'co2_factor' => 1110.69, 'ch4_factor' => 118.0, 'n2o_factor' => 18.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'HICC Oahu', 'co2_factor' => 1669.94, 'ch4_factor' => 180.0, 'n2o_factor' => 27.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'MRO East', 'co2_factor' => 1678.02, 'ch4_factor' => 169.0, 'n2o_factor' => 25.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'MRO West', 'co2_factor' => 1239.85, 'ch4_factor' => 138.0, 'n2o_factor' => 20.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'NPCC New England', 'co2_factor' => 522.31, 'ch4_factor' => 82.0, 'n2o_factor' => 11.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'WECC Northwest', 'co2_factor' => 639.04, 'ch4_factor' => 64.0, 'n2o_factor' => 9.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'NPCC NYC/Westchester', 'co2_factor' => 596.41, 'ch4_factor' => 22.0, 'n2o_factor' => 3.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'NPCC Long Island', 'co2_factor' => 1184.24, 'ch4_factor' => 139.0, 'n2o_factor' => 18.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'NPCC Upstate NY', 'co2_factor' => 253.11, 'ch4_factor' => 18.0, 'n2o_factor' => 2.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'RFC East', 'co2_factor' => 715.97, 'ch4_factor' => 61.0, 'n2o_factor' => 8.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'RFC Michigan', 'co2_factor' => 1312.56, 'ch4_factor' => 129.0, 'n2o_factor' => 18.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'RFC West', 'co2_factor' => 1166.10, 'ch4_factor' => 117.0, 'n2o_factor' => 17.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'WECC Rockies', 'co2_factor' => 1273.62, 'ch4_factor' => 123.0, 'n2o_factor' => 18.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'SPP North', 'co2_factor' => 1163.19, 'ch4_factor' => 124.0, 'n2o_factor' => 18.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'SPP South', 'co2_factor' => 1166.58, 'ch4_factor' => 91.0, 'n2o_factor' => 13.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'SERC Mississippi Valley', 'co2_factor' => 854.65, 'ch4_factor' => 55.0, 'n2o_factor' => 8.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'SERC Midwest', 'co2_factor' => 1664.15, 'ch4_factor' => 185.0, 'n2o_factor' => 27.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'SERC South', 'co2_factor' => 1027.93, 'ch4_factor' => 81.0, 'n2o_factor' => 12.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'SERC Tennessee Valley', 'co2_factor' => 1031.54, 'ch4_factor' => 97.0, 'n2o_factor' => 14.00, 'unit' => 'lb/MWh'],
                    ['year' => 2018, 'subregion' => 'SERC Virginia/Carolina', 'co2_factor' => 743.33, 'ch4_factor' => 67.0, 'n2o_factor' => 9.00, 'unit' => 'lb/MWh'],
                ]
            ],

            // Table 6: Year 2016 US Regional Electricity Emission Factors
            [
                'region' => 'United States',
                'table_name' => 'Table 6. Year 2016 US Regional Electricity Emission Factors for CO₂, CH₄, and N₂O',
                'notes' => 'United States electricity grid average emission output rates per year, per subregion.',
                'data' => [
                    // Complete 2016 data from screenshot
                    ['year' => 2016, 'subregion' => 'ASCC Alaska Grid', 'co2_factor' => 1072.28, 'ch4_factor' => 77.0, 'n2o_factor' => 11.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'ASCC Miscellaneous', 'co2_factor' => 503.13, 'ch4_factor' => 23.0, 'n2o_factor' => 4.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'WECC Southwest', 'co2_factor' => 1043.65, 'ch4_factor' => 79.0, 'n2o_factor' => 12.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'WECC California', 'co2_factor' => 527.86, 'ch4_factor' => 33.0, 'n2o_factor' => 4.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'ERCOT All', 'co2_factor' => 1009.16, 'ch4_factor' => 76.0, 'n2o_factor' => 11.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'FRCC All', 'co2_factor' => 1011.68, 'ch4_factor' => 75.0, 'n2o_factor' => 10.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'HICC Miscellaneous', 'co2_factor' => 1152.02, 'ch4_factor' => 95.0, 'n2o_factor' => 15.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'HICC Oahu', 'co2_factor' => 1662.89, 'ch4_factor' => 181.0, 'n2o_factor' => 28.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'MRO East', 'co2_factor' => 1668.21, 'ch4_factor' => 156.0, 'n2o_factor' => 26.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'MRO West', 'co2_factor' => 1238.82, 'ch4_factor' => 115.0, 'n2o_factor' => 20.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'NPCC New England', 'co2_factor' => 558.16, 'ch4_factor' => 90.0, 'n2o_factor' => 12.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'WECC Northwest', 'co2_factor' => 651.20, 'ch4_factor' => 61.0, 'n2o_factor' => 9.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'NPCC NYC/Westchester', 'co2_factor' => 635.81, 'ch4_factor' => 22.0, 'n2o_factor' => 3.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'NPCC Long Island', 'co2_factor' => 1178.32, 'ch4_factor' => 126.0, 'n2o_factor' => 16.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'NPCC Upstate NY', 'co2_factor' => 294.66, 'ch4_factor' => 21.0, 'n2o_factor' => 3.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'RFC East', 'co2_factor' => 758.18, 'ch4_factor' => 50.0, 'n2o_factor' => 9.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'RFC Michigan', 'co2_factor' => 1272.05, 'ch4_factor' => 67.0, 'n2o_factor' => 18.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'RFC West', 'co2_factor' => 1243.44, 'ch4_factor' => 108.0, 'n2o_factor' => 19.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'WECC Rockies', 'co2_factor' => 1367.77, 'ch4_factor' => 137.0, 'n2o_factor' => 20.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'SPP North', 'co2_factor' => 1412.35, 'ch4_factor' => 149.0, 'n2o_factor' => 22.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'SPP South', 'co2_factor' => 1248.33, 'ch4_factor' => 95.0, 'n2o_factor' => 15.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'SERC Mississippi Valley', 'co2_factor' => 838.89, 'ch4_factor' => 50.0, 'n2o_factor' => 7.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'SERC Midwest', 'co2_factor' => 1612.59, 'ch4_factor' => 82.0, 'n2o_factor' => 26.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'SERC South', 'co2_factor' => 1089.37, 'ch4_factor' => 87.0, 'n2o_factor' => 13.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'SERC Tennessee Valley', 'co2_factor' => 1185.43, 'ch4_factor' => 93.0, 'n2o_factor' => 17.00, 'unit' => 'lb/MWh'],
                    ['year' => 2016, 'subregion' => 'SERC Virginia/Carolina', 'co2_factor' => 805.29, 'ch4_factor' => 67.0, 'n2o_factor' => 11.00, 'unit' => 'lb/MWh'],
                ]
            ],
        ];
    }

    /**
     * Display validation results
     */
    protected function displayValidationResults(array $validation): void
    {
        if (empty($validation)) return;

        $this->info("\n=== Validation Results ===");

        if (isset($validation['is_valid'])) {
            $status = $validation['is_valid'] ? '✅ PASSED' : '❌ FAILED';
            $this->line("Status: {$status}");
        }

        if (!empty($validation['errors'])) {
            $this->error("\nValidation Errors:");
            foreach ($validation['errors'] as $error) {
                $this->line("  ❌ {$error}");
            }
        }

        if (!empty($validation['warnings'])) {
            $this->warn("\nValidation Warnings:");
            foreach ($validation['warnings'] as $warning) {
                $this->line("  ⚠️  {$warning}");
            }
        }
    }

    /**
     * Display data preview
     */
    protected function displayDataPreview(array $dataPreview): void
    {
        if (empty($dataPreview)) return;

        $this->info("\n=== Data Preview ===");

        foreach (array_slice($dataPreview, 0, 2) as $index => $table) {
            $this->line("\nTable " . ($index + 1) . ":");
            $this->line("  Region: " . ($table['region'] ?? 'Unknown'));
            $this->line("  Table: " . ($table['table_name'] ?? 'Unknown'));
            $this->line("  Data rows: " . count($table['data'] ?? []));

            if (!empty($table['data'])) {
                $firstRow = $table['data'][0];
                $this->line("  Sample: {$firstRow['year']} - " .
                           (isset($firstRow['emission_factor']) ? $firstRow['emission_factor'] :
                            (isset($firstRow['co2_factor']) ? $firstRow['co2_factor'] : 'N/A')) .
                           " {$firstRow['unit']}");
            }
        }
    }

    /**
     * Display import statistics
     */
    protected function displayStats(array $stats): void
    {
        $this->info("\n=== Import Statistics ===");

        if (isset($stats['files_processed'])) {
            $this->line("Files processed: {$stats['files_processed']}");
        }
        if (isset($stats['sheets_processed'])) {
            $this->line("Sheets processed: {$stats['sheets_processed']}");
        }
        if (isset($stats['tables_found'])) {
            $this->line("Tables found: {$stats['tables_found']}");
        }
        if (isset($stats['rows_processed'])) {
            $this->line("Rows processed: {$stats['rows_processed']}");
        }
        if (isset($stats['regions_created'])) {
            $this->line("Regions created: {$stats['regions_created']}");
        }
        if (isset($stats['factors_created'])) {
            $this->line("Factors created: {$stats['factors_created']}");
        }
        if (isset($stats['factors_updated'])) {
            $this->line("Factors updated: {$stats['factors_updated']}");
        }

        if (!empty($stats['errors'])) {
            $this->warn("\nErrors encountered:");
            foreach ($stats['errors'] as $error) {
                $this->line("  - {$error}");
            }
        }

        if (!empty($stats['warnings'])) {
            $this->warn("\nWarnings:");
            foreach ($stats['warnings'] as $warning) {
                $this->line("  - {$warning}");
            }
        }
    }
}
