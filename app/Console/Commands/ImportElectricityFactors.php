<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ElectricityFactorsImportService;
use App\Services\GhgProtocolExcelImportService;
use App\Services\GhgProtocolDataValidator;

class ImportElectricityFactors extends Command
{
    protected $signature = 'ghg:import-electricity {file? : Excel file path} {--test : Run with test data} {--validate-only : Only validate data without importing}';
    protected $description = 'Import electricity emission factors from GHG Protocol Excel files or test data';

    public function handle()
    {
        $this->info('Importing Electricity Emission Factors...');

        $filePath = $this->argument('file');
        $useTestData = $this->option('test');
        $validateOnly = $this->option('validate-only');

        if ($filePath) {
            return $this->handleExcelImport($filePath, $validateOnly);
        } elseif ($useTestData) {
            return $this->handleTestDataImport($validateOnly);
        } else {
            $this->error('Please provide an Excel file path or use --test option');
            $this->line('Usage: php artisan ghg:import-electricity [file.xlsx] [--test] [--validate-only]');
            return 1;
        }
    }

    /**
     * Handle Excel file import
     */
    protected function handleExcelImport(string $filePath, bool $validateOnly): int
    {
        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $this->info("Processing Excel file: {$filePath}");

        $excelImporter = new GhgProtocolExcelImportService();
        $result = $excelImporter->importFromExcel($filePath, ['validate_only' => $validateOnly]);

        if ($result['success']) {
            $this->info($validateOnly ? 'Validation completed successfully!' : 'Import completed successfully!');
            $this->displayValidationResults($result['validation'] ?? []);
            $this->displayStats($result['stats']);

            if (isset($result['data_preview'])) {
                $this->displayDataPreview($result['data_preview']);
            }
        } else {
            $this->error($validateOnly ? 'Validation failed: ' . $result['error'] : 'Import failed: ' . $result['error']);

            if (isset($result['validation'])) {
                $this->displayValidationResults($result['validation']);
            }

            $this->displayStats($result['stats']);
            return 1;
        }

        return 0;
    }

    /**
     * Handle test data import
     */
    protected function handleTestDataImport(bool $validateOnly): int
    {
        $this->info('Using test electricity data...');

        $electricityData = $this->getTestElectricityData();

        if ($validateOnly) {
            $validator = new GhgProtocolDataValidator();
            $validation = $validator->validateElectricityData($electricityData);

            $this->displayValidationResults($validation);

            if ($validation['is_valid']) {
                $this->info('Test data validation passed!');
                return 0;
            } else {
                $this->error('Test data validation failed!');
                return 1;
            }
        }

        $importService = new ElectricityFactorsImportService();
        $result = $importService->importElectricityFactors($electricityData);

        if ($result['success']) {
            $this->info('Import completed successfully!');
            $this->displayStats($result['stats']);
        } else {
            $this->error('Import failed: ' . $result['error']);
            $this->displayStats($result['stats']);
            return 1;
        }

        return 0;
    }

    /**
     * Get test electricity data based on your screenshots
     */
    protected function getTestElectricityData(): array
    {
        return [
            // Table 1: Chinese Electricity National Average
            [
                'region' => 'China',
                'table_name' => 'Table 1. Chinese Electricity National Average',
                'notes' => 'Original source does not provide EFs for non-CO₂ gases.',
                'data' => [
                    [
                        'year' => 2022,
                        'emission_factor' => 0.5703,
                        'unit' => 'tCO₂/MWh'
                    ]
                ]
            ],

            // Table 2: Electricity Emission Factors for Taiwan
            [
                'region' => 'Taiwan',
                'table_name' => 'Table 2. Electricity Emission Factors for Taiwan',
                'notes' => 'The original source for the above EFs for Taiwan does not indicate which GWP was used to convert gases to CO₂e. Note that the above values are given in CO₂e and are not disaggregated by gas.',
                'data' => [
                    ['year' => 2005, 'emission_factor' => 0.555, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2006, 'emission_factor' => 0.562, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2007, 'emission_factor' => 0.558, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2008, 'emission_factor' => 0.555, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2009, 'emission_factor' => 0.543, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2010, 'emission_factor' => 0.534, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2011, 'emission_factor' => 0.534, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2012, 'emission_factor' => 0.529, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2013, 'emission_factor' => 0.519, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2014, 'emission_factor' => 0.518, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2015, 'emission_factor' => 0.525, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2016, 'emission_factor' => 0.530, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2017, 'emission_factor' => 0.554, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2018, 'emission_factor' => 0.533, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2019, 'emission_factor' => 0.509, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2020, 'emission_factor' => 0.502, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2021, 'emission_factor' => 0.509, 'unit' => 'kgCO₂e/kWh'],
                    ['year' => 2022, 'emission_factor' => 0.495, 'unit' => 'kgCO₂e/kWh'],
                ]
            ],

            // Table 3: Electricity Emission Factors for Brazil
            [
                'region' => 'Brazil',
                'table_name' => 'Table 3. Electricity Emission Factors for Brazil',
                'notes' => 'Pre-2016 EFs are not provided because Amazon\'s State had an independent grid system until its connection to the national grid in May 2015. EFs for the isolated Amazon grid pre-2016 are available at: https://caesp.fgv.br/centros/centro-estudos-sustentabilidade/projetos/programa-brasileiro-ghg-protocol. Original source did not provide EFs for non-CO₂ gases.',
                'data' => [
                    ['year' => 2016, 'emission_factor' => 0.0817, 'unit' => 'tCO₂/MWh'],
                    ['year' => 2017, 'emission_factor' => 0.0927, 'unit' => 'tCO₂/MWh'],
                    ['year' => 2018, 'emission_factor' => 0.0740, 'unit' => 'tCO₂/MWh'],
                    ['year' => 2019, 'emission_factor' => 0.0750, 'unit' => 'tCO₂/MWh'],
                    ['year' => 2020, 'emission_factor' => 0.0617, 'unit' => 'tCO₂/MWh'],
                    ['year' => 2021, 'emission_factor' => 0.1264, 'unit' => 'tCO₂/MWh'],
                    ['year' => 2022, 'emission_factor' => 0.0426, 'unit' => 'tCO₂/MWh'],
                    ['year' => 2023, 'emission_factor' => 0.0385, 'unit' => 'tCO₂/MWh'],
                ]
            ],

            // Table 4: Electricity Emission Factors for Thailand
            [
                'region' => 'Thailand',
                'table_name' => 'Table 4. Electricity Emission Factors for Thailand',
                'notes' => 'Source publishes EFs for generation and consumption. The above values are generation factors, not consumption. Consumption factors published by the source include transmission and distribution (T&D) emissions, which shouldn\'t be included in scope 2 estimations. Original source did not provide EFs for non-CO₂ gases.',
                'data' => [
                    ['year' => 2011, 'emission_factor' => 0.530, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2012, 'emission_factor' => 0.530, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2013, 'emission_factor' => 0.532, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2014, 'emission_factor' => 0.532, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2015, 'emission_factor' => 0.507, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2016, 'emission_factor' => 0.493, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2017, 'emission_factor' => 0.471, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2018, 'emission_factor' => 0.459, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2019, 'emission_factor' => 0.445, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2020, 'emission_factor' => 0.442, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2021, 'emission_factor' => 0.433, 'unit' => 'CO₂ (kg/kWh)'],
                    ['year' => 2022, 'emission_factor' => 0.412, 'unit' => 'CO₂ (kg/kWh)'],
                ]
            ],

            // Table 5: Electricity Emission Factors for U.K.
            [
                'region' => 'U.K.',
                'table_name' => 'Table 5. Electricity Emission Factors for U.K.',
                'notes' => 'UK DEFRA only presented values in CO₂e, using GWP-100 from the IPCC\'s Fifth Assessment Report (AR5). CH₄ and N₂O values presented in this table were reverse calculated using the same GWP values in order to be provided per individual GHG.',
                'data' => [
                    [
                        'year' => 2023,
                        'co2_factor' => 0.20496,
                        'ch4_factor' => 0.000032,
                        'n2o_factor' => 0.0000046,
                        'unit' => 'kg/kWh'
                    ]
                ]
            ],
        ];
    }

    /**
     * Display validation results
     */
    protected function displayValidationResults(array $validation): void
    {
        if (empty($validation)) return;

        $this->info("\n=== Validation Results ===");

        if (isset($validation['is_valid'])) {
            $status = $validation['is_valid'] ? '✅ PASSED' : '❌ FAILED';
            $this->line("Status: {$status}");
        }

        if (!empty($validation['errors'])) {
            $this->error("\nValidation Errors:");
            foreach ($validation['errors'] as $error) {
                $this->line("  ❌ {$error}");
            }
        }

        if (!empty($validation['warnings'])) {
            $this->warn("\nValidation Warnings:");
            foreach ($validation['warnings'] as $warning) {
                $this->line("  ⚠️  {$warning}");
            }
        }
    }

    /**
     * Display data preview
     */
    protected function displayDataPreview(array $dataPreview): void
    {
        if (empty($dataPreview)) return;

        $this->info("\n=== Data Preview ===");

        foreach (array_slice($dataPreview, 0, 2) as $index => $table) {
            $this->line("\nTable " . ($index + 1) . ":");
            $this->line("  Region: " . ($table['region'] ?? 'Unknown'));
            $this->line("  Table: " . ($table['table_name'] ?? 'Unknown'));
            $this->line("  Data rows: " . count($table['data'] ?? []));

            if (!empty($table['data'])) {
                $firstRow = $table['data'][0];
                $this->line("  Sample: {$firstRow['year']} - " .
                           (isset($firstRow['emission_factor']) ? $firstRow['emission_factor'] :
                            (isset($firstRow['co2_factor']) ? $firstRow['co2_factor'] : 'N/A')) .
                           " {$firstRow['unit']}");
            }
        }
    }

    /**
     * Display import statistics
     */
    protected function displayStats(array $stats): void
    {
        $this->info("\n=== Import Statistics ===");

        if (isset($stats['files_processed'])) {
            $this->line("Files processed: {$stats['files_processed']}");
        }
        if (isset($stats['sheets_processed'])) {
            $this->line("Sheets processed: {$stats['sheets_processed']}");
        }
        if (isset($stats['tables_found'])) {
            $this->line("Tables found: {$stats['tables_found']}");
        }
        if (isset($stats['rows_processed'])) {
            $this->line("Rows processed: {$stats['rows_processed']}");
        }
        if (isset($stats['regions_created'])) {
            $this->line("Regions created: {$stats['regions_created']}");
        }
        if (isset($stats['factors_created'])) {
            $this->line("Factors created: {$stats['factors_created']}");
        }
        if (isset($stats['factors_updated'])) {
            $this->line("Factors updated: {$stats['factors_updated']}");
        }

        if (!empty($stats['errors'])) {
            $this->warn("\nErrors encountered:");
            foreach ($stats['errors'] as $error) {
                $this->line("  - {$error}");
            }
        }

        if (!empty($stats['warnings'])) {
            $this->warn("\nWarnings:");
            foreach ($stats['warnings'] as $warning) {
                $this->line("  - {$warning}");
            }
        }
    }
}
