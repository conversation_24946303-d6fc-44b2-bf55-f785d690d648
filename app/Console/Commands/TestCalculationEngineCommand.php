<?php

namespace App\Console\Commands;

use App\Models\ActivityData;
use App\Services\GranularCalculationEngine;
use App\Services\MethodologyFrameworkService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestCalculationEngineCommand extends Command
{
    protected $signature = 'calculations:test
                            {--activity= : Specific activity data ID to test}
                            {--methodology= : Methodology to test (ghg_protocol, iso_14064, pcaf, custom_hybrid)}
                            {--scope= : Scope to test (1, 2, 3)}
                            {--sample-size=10 : Number of sample calculations to run}';

    protected $description = 'Test the granular calculation engine with various methodologies';

    protected GranularCalculationEngine $calculationEngine;
    protected MethodologyFrameworkService $methodologyService;

    public function __construct(
        GranularCalculationEngine $calculationEngine,
        MethodologyFrameworkService $methodologyService
    ) {
        parent::__construct();
        $this->calculationEngine = $calculationEngine;
        $this->methodologyService = $methodologyService;
    }

    public function handle(): int
    {
        $this->info('🧪 Starting calculation engine testing...');

        $activityId = $this->option('activity');
        $methodology = $this->option('methodology');
        $scope = $this->option('scope');
        $sampleSize = (int) $this->option('sample-size');

        try {
            if ($activityId) {
                $this->info("📊 Testing specific activity: {$activityId}");
                $results = $this->testSpecificActivity($activityId, $methodology);
            } else {
                $this->info("📊 Running sample calculations");
                $results = $this->runSampleCalculations($methodology, $scope, $sampleSize);
            }

            $this->displayTestResults($results);

            $this->info('✅ Calculation engine testing completed successfully');
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Calculation testing failed: {$e->getMessage()}");
            Log::error('Calculation testing command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }

    protected function testSpecificActivity(string $activityId, ?string $methodology): array
    {
        $activityData = ActivityData::find($activityId);

        if (!$activityData) {
            throw new \InvalidArgumentException("Activity data not found: {$activityId}");
        }

        $this->line("  📍 Activity: {$activityData->activity_name}");
        $unitSymbol = $activityData->activityUnit ? $activityData->activityUnit->symbol : 'units';
        $this->line("  📊 Quantity: {$activityData->quantity} {$unitSymbol}");
        $this->line("  🎯 Scope: {$activityData->scope}");

        $results = [];

        if ($methodology) {
            // Test specific methodology
            $result = $this->methodologyService->calculateWithMethodology($activityData, $methodology);
            $results[$methodology] = $result;
        } else {
            // Test all supported methodologies
            $methodologies = $this->methodologyService->getSupportedMethodologies();

            foreach ($methodologies as $code => $config) {
                if (in_array($activityData->scope, $config['supported_scopes'])) {
                    $result = $this->methodologyService->calculateWithMethodology($activityData, $code);
                    $results[$code] = $result;
                }
            }
        }

        return [
            'type' => 'specific_activity',
            'activity_data' => $activityData,
            'results' => $results,
        ];
    }

    protected function runSampleCalculations(?string $methodology, ?string $scope, int $sampleSize): array
    {
        $query = ActivityData::with(['activityUnit', 'emissionFactor']);

        if ($scope) {
            $query->where('scope', $scope);
        }

        $activities = $query->limit($sampleSize)->get();

        if ($activities->isEmpty()) {
            throw new \RuntimeException('No activity data found for testing');
        }

        $this->line("  📊 Testing {$activities->count()} activities");

        $results = [];
        $successCount = 0;
        $errorCount = 0;

        foreach ($activities as $activity) {
            try {
                if ($methodology) {
                    $result = $this->methodologyService->calculateWithMethodology($activity, $methodology);
                } else {
                    $result = $this->calculationEngine->calculateEmissions($activity);
                }

                if ($result['success']) {
                    $successCount++;
                } else {
                    $errorCount++;
                }

                $results[] = [
                    'activity_id' => $activity->id,
                    'activity_name' => $activity->activity_name,
                    'scope' => $activity->scope,
                    'result' => $result,
                ];

            } catch (\Exception $e) {
                $errorCount++;
                $results[] = [
                    'activity_id' => $activity->id,
                    'activity_name' => $activity->activity_name,
                    'scope' => $activity->scope,
                    'result' => [
                        'success' => false,
                        'error' => $e->getMessage(),
                    ],
                ];
            }
        }

        return [
            'type' => 'sample_calculations',
            'total_tested' => $activities->count(),
            'success_count' => $successCount,
            'error_count' => $errorCount,
            'methodology' => $methodology,
            'scope_filter' => $scope,
            'results' => $results,
        ];
    }

    protected function displayTestResults(array $results): void
    {
        $this->info('📈 Test Results:');

        if ($results['type'] === 'specific_activity') {
            $this->displaySpecificActivityResults($results);
        } else {
            $this->displaySampleCalculationResults($results);
        }
    }

    protected function displaySpecificActivityResults(array $results): void
    {
        $activity = $results['activity_data'];
        $calculationResults = $results['results'];

        $this->line("  📍 Activity: {$activity->activity_name}");
        $this->line("  📊 Quantity: {$activity->quantity}");
        $this->line("  🎯 Scope: {$activity->scope}");
        $this->line('');

        foreach ($calculationResults as $methodology => $result) {
            $this->info("  🔬 Methodology: {$methodology}");

            if ($result['success']) {
                $emissions = isset($result['total_co2e']) ? $result['total_co2e'] : (isset($result['emissions']) ? $result['emissions'] : 0);
                $this->line("    ✅ Emissions: " . number_format($emissions, 6) . " tCO2e");

                if (isset($result['methodology_details'])) {
                    $details = $result['methodology_details'];
                    $standard = isset($details['standard']) ? $details['standard'] : 'N/A';
                    $this->line("    📋 Standard: {$standard}");
                    if (isset($details['tier_level'])) {
                        $this->line("    🎯 Tier: {$details['tier_level']}");
                    }
                    if (isset($details['data_quality_rating'])) {
                        $this->line("    📊 Data Quality: {$details['data_quality_rating']}");
                    }
                }

                if (isset($result['by_gas'])) {
                    $this->line("    🧪 By Gas:");
                    foreach ($result['by_gas'] as $gas => $gasResult) {
                        $gasEmissions = isset($gasResult['emissions']) ? $gasResult['emissions'] : 0;
                        $this->line("      {$gas}: " . number_format($gasEmissions, 6));
                    }
                }
            } else {
                $this->error("    ❌ Error: {$result['error']}");
            }

            $this->line('');
        }
    }

    protected function displaySampleCalculationResults(array $results): void
    {
        $this->line("  📊 Total Tested: {$results['total_tested']}");
        $this->line("  ✅ Successful: {$results['success_count']}");
        $this->line("  ❌ Errors: {$results['error_count']}");

        $successRate = $results['total_tested'] > 0
            ? ($results['success_count'] / $results['total_tested']) * 100
            : 0;
        $this->line("  📈 Success Rate: " . number_format($successRate, 1) . "%");

        if ($results['methodology']) {
            $this->line("  🔬 Methodology: {$results['methodology']}");
        }

        if ($results['scope_filter']) {
            $this->line("  🎯 Scope Filter: {$results['scope_filter']}");
        }

        $this->line('');

        // Show sample successful calculations
        $successfulResults = array_filter($results['results'], function ($result) {
            return isset($result['result']['success']) ? $result['result']['success'] : false;
        });

        if (!empty($successfulResults)) {
            $this->info('  📋 Sample Successful Calculations:');
            foreach (array_slice($successfulResults, 0, 5) as $result) {
                $emissions = isset($result['result']['total_co2e']) ? $result['result']['total_co2e'] : (isset($result['result']['emissions']) ? $result['result']['emissions'] : 0);
                $this->line("    {$result['activity_name']} (Scope {$result['scope']}): " .
                           number_format($emissions, 6) . " tCO2e");
            }

            if (count($successfulResults) > 5) {
                $remaining = count($successfulResults) - 5;
                $this->line("    ... and {$remaining} more successful calculations");
            }
        }

        // Show sample errors
        $errorResults = array_filter($results['results'], function ($result) {
            return !(isset($result['result']['success']) ? $result['result']['success'] : false);
        });

        if (!empty($errorResults)) {
            $this->warn('  ⚠️ Sample Errors:');
            foreach (array_slice($errorResults, 0, 3) as $result) {
                $error = isset($result['result']['error']) ? $result['result']['error'] : 'Unknown error';
                $this->line("    {$result['activity_name']}: {$error}");
            }

            if (count($errorResults) > 3) {
                $remaining = count($errorResults) - 3;
                $this->line("    ... and {$remaining} more errors");
            }
        }

        // Calculate emissions statistics
        $emissionsValues = [];
        foreach ($successfulResults as $result) {
            $emissions = isset($result['result']['total_co2e']) ? $result['result']['total_co2e'] : (isset($result['result']['emissions']) ? $result['result']['emissions'] : 0);
            if ($emissions > 0) {
                $emissionsValues[] = $emissions;
            }
        }

        if (!empty($emissionsValues)) {
            $this->info('  📊 Emissions Statistics:');
            $this->line("    Total: " . number_format(array_sum($emissionsValues), 2) . " tCO2e");
            $this->line("    Average: " . number_format(array_sum($emissionsValues) / count($emissionsValues), 6) . " tCO2e");
            $this->line("    Min: " . number_format(min($emissionsValues), 6) . " tCO2e");
            $this->line("    Max: " . number_format(max($emissionsValues), 6) . " tCO2e");
        }
    }
}
