<?php

namespace App\Console\Commands;

use App\Services\StakeholderEngagementService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class InitializeStakeholderEngagementCommand extends Command
{
    protected $signature = 'stakeholders:initialize 
                            {--force : Force re-initialization even if already initialized}
                            {--dry-run : Run without making changes}';

    protected $description = 'Initialize comprehensive stakeholder engagement system with roles, permissions, and dashboards';

    protected StakeholderEngagementService $stakeholderService;

    public function __construct(StakeholderEngagementService $stakeholderService)
    {
        parent::__construct();
        $this->stakeholderService = $stakeholderService;
    }

    public function handle(): int
    {
        $this->info('🤝 Initializing stakeholder engagement system...');

        $force = $this->option('force');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        try {
            // Check if system is already initialized
            if (!$force && $this->isSystemInitialized()) {
                $this->warn('⚠️  Stakeholder engagement system is already initialized');
                $this->line('Use --force to re-initialize');
                return 0;
            }

            if ($dryRun) {
                $this->displayInitializationPlan();
                return 0;
            }

            $this->line('  🔧 Creating enhanced permissions and roles...');
            $result = $this->stakeholderService->initializeStakeholderSystem();

            if (!$result['success']) {
                $this->error("❌ Initialization failed: {$result['error']}");
                return 1;
            }

            $this->displayInitializationResults($result);
            $this->info('✅ Stakeholder engagement system initialized successfully');

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Initialization failed: {$e->getMessage()}");
            Log::error('Stakeholder engagement initialization failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }

    protected function isSystemInitialized(): bool
    {
        // Check if stakeholder roles exist
        $stakeholderRoles = [
            'finance_team',
            'sustainability_lead',
            'executive',
            'facility_manager',
            'supplier_admin',
            'supplier_user',
        ];

        foreach ($stakeholderRoles as $role) {
            if (!\Spatie\Permission\Models\Role::where('name', $role)->exists()) {
                return false;
            }
        }

        return true;
    }

    protected function displayInitializationPlan(): void
    {
        $this->info('📋 Initialization Plan:');
        
        $this->line('  🔐 Permissions to be created:');
        $permissions = [
            'view_cost_data', 'view_financial_metrics', 'view_investment_analysis',
            'manage_targets', 'view_all_emissions', 'manage_initiatives',
            'view_executive_dashboard', 'view_kpi_summary', 'approve_targets',
            'manage_facility_data', 'submit_activity_data',
            'manage_supplier_data', 'submit_emissions_data',
            'view_audit_trails', 'access_verification_data',
        ];
        
        foreach (array_slice($permissions, 0, 10) as $permission) {
            $this->line("    - {$permission}");
        }
        $this->line("    ... and " . (count($permissions) - 10) . " more permissions");
        
        $this->line('');
        $this->line('  👥 Roles to be created:');
        $roles = [
            'finance_team' => 'Finance Team Member',
            'sustainability_lead' => 'Sustainability Lead',
            'executive' => 'Executive',
            'facility_manager' => 'Facility Manager',
            'supplier_admin' => 'Supplier Administrator',
            'supplier_user' => 'Supplier User',
            'auditor' => 'External Auditor',
            'consultant' => 'Sustainability Consultant',
        ];
        
        foreach ($roles as $roleName => $displayName) {
            $this->line("    - {$roleName}: {$displayName}");
        }
        
        $this->line('');
        $this->line('  📊 Dashboards to be created:');
        foreach ($roles as $roleName => $displayName) {
            $this->line("    - {$displayName} Dashboard");
        }
    }

    protected function displayInitializationResults(array $result): void
    {
        $this->info('📊 Initialization Results:');
        $this->line("  ✅ Permissions created: {$result['permissions_created']}");
        $this->line("  ✅ Roles created: {$result['roles_created']}");
        $this->line("  ✅ Dashboards created: {$result['dashboards_created']}");
        $this->line('');

        $this->info('🎯 Stakeholder Roles and Capabilities:');
        
        $this->line('  💰 Finance Team:');
        $this->line('    - View cost data and financial metrics');
        $this->line('    - Access investment analysis and ROI calculations');
        $this->line('    - Manage carbon pricing and budget allocations');
        
        $this->line('');
        $this->line('  🌱 Sustainability Lead:');
        $this->line('    - Manage targets and view all emissions data');
        $this->line('    - Manage initiatives and track progress');
        $this->line('    - Assign actions and approve initiatives');
        $this->line('    - Manage supplier relationships');
        
        $this->line('');
        $this->line('  👔 Executive:');
        $this->line('    - View executive dashboard and KPI summary');
        $this->line('    - Access strategic metrics and risk assessments');
        $this->line('    - Approve targets and view board reports');
        
        $this->line('');
        $this->line('  🏭 Facility Manager:');
        $this->line('    - Manage facility data and emissions');
        $this->line('    - Submit activity data and manage facility actions');
        $this->line('    - View facility-specific targets and progress');
        
        $this->line('');
        $this->line('  🤝 Supplier Portal:');
        $this->line('    - Supplier Admin: Full supplier data management');
        $this->line('    - Supplier User: Data submission and progress viewing');
        $this->line('    - Secure portal access with progress dashboards');
        
        $this->line('');
        $this->line('  🔍 External Stakeholders:');
        $this->line('    - Auditor: Access audit trails and verification data');
        $this->line('    - Consultant: Analysis data and benchmarking access');
        
        $this->line('');
        $this->info('🚀 Next Steps:');
        $this->line('  1. Assign users to appropriate roles');
        $this->line('  2. Set up supplier portal access');
        $this->line('  3. Create stakeholder actions and assignments');
        $this->line('  4. Configure role-specific dashboards');
        
        $this->line('');
        $this->line('📚 Available Commands:');
        $this->line('  - php artisan stakeholders:create-supplier-portal {supplier}');
        $this->line('  - php artisan stakeholders:assign-action {organization}');
        $this->line('  - php artisan stakeholders:generate-engagement-report {organization}');
    }
}
