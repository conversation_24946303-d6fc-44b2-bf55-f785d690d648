<?php

namespace App\Console\Commands;

use App\Services\IoTSensorService;
use App\Services\DataLakeService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PollIoTSensorsCommand extends Command
{
    protected $signature = 'data:poll-iot-sensors 
                            {--facility= : Specific facility ID to poll}
                            {--sensor= : Specific sensor ID to poll}
                            {--type= : Sensor type to poll (energy_meter, gas_meter, etc.)}
                            {--dry-run : Run without storing data}';

    protected $description = 'Poll data from IoT sensors';

    protected IoTSensorService $iotService;
    protected DataLakeService $dataLakeService;

    public function __construct(IoTSensorService $iotService, DataLakeService $dataLakeService)
    {
        parent::__construct();
        $this->iotService = $iotService;
        $this->dataLakeService = $dataLakeService;
    }

    public function handle(): int
    {
        $this->info('📡 Starting IoT sensor polling...');

        $facilityId = $this->option('facility');
        $sensorId = $this->option('sensor');
        $sensorType = $this->option('type');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No data will be stored');
        }

        try {
            if ($sensorId) {
                $this->info("📊 Polling specific sensor: {$sensorId}");
                $results = $this->pollSpecificSensor($sensorId, $dryRun);
            } elseif ($facilityId) {
                $this->info("📊 Polling sensors for facility: {$facilityId}");
                $results = $this->pollFacilitySensors($facilityId, $sensorType, $dryRun);
            } else {
                $this->info("📊 Polling all active sensors");
                $results = $this->pollAllSensors($sensorType, $dryRun);
            }

            $this->displayResults($results);

            $this->info('✅ IoT sensor polling completed successfully');
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ IoT polling failed: {$e->getMessage()}");
            Log::error('IoT polling command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }

    protected function pollSpecificSensor(string $sensorId, bool $dryRun): array
    {
        $status = $this->iotService->getSensorStatus($sensorId);
        
        if ($status['status'] === 'not_found') {
            throw new \InvalidArgumentException("Sensor not found: {$sensorId}");
        }

        $this->line("  📍 Sensor Status: {$status['status']}");
        
        if ($status['status'] === 'offline') {
            $this->warn("  ⚠️ Sensor is offline");
            return ['sensor_id' => $sensorId, 'status' => 'offline', 'data_collected' => false];
        }

        if ($dryRun) {
            return [
                'sensor_id' => $sensorId,
                'status' => 'online',
                'dry_run' => true,
                'message' => 'Dry run - would poll sensor data'
            ];
        }

        $sensor = $status['sensor'];
        $data = $this->iotService->pollSensorData($sensor);
        
        if ($data) {
            $success = $this->iotService->ingestSensorData($sensorId, $data);
            return [
                'sensor_id' => $sensorId,
                'status' => 'online',
                'data_collected' => $success,
                'data' => $data,
            ];
        }

        return ['sensor_id' => $sensorId, 'status' => 'online', 'data_collected' => false];
    }

    protected function pollFacilitySensors(string $facilityId, ?string $sensorType, bool $dryRun): array
    {
        $sensors = $this->iotService->listSensors($facilityId);
        
        if ($sensorType) {
            $sensors = $sensors->where('type', $sensorType);
        }

        $this->line("  📊 Found {$sensors->count()} sensor(s) to poll");

        $results = [
            'facility_id' => $facilityId,
            'total_sensors' => $sensors->count(),
            'online_sensors' => 0,
            'offline_sensors' => 0,
            'data_collected' => 0,
            'sensors' => [],
        ];

        if ($dryRun) {
            $results['dry_run'] = true;
            $results['message'] = 'Dry run - would poll facility sensors';
            return $results;
        }

        foreach ($sensors as $sensor) {
            try {
                $sensorResult = $this->pollSpecificSensor($sensor->id, false);
                $results['sensors'][] = $sensorResult;
                
                if ($sensorResult['status'] === 'online') {
                    $results['online_sensors']++;
                    if ($sensorResult['data_collected']) {
                        $results['data_collected']++;
                    }
                } else {
                    $results['offline_sensors']++;
                }

            } catch (\Exception $e) {
                $this->warn("  ⚠️ Failed to poll sensor {$sensor->id}: {$e->getMessage()}");
                $results['offline_sensors']++;
            }
        }

        return $results;
    }

    protected function pollAllSensors(?string $sensorType, bool $dryRun): array
    {
        $sensors = $this->iotService->listSensors();
        
        if ($sensorType) {
            $sensors = $sensors->where('type', $sensorType);
        }

        $this->line("  📊 Found {$sensors->count()} sensor(s) to poll");

        $results = [
            'total_sensors' => $sensors->count(),
            'online_sensors' => 0,
            'offline_sensors' => 0,
            'data_collected' => 0,
            'facilities' => [],
        ];

        if ($dryRun) {
            $results['dry_run'] = true;
            $results['message'] = 'Dry run - would poll all sensors';
            return $results;
        }

        $facilitySensors = $sensors->groupBy('facility_id');

        foreach ($facilitySensors as $facilityId => $facilitySensorList) {
            try {
                $facilityResult = $this->pollFacilitySensors($facilityId, $sensorType, false);
                $results['facilities'][] = $facilityResult;
                
                $results['online_sensors'] += $facilityResult['online_sensors'];
                $results['offline_sensors'] += $facilityResult['offline_sensors'];
                $results['data_collected'] += $facilityResult['data_collected'];

            } catch (\Exception $e) {
                $this->warn("  ⚠️ Failed to poll facility {$facilityId}: {$e->getMessage()}");
            }
        }

        return $results;
    }

    protected function displayResults(array $results): void
    {
        $this->info('📈 Polling Results:');
        
        if (isset($results['dry_run']) && $results['dry_run']) {
            $this->warn("  🧪 {$results['message']}");
            return;
        }

        if (isset($results['sensor_id'])) {
            // Single sensor results
            $this->line("  📍 Sensor: {$results['sensor_id']}");
            $this->line("  📊 Status: {$results['status']}");
            $this->line("  📋 Data Collected: " . ($results['data_collected'] ? 'Yes' : 'No'));
            
            if (isset($results['data'])) {
                $this->info('  📄 Sample Data:');
                $this->line("    " . json_encode($results['data'], JSON_PRETTY_PRINT));
            }
        } else {
            // Multiple sensors results
            if (isset($results['facility_id'])) {
                $this->line("  📍 Facility: {$results['facility_id']}");
            }
            
            $this->line("  📊 Total Sensors: {$results['total_sensors']}");
            $this->line("  🟢 Online: {$results['online_sensors']}");
            $this->line("  🔴 Offline: {$results['offline_sensors']}");
            $this->line("  📋 Data Collected: {$results['data_collected']}");

            if (isset($results['facilities']) && !empty($results['facilities'])) {
                $this->info('  🏢 Facility Breakdown:');
                foreach ($results['facilities'] as $facility) {
                    $this->line("    Facility {$facility['facility_id']}: {$facility['data_collected']}/{$facility['total_sensors']} sensors");
                }
            }
        }

        // Show data lake statistics
        $stats = $this->dataLakeService->getStatistics();
        $this->info('  🏗️ Data Lake Stats:');
        $this->line("    IoT Records: " . ($stats['by_source']['iot_sensor'] ?? 0));
        $this->line("    Recent Activity (30 days): {$stats['recent_activity']}");
    }
}
