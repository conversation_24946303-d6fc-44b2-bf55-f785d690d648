<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\EmissionFactorVariant;
use App\Models\GreenhouseGas;
use App\Models\MeasurementUnit;
use App\Services\EmissionFactorLookupService;

class TestGhgProtocolSchema extends Command
{
    protected $signature = 'ghg:test-schema';
    protected $description = 'Test the new GHG Protocol schema structure';

    public function handle()
    {
        $this->info('Testing GHG Protocol Schema Structure...');
        
        // Test basic structure
        $this->testBasicStructure();
        
        // Test data availability
        $this->testDataAvailability();
        
        // Test lookup service
        $this->testLookupService();
        
        $this->info('Schema test completed!');
    }

    protected function testBasicStructure()
    {
        $this->info("\n=== Testing Basic Structure ===");
        
        $sectorCount = GhgProtocolSector::count();
        $activityCount = GhgProtocolActivity::count();
        $regionCount = GhgProtocolRegion::count();
        $factorCount = GhgProtocolEmissionFactor::count();
        $variantCount = EmissionFactorVariant::count();
        
        $this->line("Sectors: {$sectorCount}");
        $this->line("Activities: {$activityCount}");
        $this->line("Regions: {$regionCount}");
        $this->line("Emission Factors: {$factorCount}");
        $this->line("Factor Variants: {$variantCount}");
        
        // Test relationships
        $sector = GhgProtocolSector::with(['activities', 'ghgProtocolEmissionFactors'])->first();
        if ($sector) {
            $this->line("Sample sector '{$sector->display_name}' has {$sector->activities->count()} activities and {$sector->ghgProtocolEmissionFactors->count()} factors");
        }
    }

    protected function testDataAvailability()
    {
        $this->info("\n=== Testing Data Availability ===");
        
        // Check for required units
        $requiredUnits = ['GJ', 'kWh', 'L', 'kg CO₂e', 'kg', 'm³'];
        $this->line("Checking for required measurement units:");
        
        foreach ($requiredUnits as $unitSymbol) {
            $unit = MeasurementUnit::where('symbol', $unitSymbol)->first();
            $status = $unit ? '✓' : '✗';
            $this->line("  {$status} {$unitSymbol}");
        }
        
        // Check for required gases
        $requiredGases = ['CO₂', 'CH₄', 'N₂O'];
        $this->line("\nChecking for required greenhouse gases:");
        
        foreach ($requiredGases as $gasFormula) {
            $gas = GreenhouseGas::where('chemical_formula', $gasFormula)->first();
            $status = $gas ? '✓' : '✗';
            $this->line("  {$status} {$gasFormula}");
        }
    }

    protected function testLookupService()
    {
        $this->info("\n=== Testing Lookup Service ===");
        
        try {
            $lookupService = new EmissionFactorLookupService();
            
            // Test basic lookup
            $criteria = [
                'sector_id' => GhgProtocolSector::first()?->id,
                'activity_id' => GhgProtocolActivity::first()?->id,
                'gas_id' => GreenhouseGas::first()?->id,
            ];
            
            if (array_filter($criteria)) {
                $factor = $lookupService->findBestFactor($criteria);
                
                if ($factor) {
                    $this->line("✓ Successfully found emission factor: {$factor->name}");
                    
                    // Test variant lookup
                    $variant = $lookupService->findBestVariant($factor, ['region_id' => GhgProtocolRegion::first()?->id]);
                    if ($variant) {
                        $this->line("✓ Found variant: {$variant->variant_name}");
                    } else {
                        $this->line("- No variants found for this factor");
                    }
                } else {
                    $this->line("✗ No emission factor found for criteria");
                }
            } else {
                $this->line("- Cannot test lookup service: missing required data");
            }
            
        } catch (\Exception $e) {
            $this->error("Lookup service test failed: " . $e->getMessage());
        }
    }
}
