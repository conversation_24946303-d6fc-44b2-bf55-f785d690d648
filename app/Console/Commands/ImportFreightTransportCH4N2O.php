<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\MeasurementUnit;
use App\Models\GreenhouseGas;
use Illuminate\Support\Facades\DB;

class ImportFreightTransportCH4N2O extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:freight-transport-ch4-n2o';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Freight Transport CH4 and N2O Emission Factors by Weight or Vehicle Distance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of Freight Transport CH4 and N2O Emission Factors...');
        
        // Freight transport CH4 and N2O data from Table 2
        // Format: [Region, Vehicle, Vehicle_Class, Weight_Class, Fuel, CH4_EF, CH4_Unit, N2O_EF, N2O_Unit]
        $freight_ch4_n2o_data = [
            // UK Freight flights
            ['UK', 'Freight flights', 'Domestic', 'Between UK airports', '', 0.0752, 'g/tonne-km/tonne', 0.0747, 'g/tonne-km/tonne'],
            ['UK', 'Freight flights', 'Short haul', 'up to 3700 km distance', '', 0.0390, 'g/tonne-km/tonne', 0.0387, 'g/tonne-km/tonne'],
            ['UK', 'Freight flights', 'Long haul', 'over 3700 km distance', '', 0.0116, 'g/tonne-km/tonne', 0.0137, 'g/tonne-km/tonne'],
            ['UK', 'Freight flights', 'International', 'to/from UK to/from distance', '', 0.0116, 'g/tonne-km/tonne', 0.0137, 'g/tonne-km/tonne'],
            
            // UK Rail
            ['UK', 'Rail', 'Freight train', '<1.305 tonnes', '', 0.0068, 'g/tonne-km/tonne', 0.00104, 'g/tonne-km/tonne'],
            
            // UK Vans
            ['UK', 'Vans', '<1.305 tonnes', 'N/A', 'Petrol', 0.0512, 'g/tonne-km/tonne', 0.00596, 'g/tonne-km/tonne'],
            ['UK', 'Vans', '1.305 - 1.74 tonnes', 'N/A', 'Petrol', 0.0354, 'g/tonne-km/tonne', 0.00412, 'g/tonne-km/tonne'],
            ['UK', 'Vans', '1.74 - 3.5 tonnes', 'N/A', 'Petrol', 0.0236, 'g/tonne-km/tonne', 0.00309, 'g/tonne-km/tonne'],
            ['UK', 'Vans', 'Average (up to 3.5 tonnes)', 'N/A', 'Petrol', 0.0324, 'g/tonne-km/tonne', 0.00356, 'g/tonne-km/tonne'],
            ['UK', 'Vans', '<1.305 tonnes', 'N/A', 'Diesel', 0.0008, 'g/tonne-km/tonne', 0.02470, 'g/tonne-km/tonne'],
            ['UK', 'Vans', '1.305 - 1.74 tonnes', 'N/A', 'Diesel', 0.0004, 'g/tonne-km/tonne', 0.01144, 'g/tonne-km/tonne'],
            ['UK', 'Vans', '1.74 - 3.5 tonnes', 'N/A', 'Diesel', 0.0004, 'g/tonne-km/tonne', 0.01568, 'g/tonne-km/tonne'],
            ['UK', 'Vans', 'Average (up to 3.5 tonnes)', 'N/A', 'Diesel', 0.0004, 'g/tonne-km/tonne', 0.01590, 'g/tonne-km/tonne'],
            ['UK', 'Vans', '3.5 to 7.5 tonnes', 'N/A', 'CNG', 0.1110, 'g/tonne-km/tonne', 0.00470, 'g/tonne-km/tonne'],
            ['UK', 'Vans', '3.5 to 7.5 tonnes', 'N/A', 'LPG', 0.0004, 'g/tonne-km/tonne', 0.01001, 'g/tonne-km/tonne'],
            ['UK', 'Vans', 'Engine Size Unknown', 'N/A', 'Unknown', 0.0012, 'g/tonne-km/tonne', 0.01527, 'g/tonne-km/tonne'],
            
            // UK HGV - Rigid
            ['UK', 'HGV - Rigid', '3.5 - 7.5 tonnes', 'Average Laden', 'Diesel', 0.0040, 'g/tonne-km/tonne', 0.02530, 'g/tonne-km/tonne'],
            ['UK', 'HGV - Rigid', '7.5 - 17 tonnes', 'Average Laden', 'Diesel', 0.0028, 'g/tonne-km/tonne', 0.01816, 'g/tonne-km/tonne'],
            ['UK', 'HGV - Rigid', '>17 tonnes', 'Average Laden', 'Diesel', 0.0016, 'g/tonne-km/tonne', 0.00758, 'g/tonne-km/tonne'],
            ['UK', 'HGV - Rigid', 'Engine Size Unknown', 'Average Laden', 'Diesel', 0.0016, 'g/tonne-km/tonne', 0.00856, 'g/tonne-km/tonne'],
            
            // UK HGV - Articulated
            ['UK', 'HGV - Articulated', '3.5 - 33 tonnes', 'Average Laden', 'Diesel', 0.0008, 'g/tonne-km/tonne', 0.00748, 'g/tonne-km/tonne'],
            ['UK', 'HGV - Articulated', '>33 tonnes', 'Average Laden', 'Diesel', 0.0004, 'g/tonne-km/tonne', 0.00473, 'g/tonne-km/tonne'],
            ['UK', 'HGV - Articulated', 'Engine Size Unknown', 'Average Laden', 'Diesel', 0.0004, 'g/tonne-km/tonne', 0.00480, 'g/tonne-km/tonne'],
            ['UK', 'HGV - Tyre Unknown', 'Average', 'Average Laden', 'Diesel', 0.0008, 'g/tonne-km/tonne', 0.00550, 'g/tonne-km/tonne'],
            
            // UK Sea tanker
            ['UK', 'Sea tanker', 'Crude tanker', '200,000+ dwt', '', 0.0004, 'g/tonne-km/tonne', 0.0001, 'g/tonne-km/tonne'],
            ['UK', 'Sea tanker', 'Crude tanker', '120,000-199,999 dwt', '', 0.0004, 'g/tonne-km/tonne', 0.0002, 'g/tonne-km/tonne'],
            ['UK', 'Sea tanker', 'Crude tanker', '80,000-119,999 dwt', '', 0.0001, 'g/tonne-km/tonne', 0.0003, 'g/tonne-km/tonne'],
            ['UK', 'Sea tanker', 'Crude tanker', '60,000-79,999 dwt', '', 0.0001, 'g/tonne-km/tonne', 0.0003, 'g/tonne-km/tonne'],
            ['UK', 'Sea tanker', 'Crude tanker', '10,000-59,999 dwt', '', 0.0004, 'g/tonne-km/tonne', 0.0004, 'g/tonne-km/tonne'],
            ['UK', 'Sea tanker', 'Crude tanker', 'Average', '', 0.0004, 'g/tonne-km/tonne', 0.0002, 'g/tonne-km/tonne'],
            
            // UK Cargo ship
            ['UK', 'Cargo ship', 'Bulk carrier', '200,000+ dwt', '', 0.0004, 'g/tonne-km/tonne', 0.0001, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Bulk carrier', '10,000-199,999 dwt', '', 0.0004, 'g/tonne-km/tonne', 0.0001, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Bulk carrier', '60,000-99,999 dwt', '', 0.0000, 'g/tonne-km/tonne', 0.0002, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Bulk carrier', '35,000-59,999 dwt', '', 0.0001, 'g/tonne-km/tonne', 0.0003, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Bulk carrier', '10,000-34,999 dwt', '', 0.0001, 'g/tonne-km/tonne', 0.0004, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Bulk carrier', '0-9999 dwt', '', 0.0004, 'g/tonne-km/tonne', 0.0015, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Bulk carrier', 'Average', '', 0.0004, 'g/tonne-km/tonne', 0.0002, 'g/tonne-km/tonne'],
            
            // UK Container ships
            ['UK', 'Cargo ship', 'Container ship', '8000+ TEU', '', 0.0002, 'g/tonne-km/tonne', 0.0006, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Container ship', '5000-7999 TEU', '', 0.0002, 'g/tonne-km/tonne', 0.0008, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Container ship', '3000-4999 TEU', '', 0.0002, 'g/tonne-km/tonne', 0.0010, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Container ship', '2000-2999 TEU', '', 0.0002, 'g/tonne-km/tonne', 0.0016, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Container ship', '1000-1999 TEU', '', 0.0004, 'g/tonne-km/tonne', 0.0015, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Container ship', '100-999 TEU', '', 0.0002, 'g/tonne-km/tonne', 0.0037, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Container ship', 'Average', '', 0.0004, 'g/tonne-km/tonne', 0.0007, 'g/tonne-km/tonne'],
            
            // UK Vehicle transport
            ['UK', 'Cargo ship', 'Vehicle transport', '6000+ CEU', '', 0.0004, 'g/tonne-km/tonne', 0.0016, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Vehicle transport', '0-5999 CEU', '', 0.0007, 'g/tonne-km/tonne', 0.0026, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Vehicle transport', 'Average', '', 0.0004, 'g/tonne-km/tonne', 0.0017, 'g/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Large RoRo ferry', 'Average', '', 0.0004, 'g/tonne-km/tonne', 0.0017, 'g/tonne-km/tonne'],
            
            // US data - Weight-Distance Basis
            ['US', 'Medium-Duty Truck', '', '', '', 0.0015, 'g/short ton-mile', 0.0047, 'g/short ton-mile'],
            ['US', 'Heavy-Duty Truck', '', '', '', 0.0015, 'g/short ton-mile', 0.0047, 'g/short ton-mile'],
            ['US', 'Rail', '', '', '', 0.0017, 'g/short ton-mile', 0.0006, 'g/short ton-mile'],
            ['US', 'Waterborne Craft', '', '', '', 0.0359, 'g/short ton-mile', 0.0021, 'g/short ton-mile'],
            ['US', 'Air', '', '', '', 0.0006, 'g/short ton-mile', 0.0027, 'g/short ton-mile'],
            
            // US data - Vehicle-Distance Basis
            ['US', 'Medium-Duty Truck', 'Vehicle-Distance Basis', '', '', 0.011, 'g/vehicle-mile', 0.035, 'g/vehicle-mile'],
            ['US', 'Heavy-Duty Truck', 'Vehicle-Distance Basis', '', '', 0.011, 'g/vehicle-mile', 0.035, 'g/vehicle-mile'],
            ['US', 'Passenger Car', 'Vehicle-Distance Basis', '', '', 0.005, 'g/vehicle-mile', 0.003, 'g/vehicle-mile'],
            ['US', 'Light-Duty Truck', 'Vehicle-Distance Basis', '', '', 0.020, 'g/vehicle-mile', 0.023, 'g/vehicle-mile']
        ];

        try {
            DB::beginTransaction();
            
            // Get the freight sector
            $sector = GhgProtocolSector::where('name', 'freight')->first();
            if (!$sector) {
                $this->error('Freight sector not found. Please run import:freight-transport first.');
                return 1;
            }
            
            // Get greenhouse gases
            $ch4_gas = GreenhouseGas::where('chemical_formula', 'CH4')->first();
            $n2o_gas = GreenhouseGas::where('chemical_formula', 'N2O')->first();
            
            if (!$ch4_gas || !$n2o_gas) {
                $this->error('CH4 or N2O gas records not found. Please ensure greenhouse gases are seeded.');
                return 1;
            }
            
            // Get or create measurement units
            $unit_g_tonne_km = MeasurementUnit::firstOrCreate([
                'name' => 'g/tonne-km/tonne'
            ], [
                'symbol' => 'g/tonne-km/tonne',
                'description' => 'Grams per tonne-kilometer per tonne',
                'unit_type' => 'emission_factor'
            ]);
            
            $unit_g_short_ton_mile = MeasurementUnit::firstOrCreate([
                'name' => 'g/short ton-mile'
            ], [
                'symbol' => 'g/short ton-mile',
                'description' => 'Grams per short ton-mile',
                'unit_type' => 'emission_factor'
            ]);
            
            $unit_g_vehicle_mile = MeasurementUnit::firstOrCreate([
                'name' => 'g/vehicle-mile'
            ], [
                'symbol' => 'g/vehicle-mile',
                'description' => 'Grams per vehicle-mile',
                'unit_type' => 'emission_factor'
            ]);
            
            $imported_count = 0;
            
            foreach ($freight_ch4_n2o_data as $row) {
                [$region_name, $vehicle_type, $vehicle_class, $weight_class, $fuel, $ch4_ef, $ch4_unit, $n2o_ef, $n2o_unit] = $row;
                
                // Get region
                $region_code = $region_name === 'US' ? 'US' : 'UK';
                $region = GhgProtocolRegion::where('code', $region_code)->first();
                
                if (!$region) {
                    $this->error("Region {$region_code} not found.");
                    continue;
                }
                
                // Find or create activity
                $activity_name = $vehicle_type;
                if ($vehicle_class && $vehicle_class !== 'Vehicle-Distance Basis') {
                    $activity_name .= " - {$vehicle_class}";
                }
                if ($fuel) {
                    $activity_name .= " - {$fuel}";
                }
                
                $activity = GhgProtocolActivity::where('name', $activity_name)
                    ->where('sector_id', $sector->id)
                    ->first();
                
                if (!$activity) {
                    // Create shorter code for database constraint
                    $short_code = strtoupper(str_replace([' ', '-', '(', ')', ',', '.', '/'], ['_', '_', '', '', '_', '', '_'], $activity_name));
                    $short_code = substr($short_code, 0, 45);
                    
                    $activity = GhgProtocolActivity::create([
                        'name' => $activity_name,
                        'code' => $short_code,
                        'sector_id' => $sector->id,
                        'description' => "Freight transport via {$vehicle_type}" . 
                                       ($vehicle_class ? " ({$vehicle_class})" : "") . 
                                       ($fuel ? " using {$fuel}" : ""),
                        'activity_type' => 'freight_transport',
                        'fuel_type' => $fuel ?: null,
                        'vehicle_type' => $vehicle_type,
                        'applicable_scopes' => [3],
                        'calculation_methods' => ['weight_distance_based', 'vehicle_distance_based'],
                        'is_active' => true,
                        'sort_order' => 0
                    ]);
                }
                
                // Determine the correct unit
                $unit_id = $unit_g_tonne_km->id; // default
                if (str_contains($ch4_unit, 'short ton-mile')) {
                    $unit_id = $unit_g_short_ton_mile->id;
                } elseif (str_contains($ch4_unit, 'vehicle-mile')) {
                    $unit_id = $unit_g_vehicle_mile->id;
                }
                
                // Create CH4 emission factor
                GhgProtocolEmissionFactor::create([
                    'code' => "{$region_name}-FREIGHT-{$vehicle_type}-" . ($vehicle_class ? $vehicle_class : 'DEFAULT') . "-CH4",
                    'name' => "Freight Transport CH4 Factor - {$vehicle_type} ({$region_name})",
                    'description' => "CH4 emission factor for freight transport via {$vehicle_type} in {$region_name}" . 
                                   ($vehicle_class ? " - {$vehicle_class}" : "") . 
                                   ($weight_class ? " ({$weight_class})" : "") .
                                   ($fuel ? " using {$fuel}" : ""),
                    'sector_id' => $sector->id,
                    'activity_id' => $activity->id,
                    'region_id' => $region->id,
                    'gas_id' => $ch4_gas->id,
                    'calculation_method' => str_contains($ch4_unit, 'vehicle-mile') ? 'Vehicle distance-based emission factor' : 'Weight-distance based emission factor',
                    'methodology_reference' => 'GHG Protocol Freight Transport Tool',
                    'factor_value' => $ch4_ef,
                    'ch4_factor' => $ch4_ef,
                    'input_unit_id' => $unit_id,
                    'output_unit_id' => $unit_id,
                    'data_quality_rating' => 'High',
                    'data_source' => 'GHG Protocol',
                    'data_source_detail' => 'Table 2. CH4 and N2O Emission Factors by Weight or Vehicle Distance (i.e., Freight Transport)',
                    'metadata' => [
                        'vehicle_type' => $vehicle_type,
                        'vehicle_class' => $vehicle_class,
                        'weight_class' => $weight_class,
                        'fuel_type' => $fuel,
                        'gas_type' => 'CH4',
                        'table_source' => 'Table 2 - Freight Transport CH4/N2O',
                        'region' => $region_name,
                        'calculation_basis' => str_contains($ch4_unit, 'vehicle-mile') ? 'vehicle_distance' : 'weight_distance'
                    ],
                    'is_default' => false,
                    'is_active' => true,
                    'tier_level' => 2,
                    'priority' => 100
                ]);
                
                // Create N2O emission factor
                GhgProtocolEmissionFactor::create([
                    'code' => "{$region_name}-FREIGHT-{$vehicle_type}-" . ($vehicle_class ? $vehicle_class : 'DEFAULT') . "-N2O",
                    'name' => "Freight Transport N2O Factor - {$vehicle_type} ({$region_name})",
                    'description' => "N2O emission factor for freight transport via {$vehicle_type} in {$region_name}" . 
                                   ($vehicle_class ? " - {$vehicle_class}" : "") . 
                                   ($weight_class ? " ({$weight_class})" : "") .
                                   ($fuel ? " using {$fuel}" : ""),
                    'sector_id' => $sector->id,
                    'activity_id' => $activity->id,
                    'region_id' => $region->id,
                    'gas_id' => $n2o_gas->id,
                    'calculation_method' => str_contains($n2o_unit, 'vehicle-mile') ? 'Vehicle distance-based emission factor' : 'Weight-distance based emission factor',
                    'methodology_reference' => 'GHG Protocol Freight Transport Tool',
                    'factor_value' => $n2o_ef,
                    'n2o_factor' => $n2o_ef,
                    'input_unit_id' => $unit_id,
                    'output_unit_id' => $unit_id,
                    'data_quality_rating' => 'High',
                    'data_source' => 'GHG Protocol',
                    'data_source_detail' => 'Table 2. CH4 and N2O Emission Factors by Weight or Vehicle Distance (i.e., Freight Transport)',
                    'metadata' => [
                        'vehicle_type' => $vehicle_type,
                        'vehicle_class' => $vehicle_class,
                        'weight_class' => $weight_class,
                        'fuel_type' => $fuel,
                        'gas_type' => 'N2O',
                        'table_source' => 'Table 2 - Freight Transport CH4/N2O',
                        'region' => $region_name,
                        'calculation_basis' => str_contains($n2o_unit, 'vehicle-mile') ? 'vehicle_distance' : 'weight_distance'
                    ],
                    'is_default' => false,
                    'is_active' => true,
                    'tier_level' => 2,
                    'priority' => 100
                ]);
                
                $imported_count += 2; // CH4 + N2O
                $this->info("Imported CH4/N2O: {$vehicle_type}" . ($vehicle_class ? " - {$vehicle_class}" : "") . " ({$region_name})");
            }
            
            DB::commit();
            
            $this->info("\nSuccessfully imported {$imported_count} freight transport CH4/N2O emission factors.");
            $this->info("Total CH4/N2O records created: {$imported_count}");
            
        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Error importing data: " . $e->getMessage());
            return 1;
        }

        $this->info("\nImport completed successfully!");
        $this->info("Freight transport CH4/N2O data covers:");
        $this->info("- Air freight (domestic, short-haul, long-haul)");
        $this->info("- Rail freight");
        $this->info("- Road freight (vans, HGVs by weight class)");
        $this->info("- Sea freight (tankers, cargo ships, container ships)");
        $this->info("- Both weight-distance and vehicle-distance based factors");
        $this->info("- Complete CH4 and N2O emission factors for all transport modes");
        return 0;
    }
}
