<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\MeasurementUnit;
use Illuminate\Support\Facades\DB;

class ImportMobileCombustionUK extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:mobile-combustion-uk';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Mobile Combustion CO2, CH4, N2O Emission Factors for U.K.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of UK Mobile Combustion CO2, CH4, N2O Emission Factors...');
        
        // UK mobile combustion data from Table 2
        // Format: [Vehicle Type, Size/Year, Fuel, CO2_EF, CH4_EF, N2O_EF]
        $uk_data = [
            // Passenger Cars
            ['Passenger Car', 'Medium car, 1.4 - 2.0 litres', 'Petrol', 0.174, 0.0126, 0.0074],
            ['Passenger Car', 'Large car, >2.0 litres', 'Petrol', 0.273, 0.0198, 0.0116],
            ['Passenger Car', 'Average', 'Petrol', 0.193, 0.0140, 0.0082],
            ['Passenger Car', 'Medium car, 1.7 - 2.0 litres', 'Diesel', 0.165, 0.00077, 0.0063],
            ['Passenger Car', 'Large car, >2.0 litres', 'Diesel', 0.237, 0.00077, 0.0090],
            ['Passenger Car', 'Average', 'Diesel', 0.171, 0.00077, 0.0065],
            ['Passenger Car', 'Small car, <1.4 litre', 'Hybrid', 0.109, 0.0094, 0.0029],
            ['Passenger Car', 'Medium car, 1.4 - 2.0 litres', 'Hybrid', 0.119, 0.0094, 0.0032],
            ['Passenger Car', 'Large car, >2.0 litres', 'Hybrid', 0.168, 0.0094, 0.0045],
            ['Passenger Car', 'Average', 'Hybrid', 0.119, 0.0094, 0.0032],
            ['Passenger Car', 'Medium car, 1.4 - 2.0 litres', 'CNG', 0.156, 0.0032, 0.0014],
            ['Passenger Car', 'Large car, >2.0 litres', 'CNG', 0.195, 0.0032, 0.0018],
            ['Passenger Car', 'Average', 'CNG', 0.173, 0.0032, 0.0014],
            ['Passenger Car', 'Large car, >2.0 litres', 'LPG', 0.178, 0.0003, 0.0014],
            ['Passenger Car', 'Average', 'LPG', 0.178, 0.0003, 0.0014],
            
            // Vans
            ['Van', 'Class II (1.25 to 1.74 tonnes)', 'Petrol', 0.157, 0.0000, 0.0014],
            ['Van', 'Class III (1.74 to 3.5 tonnes)', 'Petrol', 0.187, 0.0000, 0.0016],
            ['Van', 'Average (up to 3.5 tonnes)', 'Petrol', 0.213, 0.0196, 0.0104],
            ['Van', 'Average (up to 3.5 tonnes)', 'Diesel', 0.251, 0.0196, 0.0104],
            ['Van', 'Average (up to 3.5 tonnes)', 'CNG', 0.230, 0.0472, 0.0019],
            ['Van', 'Average (3.5 to 7.5 tonnes)', 'Diesel', 0.225, 0.0004, 0.0061],
            ['Van', 'Average (7.5 to 17 tonnes)', 'Diesel', 0.225, 0.0004, 0.0061],
            
            // Motorbike
            ['Motorbike', 'Medium, >125 and 500 cc', 'All fuels', 0.098, 0.0618, 0.0029],
            ['Motorbike', 'Large, >500 cc', 'All fuels', 0.113, 0.0452, 0.0020],
            
            // HGV - Rigid
            ['HGV - Rigid', '3.5 - 7.5 tonnes', '0% Weight Laden', 'Diesel', 0.447, 0.004, 0.021],
            ['HGV - Rigid', '3.5 - 7.5 tonnes', '50% Weight Laden', 'Diesel', 0.488, 0.004, 0.023],
            ['HGV - Rigid', '3.5 - 7.5 tonnes', '100% Weight Laden', 'Diesel', 0.529, 0.004, 0.025],
            ['HGV - Rigid', '3.5 - 7.5 tonnes', 'Average Laden', 'Diesel', 0.490, 0.004, 0.023],
            ['HGV - Rigid', '7.5 - 17 tonnes', '0% Weight Laden', 'Diesel', 0.533, 0.0048, 0.0245],
            ['HGV - Rigid', '7.5 - 17 tonnes', '50% Weight Laden', 'Diesel', 0.647, 0.0048, 0.0297],
            ['HGV - Rigid', '7.5 - 17 tonnes', '100% Weight Laden', 'Diesel', 0.697, 0.0048, 0.0320],
            ['HGV - Rigid', '7.5 - 17 tonnes', 'Average Laden', 'Diesel', 0.598, 0.0048, 0.0275],
            ['HGV - Rigid', '>17 tonnes', '0% Weight Laden', 'Diesel', 0.686, 0.009, 0.0400],
            ['HGV - Rigid', '>17 tonnes', '50% Weight Laden', 'Diesel', 1.069, 0.009, 0.0400],
            ['HGV - Rigid', '>17 tonnes', '100% Weight Laden', 'Diesel', 1.069, 0.009, 0.0400],
            ['HGV - Rigid', '>17 tonnes', 'Average Laden', 'Diesel', 0.814, 0.009, 0.0400],
            
            // HGV - Articulated
            ['HGV - Articulated', '3.5 - 33 tonnes', '0% Weight Laden', 'Diesel', 0.753, 0.0044, 0.0356],
            ['HGV - Articulated', '3.5 - 33 tonnes', '50% Weight Laden', 'Diesel', 0.794, 0.0044, 0.0375],
            ['HGV - Articulated', '3.5 - 33 tonnes', '100% Weight Laden', 'Diesel', 0.794, 0.0044, 0.0375],
            ['HGV - Articulated', '3.5 - 33 tonnes', 'Average Laden', 'Diesel', 0.756, 0.0344, 0.0356],
            ['HGV - Articulated', '>33 tonnes', '0% Weight Laden', 'Diesel', 0.743, 0.0062, 0.0341],
            ['HGV - Articulated', '>33 tonnes', '50% Weight Laden', 'Diesel', 1.030, 0.0062, 0.0473],
            ['HGV - Articulated', '>33 tonnes', '100% Weight Laden', 'Diesel', 1.030, 0.0062, 0.0473],
            ['HGV - Articulated', '>33 tonnes', 'Average Laden', 'Diesel', 0.868, 0.0062, 0.0399],
            ['HGV - 1 gas breakdown', 'Average', 'Average Laden', 'Diesel', 0.600, 0.0056, 0.0320]
        ];

        try {
            DB::beginTransaction();
            
            // Get or create the mobile combustion sector
            $sector = GhgProtocolSector::firstOrCreate([
                'name' => 'Mobile Combustion'
            ], [
                'code' => 'MOBILE',
                'description' => 'Mobile combustion sources including vehicles, aircraft, ships, etc.',
                'scope' => 'Scope 1'
            ]);
            
            // Get or create UK region
            $uk_region = GhgProtocolRegion::firstOrCreate([
                'name' => 'United Kingdom'
            ], [
                'code' => 'UK',
                'description' => 'United Kingdom of Great Britain and Northern Ireland'
            ]);
            
            // Get or create measurement units
            $unit_gmi = MeasurementUnit::firstOrCreate([
                'name' => 'g/mi'
            ], [
                'symbol' => 'g/mi',
                'description' => 'Grams per mile',
                'unit_type' => 'emission_factor'
            ]);
            
            $imported_count = 0;
            
            foreach ($uk_data as $row) {
                [$vehicle_type, $size_year, $fuel_type, $co2_ef, $ch4_ef, $n2o_ef] = $row;
                
                // Get or create activity
                $activity_name = "{$vehicle_type} - {$fuel_type}";
                if ($size_year && $size_year !== 'Average') {
                    $activity_name .= " ({$size_year})";
                }
                
                $activity = GhgProtocolActivity::firstOrCreate([
                    'name' => $activity_name,
                    'sector_id' => $sector->id
                ], [
                    'code' => strtoupper(str_replace([' ', '-', '(', ')', ',', '.'], ['_', '_', '', '', '_', ''], $activity_name)),
                    'description' => "Mobile combustion from {$vehicle_type} using {$fuel_type}" . 
                                   ($size_year ? " - {$size_year}" : ""),
                    'scope' => 'Scope 1'
                ]);
                
                // Create CO2 emission factor
                GhgProtocolEmissionFactor::create([
                    'code' => "UK-{$vehicle_type}-{$fuel_type}-{$size_year}-CO2",
                    'name' => "UK Mobile Combustion CO2 Factor - {$vehicle_type} {$fuel_type}",
                    'description' => "CO2 emission factor for {$vehicle_type} using {$fuel_type} in UK" . 
                                   ($size_year ? " ({$size_year})" : ""),
                    'sector_id' => $sector->id,
                    'activity_id' => $activity->id,
                    'region_id' => $uk_region->id,
                    'gas_id' => 1, // Assuming CO2 is gas_id 1
                    'calculation_method' => 'Vehicle distance-based emission factor',
                    'methodology_reference' => 'GHG Protocol Mobile Combustion Tool',
                    'factor_value' => $co2_ef,
                    'co2_factor' => $co2_ef,
                    'input_unit_id' => $unit_gmi->id,
                    'output_unit_id' => $unit_gmi->id,
                    'data_quality_rating' => 'High',
                    'data_source' => 'GHG Protocol',
                    'data_source_detail' => 'Table 2. CO2, CH4, N2O Emission Factors for U.K. by Vehicle Distance',
                    'metadata' => [
                        'vehicle_type' => $vehicle_type,
                        'size_year' => $size_year,
                        'fuel_type' => $fuel_type,
                        'gas_type' => 'CO2',
                        'table_source' => 'Table 2',
                        'region' => 'UK'
                    ],
                    'is_default' => false,
                    'is_active' => true,
                    'tier_level' => 2,
                    'priority' => 100
                ]);
                
                // Create CH4 emission factor
                GhgProtocolEmissionFactor::create([
                    'code' => "UK-{$vehicle_type}-{$fuel_type}-{$size_year}-CH4",
                    'name' => "UK Mobile Combustion CH4 Factor - {$vehicle_type} {$fuel_type}",
                    'description' => "CH4 emission factor for {$vehicle_type} using {$fuel_type} in UK" . 
                                   ($size_year ? " ({$size_year})" : ""),
                    'sector_id' => $sector->id,
                    'activity_id' => $activity->id,
                    'region_id' => $uk_region->id,
                    'gas_id' => 2, // Assuming CH4 is gas_id 2
                    'calculation_method' => 'Vehicle distance-based emission factor',
                    'methodology_reference' => 'GHG Protocol Mobile Combustion Tool',
                    'factor_value' => $ch4_ef,
                    'ch4_factor' => $ch4_ef,
                    'input_unit_id' => $unit_gmi->id,
                    'output_unit_id' => $unit_gmi->id,
                    'data_quality_rating' => 'High',
                    'data_source' => 'GHG Protocol',
                    'data_source_detail' => 'Table 2. CO2, CH4, N2O Emission Factors for U.K. by Vehicle Distance',
                    'metadata' => [
                        'vehicle_type' => $vehicle_type,
                        'size_year' => $size_year,
                        'fuel_type' => $fuel_type,
                        'gas_type' => 'CH4',
                        'table_source' => 'Table 2',
                        'region' => 'UK'
                    ],
                    'is_default' => false,
                    'is_active' => true,
                    'tier_level' => 2,
                    'priority' => 100
                ]);
                
                // Create N2O emission factor
                GhgProtocolEmissionFactor::create([
                    'code' => "UK-{$vehicle_type}-{$fuel_type}-{$size_year}-N2O",
                    'name' => "UK Mobile Combustion N2O Factor - {$vehicle_type} {$fuel_type}",
                    'description' => "N2O emission factor for {$vehicle_type} using {$fuel_type} in UK" . 
                                   ($size_year ? " ({$size_year})" : ""),
                    'sector_id' => $sector->id,
                    'activity_id' => $activity->id,
                    'region_id' => $uk_region->id,
                    'gas_id' => 3, // Assuming N2O is gas_id 3
                    'calculation_method' => 'Vehicle distance-based emission factor',
                    'methodology_reference' => 'GHG Protocol Mobile Combustion Tool',
                    'factor_value' => $n2o_ef,
                    'n2o_factor' => $n2o_ef,
                    'input_unit_id' => $unit_gmi->id,
                    'output_unit_id' => $unit_gmi->id,
                    'data_quality_rating' => 'High',
                    'data_source' => 'GHG Protocol',
                    'data_source_detail' => 'Table 2. CO2, CH4, N2O Emission Factors for U.K. by Vehicle Distance',
                    'metadata' => [
                        'vehicle_type' => $vehicle_type,
                        'size_year' => $size_year,
                        'fuel_type' => $fuel_type,
                        'gas_type' => 'N2O',
                        'table_source' => 'Table 2',
                        'region' => 'UK'
                    ],
                    'is_default' => false,
                    'is_active' => true,
                    'tier_level' => 2,
                    'priority' => 100
                ]);
                
                $imported_count += 3; // CO2 + CH4 + N2O factors
                $this->info("Imported: {$vehicle_type} - {$fuel_type} ({$size_year})");
            }
            
            DB::commit();
            
            $this->info("\nSuccessfully imported {$imported_count} UK mobile combustion emission factors.");
            $this->info("Total records created:");
            $this->info("- Emission factors: {$imported_count}");
            $this->info("- Activities: " . ($imported_count / 3));
            
        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Error importing data: " . $e->getMessage());
            return 1;
        }

        $this->info("\nImport completed successfully!");
        return 0;
    }
}
