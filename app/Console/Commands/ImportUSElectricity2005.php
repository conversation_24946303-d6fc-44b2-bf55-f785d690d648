<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\MeasurementUnit;
use Illuminate\Support\Facades\DB;

class ImportUSElectricity2005 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:us-electricity-2005';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import 2005 US Regional Electricity Emission Factors from EPA eGRID';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of 2005 US Regional Electricity Emission Factors...');
        
        // 2005 US Regional Electricity Emission Factors Data (Table 12)
        $data_2005 = [
            ['ASCC - Alaska Grid', 1232.36, 25.60, 6.51],
            ['ASCC - Miscellaneous', 498.86, 20.75, 4.08],
            ['ASCC - Subregion unknown', 1089.794, 24.658, 6.035],
            ['ERCOT - All', 1324.35, 18.65, 15.11],
            ['FRCC - All', 1318.57, 45.92, 16.94],
            ['HICC Miscellaneous', 1514.92, 314.68, 46.88],
            ['HICC Oahu', 1811.98, 109.47, 23.62],
            ['HICC - Subregion unknown', 1731.0139, 165.4006, 29.9583],
            ['MRO East', 1854.72, 27.59, 30.36],
            ['MRO West', 1821.84, 28.00, 30.71],
            ['MRO - Subregion unknown', 1823.6892, 27.9413, 30.6564],
            ['NPCC Long Island', 1536.80, 115.41, 18.09],
            ['NPCC New England', 927.68, 86.49, 17.01],
            ['NPCC NYC/Westchester', 815.45, 36.02, 5.46],
            ['NPCC Upstate NY', 720.80, 24.82, 11.19],
            ['NPCC - Subregion unknown', 875.7404, 60.5589, 13.5497],
            ['RFC East', 1139.07, 30.27, 18.71],
            ['RFC Michigan', 1563.28, 33.93, 27.17],
            ['RFC West', 1537.82, 18.23, 25.71],
            ['RFC - Subregion unknown', 1427.2099, 23.1934, 23.8684],
            ['SERC Midwest', 1830.51, 21.15, 30.50],
            ['SERC Mississippi Valley', 1019.74, 24.31, 11.71],
            ['SERC South', 1489.54, 26.27, 25.47],
            ['SERC Tennessee Valley', 1510.44, 20.05, 25.64],
            ['SERC Virginia/Carolina', 1134.88, 23.77, 19.79],
            ['SERC - Subregion unknown', 1368.8544, 23.3186, 22.5418],
            ['SPP North', 1960.94, 23.82, 32.09],
            ['SPP South', 1658.14, 24.98, 22.61],
            ['SPP - Subregion unknown', 1751.3721, 24.6209, 25.5241],
            ['WECC California', 724.12, 30.24, 8.08],
            ['WECC Northwest', 902.24, 19.13, 14.90],
            ['WECC Rockies', 1883.08, 22.88, 28.75],
            ['WECC Southwest', 1311.05, 17.45, 17.94],
            ['WECC - Subregion unknown', 1033.1239, 22.6178, 14.7686]
        ];

        try {
            DB::beginTransaction();
            
            // Get or create the electricity sector
            $sector = GhgProtocolSector::firstOrCreate([
                'name' => 'Electricity'
            ], [
                'code' => 'ELEC',
                'description' => 'Electricity generation and consumption',
                'scope' => 'Scope 2'
            ]);
            
            // Get or create the electricity activity
            $activity = GhgProtocolActivity::firstOrCreate([
                'name' => 'Electricity Consumption',
                'sector_id' => $sector->id
            ], [
                'code' => 'ELEC-CONS',
                'description' => 'Electricity consumption from grid',
                'scope' => 'Scope 2'
            ]);
            
            // Get or create US region
            $region = GhgProtocolRegion::firstOrCreate([
                'name' => 'United States'
            ], [
                'code' => 'US',
                'description' => 'United States of America'
            ]);
            
            // Get lb/MWh unit
            $unit = MeasurementUnit::firstOrCreate([
                'name' => 'lb/MWh'
            ], [
                'symbol' => 'lb/MWh',
                'description' => 'Pounds per Megawatt hour',
                'unit_type' => 'emission_factor'
            ]);
            
            $imported_count = 0;
            
            foreach ($data_2005 as $row) {
                [$region_name, $co2_rate, $ch4_rate, $n2o_rate] = $row;
                
                // Create emission factor record
                $emission_factor = GhgProtocolEmissionFactor::create([
                    'code' => "US-ELEC-{$region_name}-2005",
                    'name' => "US Electricity - {$region_name} (2005)",
                    'description' => "2005 US Regional Electricity Emission Factors for {$region_name}",
                    'sector_id' => $sector->id,
                    'activity_id' => $activity->id,
                    'region_id' => $region->id,
                    'gas_id' => 1, // Assuming CO2 is gas_id 1, this is a multi-gas factor
                    'calculation_method' => 'eGRID subregion annual output emission rates',
                    'methodology_reference' => 'EPA eGRID 2005',
                    'factor_value' => $co2_rate, // Primary factor is CO2
                    'co2_factor' => $co2_rate,
                    'ch4_factor' => $ch4_rate,
                    'n2o_factor' => $n2o_rate,
                    'input_unit_id' => $unit->id,
                    'output_unit_id' => $unit->id,
                    'data_quality_rating' => 'High',
                    'vintage_year' => 2005,
                    'valid_from' => '2005-01-01',
                    'valid_until' => '2005-12-31',
                    'data_source' => 'EPA eGRID',
                    'data_source_detail' => 'EPA eGRID 2005 - Year 2005 US Regional Electricity Emission Factors for CO2, CH4 and N2O',
                    'metadata' => [
                        'egrid_subregion' => $region_name,
                        'table_source' => 'Table 12. Year 2005 US Regional Electricity Emission Factors for CO2, CH4 and N2O'
                    ],
                    'is_default' => false,
                    'is_active' => true,
                    'tier_level' => 2,
                    'priority' => 100
                ]);
                
                $imported_count++;
                $this->info("Imported: {$region_name}");
            }
            
            DB::commit();
            
            $this->info("\nSuccessfully imported {$imported_count} emission factors for 2005 US Regional Electricity.");
            $this->info("Total records created:");
            $this->info("- Emission factors: {$imported_count}");
            $this->info("- Emission factor values: " . ($imported_count * 3) . " (CO2, CH4, N2O for each region)");
            $this->info("- Metadata records: " . ($imported_count * 2));
            
        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Error importing data: " . $e->getMessage());
            return 1;
        }

        $this->info("\nImport completed successfully!");
        return 0;
    }
}
