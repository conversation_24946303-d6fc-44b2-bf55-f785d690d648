<?php

namespace App\Console\Commands;

use App\Models\Organization;
use App\Services\ScienceBasedTargetsService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GenerateSBTiTargetsCommand extends Command
{
    protected $signature = 'targets:generate-sbti 
                            {organization? : Organization ID to generate targets for}
                            {--scenario=1.5C : Temperature scenario (1.5C, 2C, well_below_2C)}
                            {--base-year=2019 : Base year for target calculation}
                            {--target-year=2030 : Target year}
                            {--all : Generate targets for all organizations}
                            {--dry-run : Run without saving targets}';

    protected $description = 'Generate Science-Based Targets (SBTi) recommendations';

    protected ScienceBasedTargetsService $sbtiService;

    public function __construct(ScienceBasedTargetsService $sbtiService)
    {
        parent::__construct();
        $this->sbtiService = $sbtiService;
    }

    public function handle(): int
    {
        $this->info('🎯 Starting SBTi target generation...');

        $organizationId = $this->argument('organization');
        $scenario = $this->option('scenario');
        $baseYear = (int) $this->option('base-year');
        $targetYear = (int) $this->option('target-year');
        $generateAll = $this->option('all');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No targets will be saved');
        }

        try {
            if ($generateAll) {
                $this->info("📊 Generating SBTi targets for all organizations");
                $results = $this->generateForAllOrganizations($scenario, $baseYear, $targetYear, $dryRun);
            } elseif ($organizationId) {
                $this->info("📊 Generating SBTi targets for organization: {$organizationId}");
                $results = $this->generateForOrganization($organizationId, $scenario, $baseYear, $targetYear, $dryRun);
            } else {
                $this->error('❌ Please specify an organization ID or use --all flag');
                return 1;
            }

            $this->displayResults($results);

            $this->info('✅ SBTi target generation completed successfully');
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ SBTi target generation failed: {$e->getMessage()}");
            Log::error('SBTi target generation command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }

    protected function generateForOrganization(string $organizationId, string $scenario, int $baseYear, int $targetYear, bool $dryRun): array
    {
        $organization = Organization::find($organizationId);
        
        if (!$organization) {
            throw new \InvalidArgumentException("Organization not found: {$organizationId}");
        }

        $this->line("  📍 Organization: {$organization->name}");
        $this->line("  🌡️ Scenario: {$scenario}");
        $this->line("  📅 Base Year: {$baseYear}");
        $this->line("  🎯 Target Year: {$targetYear}");

        if ($dryRun) {
            return [
                'organization_id' => $organizationId,
                'organization_name' => $organization->name,
                'dry_run' => true,
                'message' => 'Dry run - would generate SBTi targets',
            ];
        }

        $options = [
            'scenario' => $scenario,
            'base_year' => $baseYear,
            'target_year' => $targetYear,
        ];

        $recommendations = $this->sbtiService->generateSBTiRecommendations($organization, $options);

        if (!$recommendations['success']) {
            return [
                'organization_id' => $organizationId,
                'organization_name' => $organization->name,
                'success' => false,
                'error' => $recommendations['error'],
            ];
        }

        // Save targets if not dry run
        $savedTargets = $this->saveTargets($organization, $recommendations);

        return [
            'organization_id' => $organizationId,
            'organization_name' => $organization->name,
            'success' => true,
            'recommendations' => $recommendations,
            'saved_targets' => $savedTargets,
        ];
    }

    protected function generateForAllOrganizations(string $scenario, int $baseYear, int $targetYear, bool $dryRun): array
    {
        $organizations = Organization::all();
        
        if ($organizations->isEmpty()) {
            throw new \RuntimeException('No organizations found');
        }

        $this->line("  📊 Found {$organizations->count()} organization(s) to process");

        $results = [
            'total_organizations' => $organizations->count(),
            'successful' => 0,
            'failed' => 0,
            'organizations' => [],
        ];

        if ($dryRun) {
            $results['dry_run'] = true;
            $results['message'] = 'Dry run - would generate SBTi targets for all organizations';
            return $results;
        }

        foreach ($organizations as $organization) {
            try {
                $orgResult = $this->generateForOrganization($organization->id, $scenario, $baseYear, $targetYear, false);
                $results['organizations'][] = $orgResult;
                
                if ($orgResult['success']) {
                    $results['successful']++;
                } else {
                    $results['failed']++;
                }

            } catch (\Exception $e) {
                $this->warn("  ⚠️ Failed to generate targets for {$organization->name}: {$e->getMessage()}");
                $results['failed']++;
                $results['organizations'][] = [
                    'organization_id' => $organization->id,
                    'organization_name' => $organization->name,
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    protected function saveTargets(Organization $organization, array $recommendations): array
    {
        $savedTargets = [];
        
        foreach ($recommendations['targets'] as $scope => $target) {
            $targetData = [
                'organization_id' => $organization->id,
                'target_name' => "SBTi {$target['scope_coverage']} Target",
                'description' => "Science-based target for {$target['scope_coverage']} aligned with {$recommendations['scenario']} scenario",
                'temperature_scenario' => $recommendations['scenario'],
                'base_year' => $recommendations['baseline']['base_year'],
                'target_year' => $target['milestones'] ? max(array_keys($target['milestones'])) : 2030,
                'baseline_emissions' => $target['baseline'],
                'target_emissions' => $target['target'],
                'reduction_percentage' => $target['reduction_percentage'],
                'scope_coverage' => explode(' + ', str_replace('Scope ', '', $target['scope_coverage'])),
                'yearly_milestones' => $target['milestones'],
                'sector_pathway' => $recommendations['sector_pathway']['sector'] ?? 'general',
                'is_sbti_validated' => false,
                'status' => 'draft',
                'methodology_details' => [
                    'pathway' => $recommendations['sector_pathway'],
                    'validation' => $recommendations['validation'],
                ],
            ];

            $savedTarget = \App\Models\ScienceBasedTarget::create($targetData);
            $savedTargets[] = $savedTarget;
        }

        return $savedTargets;
    }

    protected function displayResults(array $results): void
    {
        $this->info('📈 SBTi Target Generation Results:');
        
        if (isset($results['dry_run']) && $results['dry_run']) {
            $this->warn("  🧪 {$results['message']}");
            return;
        }

        if (isset($results['organization_id'])) {
            // Single organization results
            $this->line("  📍 Organization: {$results['organization_name']}");
            $this->line("  📊 Status: " . ($results['success'] ? 'Success' : 'Failed'));
            
            if ($results['success']) {
                $recommendations = $results['recommendations'];
                $this->line("  🌡️ Scenario: {$recommendations['scenario']}");
                $this->line("  📋 Baseline Emissions: " . number_format($recommendations['baseline']['emissions']['total'], 2) . " tCO2e");
                
                $this->info('  🎯 Generated Targets:');
                foreach ($recommendations['targets'] as $scope => $target) {
                    $this->line("    {$target['scope_coverage']}: {$target['reduction_percentage']}% reduction");
                }
                
                $this->info('  ✅ SBTi Validation:');
                $validation = $recommendations['validation'];
                if ($validation['is_compliant']) {
                    $this->line("    Status: SBTi Compliant");
                } else {
                    $this->line("    Status: Not SBTi Compliant");
                    foreach ($validation['criteria_failed'] as $failure) {
                        $this->line("    - {$failure}");
                    }
                }

                if (isset($results['saved_targets'])) {
                    $this->line("  💾 Saved Targets: " . count($results['saved_targets']));
                }
            } else {
                $this->error("  ❌ Error: {$results['error']}");
            }
        } else {
            // Multiple organizations results
            $this->line("  📊 Total Organizations: {$results['total_organizations']}");
            $this->line("  ✅ Successful: {$results['successful']}");
            $this->line("  ❌ Failed: {$results['failed']}");

            if (!empty($results['organizations'])) {
                $this->info('  📋 Organization Details:');
                foreach (array_slice($results['organizations'], 0, 5) as $org) {
                    $status = $org['success'] ? '✅' : '❌';
                    $this->line("    {$status} {$org['organization_name']}");
                }
                
                if (count($results['organizations']) > 5) {
                    $remaining = count($results['organizations']) - 5;
                    $this->line("    ... and {$remaining} more organizations");
                }
            }
        }

        // Show summary statistics
        if (isset($results['organizations'])) {
            $successful = array_filter($results['organizations'], fn($org) => $org['success']);
            
            if (!empty($successful)) {
                $this->info('  📊 Success Summary:');
                $totalBaseline = 0;
                $totalTargetReduction = 0;
                
                foreach ($successful as $org) {
                    if (isset($org['recommendations']['baseline']['emissions']['total'])) {
                        $totalBaseline += $org['recommendations']['baseline']['emissions']['total'];
                    }
                }
                
                if ($totalBaseline > 0) {
                    $this->line("    Total Baseline Emissions: " . number_format($totalBaseline, 2) . " tCO2e");
                }
            }
        }
    }
}
