<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\MeasurementUnit;
use App\Models\GreenhouseGas;
use Illuminate\Support\Facades\DB;

class ImportTable2CH4N2OFactors extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:table2-ch4-n2o-factors';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Table 2: CH4 and N2O Emission Factors by Fuel and Vehicle Type';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of Table 2: CH4 and N2O Emission Factors by Fuel and Vehicle Type...');
        
        // Table 2 data: [Region, Fuel, Transport_Type, Vehicle_Engine_Type, CH4_EF, CH4_Unit, N2O_EF, N2O_Unit]
        $table2_data = [
            // Other regions
            ['Other', 'Sub-bituminous Coal', 'Rail', '', 2, 'kg/TJ', 1.5, 'kg/TJ'],
            ['Other', 'Diesel', 'Rail', '', 4.15, 'kg/TJ', 28.6, 'kg/TJ'],
            ['Other', 'Diesel', 'Agriculture Equipment', '', 4.15, 'kg/TJ', 28.6, 'kg/TJ'],
            ['Other', 'Diesel', 'Industrial Equipment', '', 4.15, 'kg/TJ', 28.6, 'kg/TJ'],
            ['Other', 'Diesel', 'Industry Equipment', '', 4.15, 'kg/TJ', 28.6, 'kg/TJ'],
            ['Other', 'Diesel', 'Household Equipment', '', 4.15, 'kg/TJ', 28.6, 'kg/TJ'],
            ['Other', 'Motor Gasoline', 'Agriculture Equipment', '4 stroke', 80, 'kg/TJ', 2, 'kg/TJ'],
            ['Other', 'Motor Gasoline', 'Industry Equipment', '4 stroke', 80, 'kg/TJ', 2, 'kg/TJ'],
            ['Other', 'Motor Gasoline', 'Forestry Equipment', '2 stroke', 140, 'kg/TJ', 2, 'kg/TJ'],
            ['Other', 'Motor Gasoline', 'Agriculture Equipment', '2 stroke', 140, 'kg/TJ', 0.4, 'kg/TJ'],
            ['Other', 'Motor Gasoline', 'Forestry Equipment', '2 stroke', 170, 'kg/TJ', 0.4, 'kg/TJ'],
            ['Other', 'Motor Gasoline', 'Industry Equipment', '2 stroke', 130, 'kg/TJ', 0.4, 'kg/TJ'],
            ['Other', 'Motor Gasoline', 'Household Equipment', '2 stroke', 130, 'kg/TJ', 0.4, 'kg/TJ'],
            ['Other', 'Motor Gasoline', 'Household Equipment', '4 stroke', 80, 'kg/TJ', 2, 'kg/TJ'],
            
            // US data
            ['US', 'Motor Gasoline', 'Ship and Boat', '2 stroke', 4.64, 'g/US Gallon', 0.08, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Ship and Boat', '4 stroke', 2.36, 'g/US Gallon', 0.01, 'g/US Gallon'],
            ['US', 'Diesel Fuel', 'Ship and Boat', '', 6.41, 'g/US Gallon', 0.27, 'g/US Gallon'],
            ['US', 'Diesel Fuel', 'Locomotives', '', 59, 'g/US Gallon', 0.38, 'g/US Gallon'],
            ['US', 'Jet Fuel', 'Aircraft', '', 0, 'g/US Gallon', 0.3, 'g/US Gallon'],
            ['US', 'Aviation Gasoline', 'Aircraft', '', 7.06, 'g/US Gallon', 0.11, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Agricultural Equipment', '2 stroke', 6.02, 'g/US Gallon', 0.47, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Agricultural Equipment', '4 stroke', 1.94, 'g/US Gallon', 1.21, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Agricultural Equipment', 'Off-Road Trucks', 1.04, 'g/US Gallon', 1.2, 'g/US Gallon'],
            ['US', 'Diesel Fuel', 'Agricultural Equipment', '', 1.12, 'g/US Gallon', 1.12, 'g/US Gallon'],
            ['US', 'Diesel Fuel', 'Agricultural Equipment', 'Off-Road Trucks', 0.91, 'g/US Gallon', 0.56, 'g/US Gallon'],
            ['US', 'LPG', 'Agricultural Equipment', '', 0.33, 'g/US Gallon', 0.95, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Construction Equipment', '2 stroke', 7.98, 'g/US Gallon', 0.12, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Construction Equipment', '4 stroke', 2.85, 'g/US Gallon', 1.47, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Construction Equipment', 'Off-Road Trucks', 2.85, 'g/US Gallon', 1.47, 'g/US Gallon'],
            ['US', 'Diesel Fuel', 'Construction Equipment', '', 1.01, 'g/US Gallon', 0.94, 'g/US Gallon'],
            ['US', 'Diesel Fuel', 'Construction Equipment', 'Off-Road Trucks', 1.01, 'g/US Gallon', 0.94, 'g/US Gallon'],
            ['US', 'LPG', 'Construction Equipment', '', 0.59, 'g/US Gallon', 0.05, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Lawn and Garden Equipment', '2 stroke', 7.36, 'g/US Gallon', 0.31, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Lawn and Garden Equipment', '4 stroke', 3.00, 'g/US Gallon', 1.49, 'g/US Gallon'],
            ['US', 'Diesel Fuel', 'Lawn and Garden Equipment', '', 0.66, 'g/US Gallon', 0.49, 'g/US Gallon'],
            ['US', 'LPG', 'Lawn and Garden Equipment', '', 0.41, 'g/US Gallon', 0.02, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Airport Equipment', '', 1.02, 'g/US Gallon', 1.07, 'g/US Gallon'],
            ['US', 'Diesel', 'Airport Equipment', '', 1.86, 'g/US Gallon', 1.16, 'g/US Gallon'],
            ['US', 'LPG', 'Airport Equipment', '', 0.35, 'g/US Gallon', 0.89, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Industrial/Commercial Equipment', '2 stroke', 7.73, 'g/US Gallon', 0.5, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Industrial/Commercial Equipment', '4 stroke', 3.44, 'g/US Gallon', 1.25, 'g/US Gallon'],
            ['US', 'Diesel', 'Industrial/Commercial Equipment', '', 0.42, 'g/US Gallon', 0.6, 'g/US Gallon'],
            ['US', 'LPG', 'Industrial/Commercial Equipment', '', 0.64, 'g/US Gallon', 0.84, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Logging Equipment', '2 stroke', 3.68, 'g/US Gallon', 0, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Logging Equipment', '4 stroke', 5.04, 'g/US Gallon', 2.00, 'g/US Gallon'],
            ['US', 'Diesel', 'Logging Equipment', '', 0.36, 'g/US Gallon', 1.27, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Railroad Equipment', '', 3.24, 'g/US Gallon', 1.81, 'g/US Gallon'],
            ['US', 'Diesel', 'Railroad Equipment', '', 0.4, 'g/US Gallon', 0.95, 'g/US Gallon'],
            ['US', 'LPG', 'Railroad Equipment', '', 2.03, 'g/US Gallon', 0.02, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Recreational Equipment', '2 stroke', 3.8, 'g/US Gallon', 0.11, 'g/US Gallon'],
            ['US', 'Motor Gasoline', 'Recreational Equipment', '4 stroke', 2.72, 'g/US Gallon', 1.48, 'g/US Gallon'],
            ['US', 'Diesel', 'Recreational Equipment', '', 0.73, 'g/US Gallon', 0.66, 'g/US Gallon'],
            ['US', 'LPG', 'Recreational Equipment', '', 0.43, 'g/US Gallon', 0.51, 'g/US Gallon']
        ];

        try {
            DB::beginTransaction();
            
            // Get or create mobile combustion sector
            $sector = GhgProtocolSector::firstOrCreate([
                'name' => 'mobile_combustion'
            ], [
                'code' => 'MOBILE_COMB',
                'description' => 'Mobile Combustion Sources',
                'scope_category' => 'Scope 1',
                'is_active' => true,
                'sort_order' => 1
            ]);
            
            // Get greenhouse gases
            $ch4_gas = GreenhouseGas::where('chemical_formula', 'CH4')->first();
            $n2o_gas = GreenhouseGas::where('chemical_formula', 'N2O')->first();
            
            if (!$ch4_gas || !$n2o_gas) {
                $this->error('CH4 or N2O gas records not found. Please ensure greenhouse gases are seeded.');
                return 1;
            }
            
            // Get or create measurement units
            $unit_kg_tj = MeasurementUnit::firstOrCreate([
                'name' => 'kg/TJ'
            ], [
                'symbol' => 'kg/TJ',
                'description' => 'Kilograms per terajoule',
                'unit_type' => 'emission_factor'
            ]);
            
            $unit_g_us_gallon = MeasurementUnit::firstOrCreate([
                'name' => 'g/US Gallon'
            ], [
                'symbol' => 'g/US gal',
                'description' => 'Grams per US gallon',
                'unit_type' => 'emission_factor'
            ]);
            
            $imported_count = 0;
            
            foreach ($table2_data as $row) {
                [$region_name, $fuel_name, $transport_type, $vehicle_engine_type, $ch4_ef, $ch4_unit, $n2o_ef, $n2o_unit] = $row;
                
                // Get region
                $region_code = match($region_name) {
                    'US' => 'US',
                    'Other' => 'GLOBAL'
                };
                
                $region = GhgProtocolRegion::where('code', $region_code)->first();
                if (!$region) {
                    $this->error("Region {$region_code} not found.");
                    continue;
                }
                
                // Create activity name combining transport type and vehicle/engine type
                $activity_parts = array_filter([$transport_type, $vehicle_engine_type, $fuel_name]);
                $activity_name = implode(' - ', $activity_parts);
                
                // Find or create activity
                $activity = GhgProtocolActivity::where('name', $activity_name)
                    ->where('sector_id', $sector->id)
                    ->first();
                
                if (!$activity) {
                    // Create shorter code for database constraint
                    $short_code = strtoupper(str_replace([' ', '-', '(', ')', ',', '.', '/', '%'], ['_', '_', '', '', '_', '', '_', 'PCT'], $activity_name));
                    $short_code = substr($short_code, 0, 45);
                    
                    $activity = GhgProtocolActivity::create([
                        'name' => $activity_name,
                        'code' => $short_code,
                        'sector_id' => $sector->id,
                        'description' => "Mobile combustion - {$activity_name}",
                        'activity_type' => 'mobile_combustion',
                        'fuel_type' => $fuel_name,
                        'transport_type' => $transport_type,
                        'vehicle_engine_type' => $vehicle_engine_type ?: null,
                        'applicable_scopes' => [1],
                        'calculation_methods' => ['fuel_based'],
                        'is_active' => true,
                        'sort_order' => 0
                    ]);
                }
                
                // Determine the correct unit
                $unit_id = match($ch4_unit) {
                    'kg/TJ' => $unit_kg_tj->id,
                    'g/US Gallon' => $unit_g_us_gallon->id,
                    default => $unit_kg_tj->id
                };
                
                // Create CH4 emission factor
                if ($ch4_ef !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "{$region_name}-{$transport_type}-{$fuel_name}-{$vehicle_engine_type}-CH4",
                        'name' => "CH4 Factor - {$activity_name} ({$region_name})",
                        'description' => "CH4 emission factor for {$activity_name} in {$region_name}",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $region->id,
                        'gas_id' => $ch4_gas->id,
                        'calculation_method' => 'Fuel-based emission factor',
                        'methodology_reference' => 'GHG Protocol Mobile Combustion Tool',
                        'factor_value' => $ch4_ef,
                        'ch4_factor' => $ch4_ef,
                        'input_unit_id' => $unit_id,
                        'output_unit_id' => $unit_id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 2. CH4 and N2O Emission Factors by Fuel and Vehicle Type',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'transport_type' => $transport_type,
                            'vehicle_engine_type' => $vehicle_engine_type,
                            'table_source' => 'Table 2 - CH4/N2O by Fuel and Vehicle Type',
                            'region' => $region_name,
                            'calculation_basis' => 'fuel_consumption'
                        ],
                        'is_default' => true,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 300
                    ]);
                    $imported_count++;
                }
                
                // Create N2O emission factor
                if ($n2o_ef !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "{$region_name}-{$transport_type}-{$fuel_name}-{$vehicle_engine_type}-N2O",
                        'name' => "N2O Factor - {$activity_name} ({$region_name})",
                        'description' => "N2O emission factor for {$activity_name} in {$region_name}",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $region->id,
                        'gas_id' => $n2o_gas->id,
                        'calculation_method' => 'Fuel-based emission factor',
                        'methodology_reference' => 'GHG Protocol Mobile Combustion Tool',
                        'factor_value' => $n2o_ef,
                        'n2o_factor' => $n2o_ef,
                        'input_unit_id' => $unit_id,
                        'output_unit_id' => $unit_id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 2. CH4 and N2O Emission Factors by Fuel and Vehicle Type',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'transport_type' => $transport_type,
                            'vehicle_engine_type' => $vehicle_engine_type,
                            'table_source' => 'Table 2 - CH4/N2O by Fuel and Vehicle Type',
                            'region' => $region_name,
                            'calculation_basis' => 'fuel_consumption'
                        ],
                        'is_default' => true,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 300
                    ]);
                    $imported_count++;
                }
                
                $this->info("Imported: {$activity_name} ({$region_name})");
            }
            
            DB::commit();
            
            $this->info("\nSuccessfully imported {$imported_count} CH4/N2O emission factors from Table 2.");
            
        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Error importing data: " . $e->getMessage());
            return 1;
        }

        $this->info("\nImport completed successfully!");
        $this->info("Table 2 CH4/N2O emission factors cover:");
        $this->info("- Equipment types: Rail, Agriculture, Industrial, Forestry, Construction, etc.");
        $this->info("- Engine specifications: 2-stroke, 4-stroke, off-road trucks");
        $this->info("- Fuel types: Motor gasoline, diesel, LPG, jet fuel, aviation gasoline, coal");
        $this->info("- Regional coverage: Global/Other and US specific factors");
        $this->info("- Units: kg/TJ for Other regions, g/US Gallon for US");
        $this->info("- Both CH4 and N2O emission factors for detailed GHG accounting");
        return 0;
    }
}
