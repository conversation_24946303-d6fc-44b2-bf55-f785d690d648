<?php

namespace App\Console\Commands;

use App\Models\Organization;
use App\Services\ScenarioAnalysisService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RunScenarioAnalysisCommand extends Command
{
    protected $signature = 'targets:scenario-analysis 
                            {organization : Organization ID to analyze}
                            {--type=business_growth : Scenario type (business_growth, merger_acquisition, facility_closure, technology_adoption)}
                            {--growth-rate=0.05 : Annual growth rate for business growth scenarios}
                            {--timeframe=10 : Analysis timeframe in years}
                            {--save : Save scenario analysis to database}
                            {--dry-run : Run without saving analysis}';

    protected $description = 'Run scenario analysis for emissions projections';

    protected ScenarioAnalysisService $scenarioService;

    public function __construct(ScenarioAnalysisService $scenarioService)
    {
        parent::__construct();
        $this->scenarioService = $scenarioService;
    }

    public function handle(): int
    {
        $this->info('🔮 Starting scenario analysis...');

        $organizationId = $this->argument('organization');
        $scenarioType = $this->option('type');
        $growthRate = (float) $this->option('growth-rate');
        $timeframe = (int) $this->option('timeframe');
        $save = $this->option('save');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No analysis will be saved');
        }

        try {
            $organization = Organization::find($organizationId);
            
            if (!$organization) {
                $this->error("❌ Organization not found: {$organizationId}");
                return 1;
            }

            $this->line("  📍 Organization: {$organization->name}");
            $this->line("  🔮 Scenario Type: {$scenarioType}");
            $this->line("  📈 Growth Rate: " . ($growthRate * 100) . "%");
            $this->line("  📅 Timeframe: {$timeframe} years");

            if ($dryRun) {
                $this->warn("  🧪 Dry run - would run scenario analysis");
                return 0;
            }

            $scenarios = $this->buildScenarios($scenarioType, $growthRate, $timeframe);
            
            $analysis = $this->scenarioService->runScenarioAnalysis($organization, $scenarios);

            if (!$analysis['success']) {
                $this->error("❌ Scenario analysis failed: {$analysis['error']}");
                return 1;
            }

            $this->displayAnalysis($analysis);

            if ($save) {
                $savedAnalysis = $this->saveScenarioAnalysis($organization, $analysis, $scenarioType);
                $this->info("💾 Scenario analysis saved with ID: {$savedAnalysis->id}");
            }

            $this->info('✅ Scenario analysis completed successfully');
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Scenario analysis failed: {$e->getMessage()}");
            Log::error('Scenario analysis command failed', [
                'organization_id' => $organizationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }

    protected function buildScenarios(string $type, float $growthRate, int $timeframe): array
    {
        switch ($type) {
            case 'business_growth':
                return [
                    'conservative_growth' => [
                        'type' => 'business_growth',
                        'annual_growth_rate' => $growthRate * 0.5,
                        'efficiency_gains' => 0.03, // 3% annual efficiency improvement
                        'timeframe' => $timeframe,
                    ],
                    'moderate_growth' => [
                        'type' => 'business_growth',
                        'annual_growth_rate' => $growthRate,
                        'efficiency_gains' => 0.02, // 2% annual efficiency improvement
                        'timeframe' => $timeframe,
                    ],
                    'aggressive_growth' => [
                        'type' => 'business_growth',
                        'annual_growth_rate' => $growthRate * 1.5,
                        'efficiency_gains' => 0.01, // 1% annual efficiency improvement
                        'timeframe' => $timeframe,
                    ],
                ];

            case 'technology_adoption':
                return [
                    'slow_adoption' => [
                        'type' => 'technology_adoption',
                        'adoption_rate' => 0.3,
                        'efficiency_improvement' => 0.1,
                        'timeframe' => $timeframe,
                    ],
                    'moderate_adoption' => [
                        'type' => 'technology_adoption',
                        'adoption_rate' => 0.6,
                        'efficiency_improvement' => 0.2,
                        'timeframe' => $timeframe,
                    ],
                    'rapid_adoption' => [
                        'type' => 'technology_adoption',
                        'adoption_rate' => 0.9,
                        'efficiency_improvement' => 0.3,
                        'timeframe' => $timeframe,
                    ],
                ];

            default:
                return [
                    'baseline' => [
                        'type' => $type,
                        'timeframe' => $timeframe,
                    ],
                ];
        }
    }

    protected function displayAnalysis(array $analysis): void
    {
        $this->info('📈 Scenario Analysis Results:');
        
        // Baseline information
        $baseline = $analysis['baseline'];
        $this->line("  📊 Baseline ({$baseline['year']}): " . number_format($baseline['emissions']['total'], 2) . " tCO2e");
        $this->line('');

        // Scenario results
        $this->info('  🔮 Scenario Projections:');
        foreach ($analysis['scenarios'] as $scenarioId => $scenario) {
            if (!$scenario['success']) {
                $this->error("    ❌ {$scenarioId}: {$scenario['error']}");
                continue;
            }

            $this->line("    📋 {$scenarioId}:");
            
            if (isset($scenario['projections'])) {
                // Show final year projection
                $finalProjection = end($scenario['projections']);
                $this->line("      Final Year Emissions: " . number_format($finalProjection['emissions'], 2) . " tCO2e");
                $this->line("      Total Change: " . number_format($finalProjection['net_change'], 1) . "%");
                
                // Show key years
                $keyYears = [2025, 2030, 2040, 2050];
                foreach ($keyYears as $year) {
                    if (isset($scenario['projections'][$year])) {
                        $projection = $scenario['projections'][$year];
                        $this->line("      {$year}: " . number_format($projection['emissions'], 2) . " tCO2e (" . 
                                   number_format($projection['net_change'], 1) . "%)");
                    }
                }
            }
            
            $this->line('');
        }

        // Comparison
        if (isset($analysis['comparison'])) {
            $comparison = $analysis['comparison'];
            $this->info('  🏆 Scenario Comparison:');
            $this->line("    Best Case: {$comparison['best_case']}");
            $this->line("    Worst Case: {$comparison['worst_case']}");
            $this->line("    Most Likely: {$comparison['most_likely']}");
            $this->line('');

            if (!empty($comparison['ranking'])) {
                $this->info('  📊 Ranking (by final emissions):');
                foreach ($comparison['ranking'] as $index => $ranking) {
                    $rank = $index + 1;
                    $this->line("    {$rank}. {$ranking['scenario_id']}: " . 
                               number_format($ranking['final_emissions'], 2) . " tCO2e (" . 
                               number_format($ranking['change_from_baseline'], 1) . "%)");
                }
                $this->line('');
            }
        }

        // Recommendations
        if (isset($analysis['recommendations'])) {
            $this->info('  💡 Recommendations:');
            foreach ($analysis['recommendations'] as $recommendation) {
                $this->line("    • {$recommendation}");
            }
        }
    }

    protected function saveScenarioAnalysis(Organization $organization, array $analysis, string $scenarioType): \App\Models\ScenarioAnalysis
    {
        $analysisData = [
            'organization_id' => $organization->id,
            'analysis_name' => "Scenario Analysis - " . ucfirst(str_replace('_', ' ', $scenarioType)),
            'description' => "Scenario analysis for {$scenarioType} with multiple projection scenarios",
            'scenario_type' => $scenarioType,
            'scenario_parameters' => [
                'scenarios' => array_keys($analysis['scenarios']),
                'baseline_year' => $analysis['baseline']['year'],
            ],
            'baseline_emissions' => $analysis['baseline']['emissions']['total'],
            'scenario_results' => $analysis['scenarios'],
            'comparison_analysis' => $analysis['comparison'] ?? null,
            'recommendations' => $analysis['recommendations'] ?? null,
            'analysis_date' => now()->toDateString(),
            'status' => 'draft',
        ];

        $scenarioAnalysis = \App\Models\ScenarioAnalysis::create($analysisData);

        // Save individual scenario projections
        foreach ($analysis['scenarios'] as $scenarioId => $scenario) {
            if (!$scenario['success'] || !isset($scenario['projections'])) {
                continue;
            }

            foreach ($scenario['projections'] as $year => $projection) {
                \App\Models\ScenarioProjection::create([
                    'scenario_analysis_id' => $scenarioAnalysis->id,
                    'scenario_name' => $scenarioId,
                    'projection_year' => $year,
                    'projected_emissions' => $projection['emissions'],
                    'growth_factor' => $projection['growth_factor'] ?? null,
                    'efficiency_factor' => $projection['efficiency_factor'] ?? null,
                    'key_assumptions' => [
                        'net_change' => $projection['net_change'],
                        'scenario_type' => $scenarioType,
                    ],
                ]);
            }
        }

        return $scenarioAnalysis;
    }
}
