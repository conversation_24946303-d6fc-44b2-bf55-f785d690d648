<?php

namespace App\Console\Commands;

use App\Models\Supplier;
use App\Services\StakeholderEngagementService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CreateSupplierPortalCommand extends Command
{
    protected $signature = 'stakeholders:create-supplier-portal 
                            {supplier : Supplier ID to create portal access for}
                            {--access-level=standard : Access level (basic, standard, premium)}
                            {--password= : Custom password (auto-generated if not provided)}
                            {--expires-in=365 : Days until access expires}
                            {--disable-data-submission : Disable data submission feature}
                            {--disable-dashboard : Disable dashboard access}
                            {--enable-reporting : Enable reporting access}
                            {--dry-run : Run without creating portal access}';

    protected $description = 'Create secure supplier portal access with progress dashboards';

    protected StakeholderEngagementService $stakeholderService;

    public function __construct(StakeholderEngagementService $stakeholderService)
    {
        parent::__construct();
        $this->stakeholderService = $stakeholderService;
    }

    public function handle(): int
    {
        $this->info('🏭 Creating supplier portal access...');

        $supplierId = $this->argument('supplier');
        $accessLevel = $this->option('access-level');
        $customPassword = $this->option('password');
        $expiresIn = (int) $this->option('expires-in');
        $disableDataSubmission = $this->option('disable-data-submission');
        $disableDashboard = $this->option('disable-dashboard');
        $enableReporting = $this->option('enable-reporting');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No portal access will be created');
        }

        try {
            $supplier = Supplier::find($supplierId);
            
            if (!$supplier) {
                $this->error("❌ Supplier not found: {$supplierId}");
                return 1;
            }

            $this->line("  📍 Supplier: {$supplier->name}");
            $this->line("  📧 Contact Email: {$supplier->contact_email}");
            $this->line("  🔐 Access Level: {$accessLevel}");
            $this->line("  📅 Expires In: {$expiresIn} days");

            // Validate access level
            if (!in_array($accessLevel, ['basic', 'standard', 'premium'])) {
                $this->error("❌ Invalid access level: {$accessLevel}");
                $this->line("Valid levels: basic, standard, premium");
                return 1;
            }

            // Check if portal access already exists
            $existingAccess = \App\Models\SupplierPortalAccess::where('supplier_id', $supplier->id)
                ->where('is_active', true)
                ->first();

            if ($existingAccess) {
                $this->warn("⚠️  Active portal access already exists for this supplier");
                if (!$this->confirm('Do you want to create additional access?')) {
                    return 0;
                }
            }

            if ($dryRun) {
                $this->displayPortalAccessPlan($supplier, $accessLevel, $expiresIn);
                return 0;
            }

            $options = [
                'access_level' => $accessLevel,
                'password' => $customPassword,
                'expires_at' => now()->addDays($expiresIn),
                'data_submission_enabled' => !$disableDataSubmission,
                'dashboard_access_enabled' => !$disableDashboard,
                'reporting_access_enabled' => $enableReporting,
            ];

            $result = $this->stakeholderService->createSupplierPortalAccess($supplier, $options);

            if (!$result['success']) {
                $this->error("❌ Portal access creation failed: {$result['error']}");
                return 1;
            }

            $this->displayPortalAccessResults($result);
            $this->info('✅ Supplier portal access created successfully');

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Portal access creation failed: {$e->getMessage()}");
            Log::error('Supplier portal access creation failed', [
                'supplier_id' => $supplierId,
                'error' => $e->getMessage(),
            ]);
            return 1;
        }
    }

    protected function displayPortalAccessPlan(Supplier $supplier, string $accessLevel, int $expiresIn): void
    {
        $this->info('📋 Portal Access Plan:');
        
        $this->line('  🏭 Supplier Information:');
        $this->line("    Name: {$supplier->name}");
        $this->line("    Code: {$supplier->supplier_code}");
        $this->line("    Email: {$supplier->contact_email}");
        $this->line("    Industry: {$supplier->industry_sector}");
        $this->line("    Country: {$supplier->country}");
        
        $this->line('');
        $this->line('  🔐 Access Configuration:');
        $this->line("    Access Level: {$accessLevel}");
        $this->line("    Expires In: {$expiresIn} days");
        $this->line("    Portal URL: " . config('app.url') . "/supplier-portal/{$supplier->supplier_code}");
        
        $this->line('');
        $this->line('  ✨ Features to be enabled:');
        
        $features = $this->getAccessLevelFeatures($accessLevel);
        foreach ($features as $feature) {
            $this->line("    ✅ {$feature}");
        }
        
        $this->line('');
        $this->line('  📊 Dashboard Components:');
        $dashboardComponents = [
            'Emissions Summary',
            'Data Submission Status',
            'Assigned Actions',
            'Progress Tracking',
            'Engagement Metrics',
            'Compliance Status',
        ];
        
        foreach ($dashboardComponents as $component) {
            $this->line("    📈 {$component}");
        }
    }

    protected function displayPortalAccessResults(array $result): void
    {
        $portalAccess = $result['portal_access'];
        $credentials = $result['credentials'];
        
        $this->info('🎉 Portal Access Created Successfully!');
        
        $this->line('');
        $this->line('  🔑 Login Credentials:');
        $this->line("    Email: {$credentials['email']}");
        $this->line("    Password: {$credentials['password']}");
        $this->line("    Access Token: {$credentials['access_token']}");
        
        $this->line('');
        $this->line('  🌐 Portal Information:');
        $this->line("    Portal URL: {$result['portal_url']}");
        $this->line("    Access Level: {$portalAccess->access_level}");
        $this->line("    Expires: {$portalAccess->expires_at->format('Y-m-d H:i:s')}");
        
        $this->line('');
        $this->line('  ✨ Enabled Features:');
        $enabledFeatures = $portalAccess->getEnabledFeatures();
        foreach ($enabledFeatures as $feature) {
            $this->line("    ✅ {$feature}");
        }
        
        $this->line('');
        $this->line('  📊 Dashboard Configuration:');
        $dashboard = $result['dashboard'];
        $this->line("    Dashboard Name: {$dashboard->dashboard_name}");
        $this->line("    Role: {$dashboard->role_name}");
        $this->line("    Status: " . ($dashboard->is_active ? 'Active' : 'Inactive'));
        
        $this->line('');
        $this->info('📧 Next Steps:');
        $this->line('  1. Welcome email sent to supplier contact');
        $this->line('  2. Supplier can log in using provided credentials');
        $this->line('  3. Dashboard is ready for supplier engagement');
        $this->line('  4. Data submission portal is available');
        
        $this->line('');
        $this->line('🔧 Management Commands:');
        $this->line("  - View access: php artisan stakeholders:view-supplier-access {$portalAccess->supplier_id}");
        $this->line("  - Extend access: php artisan stakeholders:extend-supplier-access {$portalAccess->id} --days=90");
        $this->line("  - Revoke access: php artisan stakeholders:revoke-supplier-access {$portalAccess->id}");
        
        $this->line('');
        $this->warn('🔒 Security Note:');
        $this->line('  Store the access credentials securely and share them through secure channels only.');
    }

    protected function getAccessLevelFeatures(string $accessLevel): array
    {
        return match($accessLevel) {
            'basic' => [
                'Data Submission Portal',
                'Basic Progress Dashboard',
                'Action Item Viewing',
            ],
            'standard' => [
                'Data Submission Portal',
                'Comprehensive Dashboard',
                'Action Item Management',
                'Progress Tracking',
                'Engagement Metrics',
            ],
            'premium' => [
                'Data Submission Portal',
                'Advanced Dashboard',
                'Action Item Management',
                'Progress Tracking',
                'Engagement Metrics',
                'Reporting Access',
                'Benchmarking Data',
                'Advanced Analytics',
            ],
            default => ['Basic Access'],
        };
    }
}
