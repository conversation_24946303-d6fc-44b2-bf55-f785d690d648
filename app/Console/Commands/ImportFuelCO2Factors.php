<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\MeasurementUnit;
use App\Models\GreenhouseGas;
use Illuminate\Support\Facades\DB;

class ImportFuelCO2Factors extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:fuel-co2-factors';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import CO2 Emission Factors by Fuel for Mobile Combustion Sources';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of CO2 Emission Factors by Fuel...');
        
        // CO2 emission factors by fuel from Table 1
        // Format: [Region, Fuel, Fossil_CO2_EF, Biogenic_CO2_EF, EF_Unit]
        $fuel_co2_data = [
            // Other regions
            ['Other', 'Jet Kerosene', 2.57, null, 'kg/L'],
            ['Other', 'Aviation Gasoline', 2.18, null, 'kg/L'],
            ['Other', 'Motor Gasoline/Petrol', 2.29, null, 'kg/L'],
            ['Other', 'On-Road Diesel Fuel', 2.61, null, 'kg/L'],
            ['Other', 'Residual Fuel Oil', 3.17, null, 'kg/L'],
            ['Other', 'Liquefied Petroleum Gases (LPG)', 1.47, null, 'kg/L'],
            ['Other', 'Compressed Natural Gas (CNG)', 1.88, null, 'kg/m³'],
            
            // US
            ['US', 'Kerosene - Type Jet Fuel', 9.75, null, 'kg/US Gallon'],
            ['US', 'Aviation Gasoline', 8.31, null, 'kg/US Gallon'],
            ['US', 'Motor Gasoline', 8.78, null, 'kg/US Gallon'],
            ['US', 'Diesel Fuel', 10.21, null, 'kg/US Gallon'],
            ['US', 'Residual Fuel Oil', 11.27, null, 'kg/US Gallon'],
            ['US', 'Liquefied Petroleum Gases (LPG)', 5.68, null, 'kg/US Gallon'],
            ['US', 'Compressed Natural Gas (CNG)', 0.054, null, 'kg/scf'],
            ['US', 'Liquefied Natural Gas (LNG)', 4.50, null, 'kg/US Gallon'],
            ['US', 'Ethanol (100%)', null, 5.75, 'kg/US Gallon'],
            ['US', 'E85 Ethanol/Gasoline', null, 0.85, 'kg/US Gallon'],
            ['US', 'E10 Ethanol/Gasoline', 1.32, 4.89, 'kg/US Gallon'],
            ['US', 'B20 Biodiesel/Diesel', 8.17, 1.69, 'kg/US Gallon'],
            
            // UK
            ['UK', 'Aviation spirit (Aviation Gasoline)', 2.283, null, 'kg/L'],
            ['UK', 'Aviation turbine fuel (Jet Fuel)', 2.520, null, 'kg/L'],
            ['UK', 'Burning oil (Kerosene)', 2.520, null, 'kg/L'],
            ['UK', 'Fuel oil (Residual Fuel Oil)', 3.163, null, 'kg/L'],
            ['UK', 'Petrol (100% mineral petrol) (Motor Gasoline)', 2.331, null, 'kg/L'],
            ['UK', 'Processed fuel oils - residual oil', 3.163, null, 'kg/L'],
            ['UK', 'Compressed Natural Gas (CNG)', 0.448, null, 'kg/L'],
            ['UK', 'Liquefied Natural Gas (LNG)', 1.158, null, 'kg/L'],
            ['UK', 'Liquefied Petroleum Gases (LPG)', 1.555, null, 'kg/L'],
            ['UK', 'Gas oil (100% mineral blend)', 2.650, null, 'kg/m³'],
            ['UK', 'Bioethanol', null, 1.52, 'kg/L'],
            ['UK', 'Biodiesel ME', null, 2.39, 'kg/L'],
            ['UK', 'E85 Ethanol/Gasoline', 0.350, 1.292, 'kg/L'],
            ['UK', 'B20 Biodiesel/Diesel', 2.101, 0.478, 'kg/L']
        ];

        try {
            DB::beginTransaction();
            
            // Get or create mobile combustion sector
            $sector = GhgProtocolSector::firstOrCreate([
                'name' => 'mobile_combustion'
            ], [
                'code' => 'MOBILE_COMB',
                'description' => 'Mobile Combustion Sources',
                'scope_category' => 'Scope 1',
                'is_active' => true,
                'sort_order' => 1
            ]);
            
            // Get CO2 gas
            $co2_gas = GreenhouseGas::where('chemical_formula', 'CO2')->first();
            if (!$co2_gas) {
                $this->error('CO2 gas record not found. Please ensure greenhouse gases are seeded.');
                return 1;
            }
            
            // Get or create measurement units
            $unit_kg_l = MeasurementUnit::firstOrCreate([
                'name' => 'kg/L'
            ], [
                'symbol' => 'kg/L',
                'description' => 'Kilograms per liter',
                'unit_type' => 'emission_factor'
            ]);
            
            $unit_kg_us_gallon = MeasurementUnit::firstOrCreate([
                'name' => 'kg/US Gallon'
            ], [
                'symbol' => 'kg/US gal',
                'description' => 'Kilograms per US gallon',
                'unit_type' => 'emission_factor'
            ]);
            
            $unit_kg_m3 = MeasurementUnit::firstOrCreate([
                'name' => 'kg/m³'
            ], [
                'symbol' => 'kg/m³',
                'description' => 'Kilograms per cubic meter',
                'unit_type' => 'emission_factor'
            ]);
            
            $unit_kg_scf = MeasurementUnit::firstOrCreate([
                'name' => 'kg/scf'
            ], [
                'symbol' => 'kg/scf',
                'description' => 'Kilograms per standard cubic foot',
                'unit_type' => 'emission_factor'
            ]);
            
            $imported_count = 0;
            
            foreach ($fuel_co2_data as $row) {
                [$region_name, $fuel_name, $fossil_co2, $biogenic_co2, $unit_name] = $row;
                
                // Get region
                $region_code = match($region_name) {
                    'US' => 'US',
                    'UK' => 'UK',
                    'Other' => 'GLOBAL'
                };
                
                $region = GhgProtocolRegion::where('code', $region_code)->first();
                if (!$region) {
                    $this->error("Region {$region_code} not found.");
                    continue;
                }
                
                // Find or create activity for this fuel type
                $activity_name = "Fuel Combustion - {$fuel_name}";
                $activity = GhgProtocolActivity::where('name', $activity_name)
                    ->where('sector_id', $sector->id)
                    ->first();
                
                if (!$activity) {
                    // Create shorter code for database constraint
                    $short_code = strtoupper(str_replace([' ', '-', '(', ')', ',', '.', '/', '%'], ['_', '_', '', '', '_', '', '_', 'PCT'], $fuel_name));
                    $short_code = substr($short_code, 0, 45);
                    
                    $activity = GhgProtocolActivity::create([
                        'name' => $activity_name,
                        'code' => $short_code,
                        'sector_id' => $sector->id,
                        'description' => "Mobile combustion of {$fuel_name}",
                        'activity_type' => 'fuel_combustion',
                        'fuel_type' => $fuel_name,
                        'applicable_scopes' => [1],
                        'calculation_methods' => ['fuel_based'],
                        'is_active' => true,
                        'sort_order' => 0
                    ]);
                }
                
                // Determine the correct unit
                $unit_id = match($unit_name) {
                    'kg/L' => $unit_kg_l->id,
                    'kg/US Gallon' => $unit_kg_us_gallon->id,
                    'kg/m³' => $unit_kg_m3->id,
                    'kg/scf' => $unit_kg_scf->id,
                    default => $unit_kg_l->id
                };
                
                // Create fossil CO2 emission factor if present
                if ($fossil_co2 !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "{$region_name}-FUEL-{$fuel_name}-FOSSIL-CO2",
                        'name' => "Fossil CO2 Factor - {$fuel_name} ({$region_name})",
                        'description' => "Fossil CO2 emission factor for {$fuel_name} combustion in {$region_name}",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $region->id,
                        'gas_id' => $co2_gas->id,
                        'calculation_method' => 'Fuel-based emission factor',
                        'methodology_reference' => 'GHG Protocol Mobile Combustion Tool',
                        'factor_value' => $fossil_co2,
                        'co2_factor' => $fossil_co2,
                        'input_unit_id' => $unit_id,
                        'output_unit_id' => $unit_id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 1. CO2 Emission Factors by Fuel',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'co2_type' => 'fossil',
                            'table_source' => 'Table 1 - CO2 by Fuel',
                            'region' => $region_name,
                            'calculation_basis' => 'fuel_consumption'
                        ],
                        'is_default' => true,
                        'is_active' => true,
                        'tier_level' => 1,
                        'priority' => 200
                    ]);
                    $imported_count++;
                }
                
                // Create biogenic CO2 emission factor if present
                if ($biogenic_co2 !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "{$region_name}-FUEL-{$fuel_name}-BIOGENIC-CO2",
                        'name' => "Biogenic CO2 Factor - {$fuel_name} ({$region_name})",
                        'description' => "Biogenic CO2 emission factor for {$fuel_name} combustion in {$region_name}",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $region->id,
                        'gas_id' => $co2_gas->id,
                        'calculation_method' => 'Fuel-based emission factor',
                        'methodology_reference' => 'GHG Protocol Mobile Combustion Tool',
                        'factor_value' => $biogenic_co2,
                        'co2_factor' => $biogenic_co2,
                        'input_unit_id' => $unit_id,
                        'output_unit_id' => $unit_id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 1. CO2 Emission Factors by Fuel',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'co2_type' => 'biogenic',
                            'table_source' => 'Table 1 - CO2 by Fuel',
                            'region' => $region_name,
                            'calculation_basis' => 'fuel_consumption'
                        ],
                        'is_default' => false,
                        'is_active' => true,
                        'tier_level' => 1,
                        'priority' => 150
                    ]);
                    $imported_count++;
                }
                
                $this->info("Imported: {$fuel_name} ({$region_name})");
            }
            
            DB::commit();
            
            $this->info("\nSuccessfully imported {$imported_count} fuel-based CO2 emission factors.");
            
        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Error importing data: " . $e->getMessage());
            return 1;
        }

        $this->info("\nImport completed successfully!");
        $this->info("Fuel-based CO2 emission factors cover:");
        $this->info("- Fossil fuels: Jet fuel, gasoline, diesel, residual fuel oil, LPG, CNG, LNG");
        $this->info("- Biofuels: Ethanol, biodiesel, and blended fuels (E85, E10, B20)");
        $this->info("- Regional variations: Global/Other, US, and UK specific factors");
        $this->info("- Both fossil and biogenic CO2 components where applicable");
        $this->info("- Appropriate units: kg/L, kg/US Gallon, kg/m³, kg/scf");
        return 0;
    }
}
