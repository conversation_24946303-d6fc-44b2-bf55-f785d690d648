<?php

namespace App\Console\Commands;

use App\Services\ERPIntegrationService;
use App\Services\DataLakeService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SyncERPDataCommand extends Command
{
    protected $signature = 'data:sync-erp 
                            {--system= : ERP system to sync (sap, oracle, all)}
                            {--facility= : Specific facility ID to sync}
                            {--from-date= : Start date for data sync (Y-m-d)}
                            {--to-date= : End date for data sync (Y-m-d)}
                            {--dry-run : Run without making changes}';

    protected $description = 'Sync data from ERP systems (SAP, Oracle, etc.)';

    protected ERPIntegrationService $erpService;
    protected DataLakeService $dataLakeService;

    public function __construct(ERPIntegrationService $erpService, DataLakeService $dataLakeService)
    {
        parent::__construct();
        $this->erpService = $erpService;
        $this->dataLakeService = $dataLakeService;
    }

    public function handle(): int
    {
        $this->info('🔄 Starting ERP data synchronization...');

        $system = $this->option('system') ?? 'all';
        $facilityId = $this->option('facility');
        $fromDate = $this->option('from-date') ? Carbon::parse($this->option('from-date')) : now()->subMonth();
        $toDate = $this->option('to-date') ? Carbon::parse($this->option('to-date')) : now();
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        try {
            // Test connections first
            $this->info('🔍 Testing ERP connections...');
            $connectionResults = $this->testConnections($system);
            
            if (empty($connectionResults['active'])) {
                $this->error('❌ No active ERP connections found');
                return 1;
            }

            $this->info("✅ Found {$connectionResults['count']} active connection(s)");

            // Sync data
            if ($facilityId) {
                $this->info("📊 Syncing data for facility: {$facilityId}");
                $results = $this->syncFacilityData($facilityId, $dryRun);
            } else {
                $this->info("📊 Syncing data for date range: {$fromDate->format('Y-m-d')} to {$toDate->format('Y-m-d')}");
                $results = $this->syncDateRangeData($fromDate, $toDate, $dryRun);
            }

            // Display results
            $this->displayResults($results);

            $this->info('✅ ERP data synchronization completed successfully');
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ ERP sync failed: {$e->getMessage()}");
            Log::error('ERP sync command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }

    protected function testConnections(string $system): array
    {
        $systems = $system === 'all' ? ['sap', 'oracle'] : [$system];
        $active = [];
        
        foreach ($systems as $sys) {
            $result = $this->erpService->testConnection($sys);
            if ($result['status'] === 'connected') {
                $active[] = $sys;
                $this->line("  ✅ {$sys}: {$result['message']}");
            } else {
                $this->line("  ❌ {$sys}: {$result['message']}");
            }
        }

        return ['active' => $active, 'count' => count($active)];
    }

    protected function syncFacilityData(string $facilityId, bool $dryRun): array
    {
        if ($dryRun) {
            return [
                'facility_id' => $facilityId,
                'records_synced' => 0,
                'dry_run' => true,
                'message' => 'Dry run - would sync facility data'
            ];
        }

        return $this->erpService->syncEnergyData($facilityId);
    }

    protected function syncDateRangeData(Carbon $fromDate, Carbon $toDate, bool $dryRun): array
    {
        if ($dryRun) {
            return [
                'from_date' => $fromDate->format('Y-m-d'),
                'to_date' => $toDate->format('Y-m-d'),
                'records_synced' => 0,
                'dry_run' => true,
                'message' => 'Dry run - would sync date range data'
            ];
        }

        $data = $this->erpService->pullActivityData($fromDate, $toDate);
        
        return [
            'from_date' => $fromDate->format('Y-m-d'),
            'to_date' => $toDate->format('Y-m-d'),
            'records_synced' => $data->count(),
            'data' => $data->take(5)->toArray(), // Show first 5 records
        ];
    }

    protected function displayResults(array $results): void
    {
        $this->info('📈 Synchronization Results:');
        
        if (isset($results['dry_run']) && $results['dry_run']) {
            $this->warn("  🧪 {$results['message']}");
            return;
        }

        if (isset($results['facility_id'])) {
            $this->line("  📍 Facility: {$results['facility_id']}");
        }

        if (isset($results['from_date']) && isset($results['to_date'])) {
            $this->line("  📅 Date Range: {$results['from_date']} to {$results['to_date']}");
        }

        $recordCount = $results['records_synced'] ?? 0;
        $this->line("  📊 Records Synced: {$recordCount}");

        if (isset($results['data']) && !empty($results['data'])) {
            $this->info('  📋 Sample Records:');
            foreach (array_slice($results['data'], 0, 3) as $index => $record) {
                $this->line("    " . ($index + 1) . ". " . json_encode($record, JSON_PRETTY_PRINT));
            }
        }

        // Show data lake statistics
        $stats = $this->dataLakeService->getStatistics();
        $this->info('  🏗️ Data Lake Stats:');
        $this->line("    Total Records: {$stats['total_records']}");
        $this->line("    Total Size: " . number_format($stats['total_size_mb'], 2) . " MB");
        $this->line("    Recent Activity (30 days): {$stats['recent_activity']}");
    }
}
