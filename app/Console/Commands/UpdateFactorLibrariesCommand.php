<?php

namespace App\Console\Commands;

use App\Services\ExternalFactorLibraryService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateFactorLibrariesCommand extends Command
{
    protected $signature = 'factors:update 
                            {--source= : Specific source to update (egrid, defra, spend)}
                            {--year= : Year for data import}
                            {--dry-run : Run without making changes}';

    protected $description = 'Update external emission factor libraries (eGRID, DEFRA, spend-based)';

    protected ExternalFactorLibraryService $factorService;

    public function __construct(ExternalFactorLibraryService $factorService)
    {
        parent::__construct();
        $this->factorService = $factorService;
    }

    public function handle(): int
    {
        $this->info('🔄 Starting factor library updates...');

        $source = $this->option('source');
        $year = $this->option('year') ? (int) $this->option('year') : null;
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        try {
            if ($source) {
                $this->info("📊 Updating specific source: {$source}");
                $results = $this->updateSpecificSource($source, $year, $dryRun);
            } else {
                $this->info("📊 Updating all factor libraries");
                $results = $this->updateAllSources($dryRun);
            }

            $this->displayResults($results);

            $this->info('✅ Factor library updates completed successfully');
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Factor library update failed: {$e->getMessage()}");
            Log::error('Factor library update command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }

    protected function updateSpecificSource(string $source, ?int $year, bool $dryRun): array
    {
        if ($dryRun) {
            return [
                'success' => true,
                'source' => $source,
                'dry_run' => true,
                'message' => "Dry run - would update {$source} factor library",
            ];
        }

        switch ($source) {
            case 'egrid':
                return $this->factorService->importEGridFactors($year);
            case 'defra':
                return $this->factorService->importDEFRAFactors($year);
            case 'spend':
                return $this->factorService->importSpendBasedFactors();
            default:
                throw new \InvalidArgumentException("Unknown source: {$source}");
        }
    }

    protected function updateAllSources(bool $dryRun): array
    {
        if ($dryRun) {
            return [
                'success' => true,
                'dry_run' => true,
                'message' => 'Dry run - would update all factor libraries',
                'sources' => ['egrid', 'defra', 'spend'],
            ];
        }

        return $this->factorService->updateAllFactorLibraries();
    }

    protected function displayResults(array $results): void
    {
        $this->info('📈 Update Results:');
        
        if (isset($results['dry_run']) && $results['dry_run']) {
            $this->warn("  🧪 {$results['message']}");
            return;
        }

        if (isset($results['source'])) {
            // Single source results
            $this->line("  📍 Source: {$results['source']}");
            $this->line("  📊 Status: " . ($results['success'] ? 'Success' : 'Failed'));
            
            if ($results['success']) {
                $imported = $results['imported'] ?? 0;
                $errors = count($results['errors'] ?? []);
                $this->line("  📋 Imported: {$imported}");
                $this->line("  ❌ Errors: {$errors}");
                
                if ($errors > 0) {
                    $this->warn("  ⚠️ Import Errors:");
                    foreach (array_slice($results['errors'], 0, 5) as $error) {
                        $this->line("    - {$error}");
                    }
                    if (count($results['errors']) > 5) {
                        $remaining = count($results['errors']) - 5;
                        $this->line("    ... and {$remaining} more errors");
                    }
                }
            } else {
                $this->error("  ❌ Error: {$results['error']}");
            }
        } else {
            // Multiple sources results
            $this->line("  📊 Sources Updated: {$results['sources_updated']}/{$results['total_sources']}");
            $this->line("  📋 Total Imported: {$results['total_imported']}");
            $this->line("  ❌ Total Errors: {$results['total_errors']}");

            if (!empty($results['details'])) {
                $this->info('  📋 Source Details:');
                foreach ($results['details'] as $source => $detail) {
                    $status = $detail['success'] ? '✅' : '❌';
                    $imported = $detail['imported'] ?? 0;
                    $this->line("    {$status} {$source}: {$imported} factors imported");
                }
            }
        }

        // Show factor library statistics
        $stats = $this->factorService->getFactorLibraryStats();
        $this->info('  🏗️ Factor Library Stats:');
        $this->line("    GHG Protocol Factors: " . number_format($stats['ghg_protocol_factors']));
        $this->line("    Electricity Grid Factors: " . number_format($stats['electricity_grid_factors']));
        $this->line("    Spend-based Factors: " . number_format($stats['spend_emission_factors']));
        
        if (!empty($stats['last_updated'])) {
            $this->info('  📅 Last Updated:');
            foreach ($stats['last_updated'] as $type => $date) {
                if ($date) {
                    $this->line("    {$type}: {$date}");
                }
            }
        }
    }
}
