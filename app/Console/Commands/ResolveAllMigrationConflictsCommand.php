<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ResolveAllMigrationConflictsCommand extends Command
{
    protected $signature = 'migrations:resolve-conflicts
                            {--force : Force resolution of conflicts}
                            {--dry-run : Show what would be done without making changes}';

    protected $description = 'Resolve all migration conflicts by marking existing tables as migrated';

    public function handle(): int
    {
        $this->info('🔧 Resolving migration conflicts...');

        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        try {
            // Get all pending migrations
            $pendingMigrations = $this->getPendingMigrations();

            if (empty($pendingMigrations)) {
                $this->info('✅ No pending migrations found');
                return 0;
            }

            $this->line("Found {count($pendingMigrations)} pending migrations:");
            foreach ($pendingMigrations as $migration) {
                $this->line("  - {$migration}");
            }

            // Check which tables already exist
            $existingTables = $this->getExistingTables();
            $conflictingMigrations = $this->findConflictingMigrations($pendingMigrations, $existingTables);

            if (empty($conflictingMigrations)) {
                $this->info('✅ No conflicting migrations found');

                if (!$dryRun) {
                    $this->info('Running remaining migrations...');
                    $this->call('migrate', ['--force' => true]);
                }

                return 0;
            }

            $this->warn("Found {count($conflictingMigrations)} conflicting migrations:");
            foreach ($conflictingMigrations as $migration => $tables) {
                $this->line("  - {$migration}: " . implode(', ', $tables));
            }

            if (!$force && !$dryRun) {
                if (!$this->confirm('Do you want to mark these migrations as run?')) {
                    $this->info('Operation cancelled');
                    return 0;
                }
            }

            if (!$dryRun) {
                // Mark conflicting migrations as run
                $this->markMigrationsAsRun($conflictingMigrations);

                // Run remaining migrations
                $this->info('Running remaining migrations...');
                $this->call('migrate', ['--force' => true]);
            }

            $this->info('✅ Migration conflicts resolved successfully');
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Failed to resolve conflicts: {$e->getMessage()}");
            return 1;
        }
    }

    protected function getPendingMigrations(): array
    {
        // Get migrations directly from database
        $ranMigrations = DB::table('migrations')->pluck('migration')->toArray();

        // Get all migration files
        $migrationFiles = glob(database_path('migrations/*.php'));
        $allMigrations = [];

        foreach ($migrationFiles as $file) {
            $filename = basename($file, '.php');
            $allMigrations[] = $filename;
        }

        // Find pending migrations
        $pending = array_diff($allMigrations, $ranMigrations);

        return array_values($pending);
    }

    protected function getExistingTables(): array
    {
        $tables = DB::select('SHOW TABLES');
        $tableNames = [];

        foreach ($tables as $table) {
            $tableArray = (array) $table;
            $tableNames[] = array_values($tableArray)[0];
        }

        return $tableNames;
    }

    protected function findConflictingMigrations(array $pendingMigrations, array $existingTables): array
    {
        $conflicts = [];

        // Map of migration patterns to table names they create
        $migrationTableMap = [
            'create_ghg_protocol_emission_factors_table' => ['ghg_protocol_emission_factors'],
            'create_emission_factors_table' => ['emission_factors'],
            'create_emission_categories_table' => ['emission_categories'],
            'create_emission_sources_table' => ['emission_sources'],
            'create_greenhouse_gases_table' => ['greenhouse_gases'],
            'create_measurement_units_table' => ['measurement_units'],
            'create_activity_data_table' => ['activity_data'],
            'create_business_units_table' => ['business_units'],
            'create_cache_table' => ['cache'],
            'create_cache_locks_table' => ['cache_locks'],
            'create_calculated_emissions_table' => ['calculated_emissions'],
            'create_carbon_offsets_table' => ['carbon_offsets'],
            'create_emission_factor_units_table' => ['emission_factor_units'],
            'create_emission_report_table' => ['emission_reports'],
            'create_emission_targets_table' => ['emission_targets'],
            'create_emissions_table' => ['emissions'],
            'create_facilities_table' => ['facilities'],
            'create_ghg_protocol_sectors_table' => ['ghg_protocol_sectors'],
            'create_kpi_categories_table' => ['kpi_categories'],
            'create_kpi_targets_table' => ['kpi_targets'],
            'create_kpi_values_table' => ['kpi_values'],
            'create_kpis_table' => ['kpis'],
            'create_granular_calculation_tables' => [
                'calculation_methodologies',
                'calculation_parameters',
                'calculation_results',
                'calculation_audits'
            ],
            'create_target_setting_planning_tables' => [
                'emission_targets',
                'target_scenarios',
                'decarbonization_initiatives',
                'initiative_progress'
            ],
            'create_audit_reporting_tables' => [
                'audit_trails',
                'compliance_reports',
                'verification_records',
                'report_templates'
            ],
            'create_data_lake_tables' => [
                'data_sources',
                'data_ingestion_jobs',
                'data_transformations',
                'data_quality_checks'
            ],
        ];

        foreach ($pendingMigrations as $migration) {
            foreach ($migrationTableMap as $pattern => $tables) {
                if (strpos($migration, $pattern) !== false) {
                    $existingTablesForMigration = [];

                    foreach ($tables as $table) {
                        if (in_array($table, $existingTables)) {
                            $existingTablesForMigration[] = $table;
                        }
                    }

                    if (!empty($existingTablesForMigration)) {
                        $conflicts[$migration] = $existingTablesForMigration;
                    }
                    break;
                }
            }
        }

        return $conflicts;
    }

    protected function markMigrationsAsRun(array $conflictingMigrations): void
    {
        $batch = $this->getNextBatchNumber();

        foreach (array_keys($conflictingMigrations) as $migration) {
            DB::table('migrations')->insert([
                'migration' => $migration,
                'batch' => $batch,
            ]);

            $this->info("✅ Marked {$migration} as run");
        }
    }

    protected function getNextBatchNumber(): int
    {
        return DB::table('migrations')->max('batch') + 1;
    }
}
