<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ImportMobileCombustionAll extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:mobile-combustion-all {--skip-confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import all Mobile Combustion emission factors and reference data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Mobile Combustion & Freight Transport Data Import ===');
        $this->info('This will import:');
        $this->info('1. Fuel Economy Reference Table');
        $this->info('2. US & Other Regions CH4/N2O Emission Factors (Table 1)');
        $this->info('3. UK CO2/CH4/N2O Emission Factors (Table 2)');
        $this->info('4. Freight Transport CO2 Emission Factors');
        $this->newLine();

        if (!$this->option('skip-confirmation')) {
            if (!$this->confirm('Do you want to proceed with the import?')) {
                $this->info('Import cancelled.');
                return 0;
            }
        }

        $start_time = now();
        $this->info("Starting import at {$start_time->format('Y-m-d H:i:s')}");
        $this->newLine();

        // Import fuel economy reference data
        $this->info('Step 1/4: Importing Fuel Economy Reference Table...');
        $result1 = $this->call('import:fuel-economy-reference');
        if ($result1 !== 0) {
            $this->error('Failed to import fuel economy reference data.');
            return 1;
        }
        $this->info('✓ Fuel Economy Reference Table imported successfully');
        $this->newLine();

        // Import US mobile combustion data
        $this->info('Step 2/4: Importing US & Other Regions Mobile Combustion Data...');
        $result2 = $this->call('import:mobile-combustion-us');
        if ($result2 !== 0) {
            $this->error('Failed to import US mobile combustion data.');
            return 1;
        }
        $this->info('✓ US & Other Regions Mobile Combustion Data imported successfully');
        $this->newLine();

        // Import UK mobile combustion data
        $this->info('Step 3/4: Importing UK Mobile Combustion Data...');
        $result3 = $this->call('import:mobile-combustion-uk');
        if ($result3 !== 0) {
            $this->error('Failed to import UK mobile combustion data.');
            return 1;
        }
        $this->info('✓ UK Mobile Combustion Data imported successfully');
        $this->newLine();

        // Import freight transport emission factors
        $this->info('Step 4/4: Importing Freight Transport CO2 Emission Factors...');
        $result4 = $this->call('import:freight-transport');
        if ($result4 !== 0) {
            $this->error('Failed to import freight transport emission factors.');
            return 1;
        }
        $this->info('✓ Freight Transport CO2 Emission Factors imported successfully');
        $this->newLine();

        $end_time = now();
        $duration = $end_time->diffInSeconds($start_time);

        $this->info('=== Import Summary ===');
        $this->info("Started: {$start_time->format('Y-m-d H:i:s')}");
        $this->info("Completed: {$end_time->format('Y-m-d H:i:s')}");
        $this->info("Duration: {$duration} seconds");
        $this->newLine();

        $this->info('All mobile combustion and freight transport data imported successfully!');
        $this->info('The following data has been imported:');
        $this->info('• Reference fuel economy values for vehicle types');
        $this->info('• CH4 and N2O emission factors for US and other regions');
        $this->info('• CO2, CH4, and N2O emission factors for UK');
        $this->info('• Freight transport CO2 emission factors for UK and US');
        $this->info('• Vehicle types: Passenger Cars, Light-Duty Vehicles, Trucks, Buses, Motorcycles, HGVs');
        $this->info('• Freight modes: Air, Rail, Road (Vans, HGVs), Sea (Tankers, Cargo ships)');
        $this->info('• Multiple fuel types: Gasoline, Diesel, CNG, LNG, LPG, Ethanol, Methanol, Biodiesel, Hybrid');
        $this->info('• Year-specific factors for US vehicles (1970-2030)');
        $this->info('• Size/weight-specific factors for UK vehicles');
        $this->info('• Weight-distance and vehicle-distance based freight factors');

        return 0;
    }
}
