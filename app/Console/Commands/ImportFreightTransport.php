<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\MeasurementUnit;
use Illuminate\Support\Facades\DB;

class ImportFreightTransport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:freight-transport';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Freight Transport CO2 Emission Factors by Weight or Vehicle Distance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of Freight Transport CO2 Emission Factors...');
        
        // Freight transport data from Table 1 - CO2 Emission Factors by Weight or Vehicle Distance
        // Format: [Region, Vehicle, Type, Weight Class, Fuel, CO2_EF, EF_Unit]
        $freight_data = [
            // UK Freight flights
            ['UK', 'Freight flights', 'Domestic', 'Between UK airports', '', 2.352, 'kg/tonne-km/tonne'],
            ['UK', 'Freight flights', 'Short haul', 'up to 3700 km distance', '', 1.206, 'kg/tonne-km/tonne'],
            ['UK', 'Freight flights', 'Long haul', 'over 3700 km distance', '', 0.534, 'kg/tonne-km/tonne'],
            ['UK', 'Freight flights', 'International', 'to/from UK to/from distance', '', 0.534, 'kg/tonne-km/tonne'],
            
            // UK Rail
            ['UK', 'Rail', 'Freight train', '', '', 0.027, 'kg/tonne-km/tonne'],
            
            // UK Vans
            ['UK', 'Vans', 'Class I', '<1.305 tonnes', 'Petrol', 1.055, 'kg/tonne-km/tonne'],
            ['UK', 'Vans', 'Class II', '>1.305 to <1.74 tonnes', 'Petrol', 0.730, 'kg/tonne-km/tonne'],
            ['UK', 'Vans', 'Class III', '>1.74 to <3.5 tonnes', 'Petrol', 0.775, 'kg/tonne-km/tonne'],
            ['UK', 'Vans', 'Class I', '<1.305 tonnes', 'Diesel', 0.817, 'kg/tonne-km/tonne'],
            ['UK', 'Vans', 'Class I', '>1.305 tonnes', 'Diesel', 0.761, 'kg/tonne-km/tonne'],
            ['UK', 'Vans', 'Class II', '>1.305 to <1.74 tonnes', 'Diesel', 0.562, 'kg/tonne-km/tonne'],
            ['UK', 'Vans', 'Class III', '>1.74 to <3.5 tonnes', 'Diesel', 0.564, 'kg/tonne-km/tonne'],
            ['UK', 'Vans', 'Average', 'up to 3.5 tonnes', 'Diesel', 0.566, 'kg/tonne-km/tonne'],
            ['UK', 'Vans', 'Average', 'up to 3.5 tonnes', 'CNG', 0.671, 'kg/tonne-km/tonne'],
            ['UK', 'Vans', 'Average', 'up to 3.5 tonnes', 'LPG', 0.631, 'kg/tonne-km/tonne'],
            ['UK', 'Vans', 'Average', '3.5 to 7.5 tonnes', 'Unknown', 0.571, 'kg/tonne-km/tonne'],
            
            // UK HGV - Rigid
            ['UK', 'HGV - Rigid', '3.5 - 7.5 tonnes', 'Average Laden', 'Diesel', 0.507, 'kg/tonne-km/tonne'],
            ['UK', 'HGV - Rigid', '7.5 - 17 tonnes', 'Average Laden', 'Diesel', 0.350, 'kg/tonne-km/tonne'],
            ['UK', 'HGV - Rigid', '>17 tonnes', 'Average Laden', 'Diesel', 0.162, 'kg/tonne-km/tonne'],
            ['UK', 'HGV - Rigid', 'Average', 'Average Laden', 'Diesel', 0.175, 'kg/tonne-km/tonne'],
            
            // UK HGV - Articulated
            ['UK', 'HGV - Articulated', '3.5 - 33 tonnes', 'Average Laden', 'Diesel', 0.114, 'kg/tonne-km/tonne'],
            ['UK', 'HGV - Articulated', '>33 tonnes', 'Average Laden', 'Diesel', 0.073, 'kg/tonne-km/tonne'],
            ['UK', 'HGV - Articulated', 'Average', 'Average Laden', 'Diesel', 0.074, 'kg/tonne-km/tonne'],
            ['UK', 'HGV - 1 gas breakdown', 'Average', 'Average Laden', 'Diesel', 0.085, 'kg/tonne-km/tonne'],
            
            // UK Sea tanker
            ['UK', 'Sea tanker', 'Crude tanker', '200,000+ dwt', '', 0.003, 'kg/tonne-km/tonne'],
            ['UK', 'Sea tanker', 'Crude tanker', '120,000-199,999 dwt', '', 0.004, 'kg/tonne-km/tonne'],
            ['UK', 'Sea tanker', 'Crude tanker', '80,000-119,999 dwt', '', 0.006, 'kg/tonne-km/tonne'],
            ['UK', 'Sea tanker', 'Crude tanker', '60,000-79,999 dwt', '', 0.006, 'kg/tonne-km/tonne'],
            ['UK', 'Sea tanker', 'Crude tanker', '10,000-59,999 dwt', '', 0.009, 'kg/tonne-km/tonne'],
            ['UK', 'Sea tanker', 'Crude tanker', '<9999 dwt', '', 0.033, 'kg/tonne-km/tonne'],
            ['UK', 'Sea tanker', 'Crude tanker', 'Average', '', 0.005, 'kg/tonne-km/tonne'],
            
            // UK Cargo ship
            ['UK', 'Cargo ship', 'Bulk carrier', '200,000+ dwt', '', 0.003, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Bulk carrier', '10,000-199,999 dwt', '', 0.003, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Bulk carrier', '<9999 dwt', '', 0.014, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Bulk carrier', '35,000-59,999 dwt', '', 0.006, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Bulk carrier', '<9999 dwt', '', 0.006, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Bulk carrier', '0-9999 dwt', '', 0.029, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Bulk carrier', 'Average', '', 0.003, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Container ship', 'Average', '', 0.011, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Container ship', '5000-7999 TEU', '', 0.017, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Container ship', '3000-4999 TEU', '', 0.017, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Container ship', '2000-2999 TEU', '', 0.020, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Container ship', '1000-1999 TEU', '', 0.032, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Container ship', '100-999 TEU', '', 0.056, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Container ship', 'Average', '', 0.016, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Vehicle transport', 'Average CEU', '', 0.032, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Vehicle transport', '0-5999 CEU', '', 0.058, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Vehicle transport', 'Average', '', 0.032, 'kg/tonne-km/tonne'],
            ['UK', 'Cargo ship', 'Large RoRo ferry', 'Average', '', 0.036, 'kg/tonne-km/tonne'],
            
            // US data
            ['US', 'Medium-Duty Truck', '', '', '', 0.168, 'kg/short ton-mile'],
            ['US', 'Heavy-Duty Truck', '', '', '', 0.168, 'kg/short ton-mile'],
            ['US', 'Rail', '', '', '', 0.022, 'kg/short ton-mile'],
            ['US', 'Waterborne Craft', '', '', '', 0.062, 'kg/short ton-mile'],
            ['US', 'Air', '', '', '', 0.952, 'kg/short ton-mile'],
            
            // Vehicle-Distance Basis (US)
            ['US', 'Medium-Duty Truck', 'Vehicle-Distance Basis', '', '', 1.247, 'kg/vehicle-mile'],
            ['US', 'Heavy-Duty Truck', 'Vehicle-Distance Basis', '', '', 1.247, 'kg/vehicle-mile'],
            ['US', 'Passenger Car', 'Vehicle-Distance Basis', '', '', 0.175, 'kg/vehicle-mile'],
            ['US', 'Light-Duty Truck', 'Vehicle-Distance Basis', '', '', 0.865, 'kg/vehicle-mile']
        ];

        try {
            DB::beginTransaction();
            
            // Get or create the freight sector
            $sector = GhgProtocolSector::firstOrCreate([
                'name' => 'freight'
            ], [
                'display_name' => 'Freight and Logistics',
                'description' => 'Emissions from freight transportation and logistics across various modes',
                'sort_order' => 4,
                'is_active' => true
            ]);
            
            // Get or create measurement units
            $unit_kg_tonne_km = MeasurementUnit::firstOrCreate([
                'name' => 'kg/tonne-km/tonne'
            ], [
                'symbol' => 'kg/tonne-km/tonne',
                'description' => 'Kilograms per tonne-kilometer per tonne',
                'unit_type' => 'emission_factor'
            ]);
            
            $unit_kg_short_ton_mile = MeasurementUnit::firstOrCreate([
                'name' => 'kg/short ton-mile'
            ], [
                'symbol' => 'kg/short ton-mile',
                'description' => 'Kilograms per short ton-mile',
                'unit_type' => 'emission_factor'
            ]);
            
            $unit_kg_vehicle_mile = MeasurementUnit::firstOrCreate([
                'name' => 'kg/vehicle-mile'
            ], [
                'symbol' => 'kg/vehicle-mile',
                'description' => 'Kilograms per vehicle-mile',
                'unit_type' => 'emission_factor'
            ]);
            
            $imported_count = 0;
            
            foreach ($freight_data as $row) {
                [$region_name, $vehicle_type, $type, $weight_class, $fuel, $co2_ef, $ef_unit] = $row;
                
                // Get or create region
                $region_code = $region_name === 'US' ? 'US' : 'UK';
                $region_full_name = $region_name === 'US' ? 'United States' : 'United Kingdom';
                
                $region = GhgProtocolRegion::firstOrCreate([
                    'code' => $region_code
                ], [
                    'name' => $region_full_name,
                    'region_type' => 'country',
                    'iso_country_code' => $region_name === 'US' ? 'USA' : 'GBR',
                    'description' => $region_name === 'US' ? 'United States of America' : 'United Kingdom of Great Britain and Northern Ireland',
                    'is_active' => true,
                    'sort_order' => $region_name === 'US' ? 1 : 2
                ]);
                
                // Get or create activity
                $activity_name = $vehicle_type;
                if ($type) {
                    $activity_name .= " - {$type}";
                }
                if ($fuel) {
                    $activity_name .= " - {$fuel}";
                }
                
                // Create shorter code for database constraint
                $short_code = strtoupper(str_replace([' ', '-', '(', ')', ',', '.', '/'], ['_', '_', '', '', '_', '', '_'], $activity_name));
                $short_code = substr($short_code, 0, 45); // Limit to 45 chars
                
                $activity = GhgProtocolActivity::firstOrCreate([
                    'name' => $activity_name,
                    'sector_id' => $sector->id
                ], [
                    'code' => $short_code,
                    'description' => "Freight transport via {$vehicle_type}" . 
                                   ($type ? " ({$type})" : "") . 
                                   ($fuel ? " using {$fuel}" : ""),
                    'activity_type' => 'freight_transport',
                    'fuel_type' => $fuel ?: null,
                    'vehicle_type' => $vehicle_type,
                    'applicable_scopes' => [3], // Freight is typically Scope 3
                    'calculation_methods' => ['weight_distance_based', 'vehicle_distance_based'],
                    'is_active' => true,
                    'sort_order' => 0
                ]);
                
                // Determine the correct unit
                $unit_id = $unit_kg_tonne_km->id; // default
                if (str_contains($ef_unit, 'short ton-mile')) {
                    $unit_id = $unit_kg_short_ton_mile->id;
                } elseif (str_contains($ef_unit, 'vehicle-mile')) {
                    $unit_id = $unit_kg_vehicle_mile->id;
                }
                
                // Create CO2 emission factor
                GhgProtocolEmissionFactor::create([
                    'code' => "{$region_name}-FREIGHT-{$vehicle_type}-" . ($type ? $type : 'DEFAULT') . "-CO2",
                    'name' => "Freight Transport CO2 Factor - {$vehicle_type} ({$region_name})",
                    'description' => "CO2 emission factor for freight transport via {$vehicle_type} in {$region_name}" . 
                                   ($type ? " - {$type}" : "") . 
                                   ($weight_class ? " ({$weight_class})" : "") .
                                   ($fuel ? " using {$fuel}" : ""),
                    'sector_id' => $sector->id,
                    'activity_id' => $activity->id,
                    'region_id' => $region->id,
                    'gas_id' => 1, // CO2
                    'calculation_method' => str_contains($ef_unit, 'vehicle-mile') ? 'Vehicle distance-based emission factor' : 'Weight-distance based emission factor',
                    'methodology_reference' => 'GHG Protocol Freight Transport Tool',
                    'factor_value' => $co2_ef,
                    'co2_factor' => $co2_ef,
                    'input_unit_id' => $unit_id,
                    'output_unit_id' => $unit_id,
                    'data_quality_rating' => 'High',
                    'data_source' => 'GHG Protocol',
                    'data_source_detail' => 'Table 1. CO2 Emission Factors by Weight or Vehicle Distance (i.e., Freight Transport)',
                    'metadata' => [
                        'vehicle_type' => $vehicle_type,
                        'transport_type' => $type,
                        'weight_class' => $weight_class,
                        'fuel_type' => $fuel,
                        'gas_type' => 'CO2',
                        'table_source' => 'Table 1 - Freight Transport',
                        'region' => $region_name,
                        'calculation_basis' => str_contains($ef_unit, 'vehicle-mile') ? 'vehicle_distance' : 'weight_distance'
                    ],
                    'is_default' => false,
                    'is_active' => true,
                    'tier_level' => 2,
                    'priority' => 100
                ]);
                
                $imported_count++;
                $this->info("Imported: {$vehicle_type}" . ($type ? " - {$type}" : "") . " ({$region_name})" . ($weight_class ? " [{$weight_class}]" : ""));
            }
            
            DB::commit();
            
            $this->info("\nSuccessfully imported {$imported_count} freight transport emission factors.");
            $this->info("Total records created:");
            $this->info("- Emission factors: {$imported_count}");
            $this->info("- Activities: " . GhgProtocolActivity::where('sector_id', $sector->id)->count());
            
        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Error importing data: " . $e->getMessage());
            return 1;
        }

        $this->info("\nImport completed successfully!");
        $this->info("Freight transport data covers:");
        $this->info("- Air freight (domestic, short-haul, long-haul)");
        $this->info("- Rail freight");
        $this->info("- Road freight (vans, HGVs by weight class)");
        $this->info("- Sea freight (tankers, cargo ships, container ships)");
        $this->info("- Both weight-distance and vehicle-distance based factors");
        return 0;
    }
}
