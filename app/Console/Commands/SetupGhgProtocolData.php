<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MeasurementUnit;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\EmissionFactorVariant;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\GreenhouseGas;

class SetupGhgProtocolData extends Command
{
    protected $signature = 'ghg:setup-data';
    protected $description = 'Setup basic GHG Protocol data for testing';

    public function handle()
    {
        $this->info('Setting up GHG Protocol data...');
        
        $this->createMissingUnits();
        $this->createSampleEmissionFactors();
        
        $this->info('Setup completed!');
    }

    protected function createMissingUnits()
    {
        $this->info('Creating missing measurement units...');
        
        $units = [
            [
                'name' => 'Kilograms CO2 Equivalent',
                'symbol' => 'kg CO₂e',
                'unit_type' => 'emissions',
                'is_base_unit' => false,
                'is_active' => true,
                'description' => 'Kilograms of carbon dioxide equivalent'
            ],
            [
                'name' => 'Tonnes CO2 Equivalent',
                'symbol' => 't CO₂e',
                'unit_type' => 'emissions',
                'is_base_unit' => false,
                'is_active' => true,
                'description' => 'Tonnes of carbon dioxide equivalent'
            ],
        ];

        foreach ($units as $unitData) {
            MeasurementUnit::updateOrCreate(
                ['symbol' => $unitData['symbol']],
                $unitData
            );
            $this->line("Created unit: {$unitData['symbol']}");
        }
    }

    protected function createSampleEmissionFactors()
    {
        $this->info('Creating sample emission factors...');
        
        // Get required entities
        $stationarySector = GhgProtocolSector::where('name', 'stationary_combustion')->first();
        $ngActivity = GhgProtocolActivity::where('code', 'STAT_COMB_NG')->first();
        $co2Gas = GreenhouseGas::where('chemical_formula', 'CO₂')->first();
        $gjUnit = MeasurementUnit::where('symbol', 'GJ')->first();
        $kgCo2eUnit = MeasurementUnit::where('symbol', 'kg CO₂e')->first();
        $globalRegion = GhgProtocolRegion::where('code', 'GLOBAL')->first();
        $usRegion = GhgProtocolRegion::where('code', 'US')->first();

        if (!$stationarySector || !$ngActivity || !$co2Gas || !$gjUnit || !$kgCo2eUnit) {
            $this->error('Missing required entities for creating emission factors');
            return;
        }

        // Create Natural Gas CO2 Factor
        $ngFactor = GhgProtocolEmissionFactor::updateOrCreate(
            ['code' => 'STAT_NG_CO2_GLOBAL'],
            [
                'name' => 'Natural Gas CO2 Emission Factor (Global)',
                'description' => 'Global default CO2 emission factor for natural gas combustion',
                'sector_id' => $stationarySector->id,
                'activity_id' => $ngActivity->id,
                'region_id' => $globalRegion?->id,
                'gas_id' => $co2Gas->id,
                'calculation_method' => 'tier1',
                'methodology_reference' => '2006 IPCC Guidelines',
                'factor_value' => 56.1, // kg CO2/GJ
                'carbon_content' => 15.3, // kg C/GJ
                'oxidation_factor' => 0.995,
                'heating_value' => 38.3, // MJ/m3
                'heating_value_type' => 'net',
                'input_unit_id' => $gjUnit->id,
                'output_unit_id' => $kgCo2eUnit->id,
                'data_quality_rating' => 'A',
                'uncertainty_percentage' => 5.0,
                'vintage_year' => 2023,
                'valid_from' => '2023-01-01',
                'data_source' => 'GHG Protocol',
                'is_default' => true,
                'is_active' => true,
                'tier_level' => 1,
                'priority' => 100,
            ]
        );

        $this->line("Created emission factor: {$ngFactor->name}");

        // Create US variant
        if ($usRegion) {
            $usVariant = EmissionFactorVariant::updateOrCreate(
                [
                    'base_factor_id' => $ngFactor->id,
                    'variant_code' => 'US_SPECIFIC'
                ],
                [
                    'variant_type' => 'regional',
                    'variant_name' => 'US Specific Natural Gas CO2',
                    'variant_description' => 'US-specific natural gas CO2 emission factor',
                    'region_id' => $usRegion->id,
                    'factor_value' => 53.06, // EPA value
                    'carbon_content' => 14.46,
                    'oxidation_factor' => 0.995,
                    'data_quality_rating' => 'A',
                    'uncertainty_percentage' => 2.0,
                    'vintage_year' => 2023,
                    'valid_from' => '2023-01-01',
                    'notes' => 'EPA GHG Inventory 2023',
                    'priority' => 200,
                    'is_active' => true,
                ]
            );

            $this->line("Created variant: {$usVariant->variant_name}");
        }

        // Create electricity factor
        $electricitySector = GhgProtocolSector::where('name', 'electricity')->first();
        $elecActivity = GhgProtocolActivity::where('code', 'ELEC_GRID')->first();
        $kwhUnit = MeasurementUnit::where('symbol', 'kWh')->first();

        if ($electricitySector && $elecActivity && $kwhUnit) {
            $elecFactor = GhgProtocolEmissionFactor::updateOrCreate(
                ['code' => 'ELEC_GRID_CO2_GLOBAL'],
                [
                    'name' => 'Grid Electricity CO2 Factor (Global)',
                    'description' => 'Global average grid electricity CO2 emission factor',
                    'sector_id' => $electricitySector->id,
                    'activity_id' => $elecActivity->id,
                    'region_id' => $globalRegion?->id,
                    'gas_id' => $co2Gas->id,
                    'calculation_method' => 'location_based',
                    'methodology_reference' => 'GHG Protocol Scope 2 Guidance',
                    'factor_value' => 0.709, // kg CO2/kWh
                    'input_unit_id' => $kwhUnit->id,
                    'output_unit_id' => $kgCo2eUnit->id,
                    'data_quality_rating' => 'B',
                    'uncertainty_percentage' => 20.0,
                    'vintage_year' => 2023,
                    'valid_from' => '2023-01-01',
                    'data_source' => 'IEA Emissions Factors',
                    'is_default' => true,
                    'is_active' => true,
                    'tier_level' => 1,
                    'priority' => 100,
                ]
            );

            $this->line("Created emission factor: {$elecFactor->name}");

            // Create US electricity variant
            if ($usRegion) {
                $usElecVariant = EmissionFactorVariant::updateOrCreate(
                    [
                        'base_factor_id' => $elecFactor->id,
                        'variant_code' => 'US_NATIONAL'
                    ],
                    [
                        'variant_type' => 'regional',
                        'variant_name' => 'US National Grid Average',
                        'variant_description' => 'US national average grid electricity emission factor',
                        'region_id' => $usRegion->id,
                        'factor_value' => 0.386, // kg CO2/kWh
                        'data_quality_rating' => 'A',
                        'uncertainty_percentage' => 10.0,
                        'vintage_year' => 2023,
                        'valid_from' => '2023-01-01',
                        'notes' => 'EPA eGRID 2023',
                        'priority' => 200,
                        'is_active' => true,
                    ]
                );

                $this->line("Created variant: {$usElecVariant->variant_name}");
            }
        }
    }
}
