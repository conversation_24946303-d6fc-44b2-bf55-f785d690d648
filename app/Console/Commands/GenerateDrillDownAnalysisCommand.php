<?php

namespace App\Console\Commands;

use App\Models\Organization;
use App\Services\DrillDownAnalysisService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GenerateDrillDownAnalysisCommand extends Command
{
    protected $signature = 'reports:drill-down-analysis 
                            {organization : Organization ID to analyze}
                            {--reporting-year= : Reporting year (default: previous year)}
                            {--include-suppliers : Include supplier-level breakdown}
                            {--include-calculations : Include detailed calculation information}
                            {--save : Save analysis to database}
                            {--dry-run : Run without saving analysis}';

    protected $description = 'Generate comprehensive drill-down analysis from corporate totals to individual sources';

    protected DrillDownAnalysisService $drillDownService;

    public function __construct(DrillDownAnalysisService $drillDownService)
    {
        parent::__construct();
        $this->drillDownService = $drillDownService;
    }

    public function handle(): int
    {
        $this->info('🔍 Starting drill-down analysis...');

        $organizationId = $this->argument('organization');
        $reportingYear = $this->option('reporting-year') ?? now()->year - 1;
        $includeSuppliers = $this->option('include-suppliers');
        $includeCalculations = $this->option('include-calculations');
        $save = $this->option('save');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No analysis will be saved');
        }

        try {
            $organization = Organization::find($organizationId);
            
            if (!$organization) {
                $this->error("❌ Organization not found: {$organizationId}");
                return 1;
            }

            $this->line("  📍 Organization: {$organization->name}");
            $this->line("  📅 Reporting Year: {$reportingYear}");
            $this->line("  🏭 Include Suppliers: " . ($includeSuppliers ? 'Yes' : 'No'));
            $this->line("  🧮 Include Calculations: " . ($includeCalculations ? 'Yes' : 'No'));

            if ($dryRun) {
                $this->warn("  🧪 Dry run - would generate drill-down analysis");
                return 0;
            }

            $options = [
                'reporting_year' => $reportingYear,
                'include_suppliers' => $includeSuppliers,
                'include_calculation_details' => $includeCalculations,
            ];

            $analysis = $this->drillDownService->generateDrillDownAnalysis($organization, $options);

            if (!$analysis['success']) {
                $this->error("❌ Drill-down analysis failed: {$analysis['error']}");
                return 1;
            }

            $this->displayAnalysisResults($analysis);

            if ($save) {
                $savedAnalysis = $this->saveAnalysisToDatabase($organization, $analysis);
                $this->info("💾 Analysis saved to database with ID: {$savedAnalysis->id}");
            }

            $this->info('✅ Drill-down analysis completed successfully');
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Drill-down analysis failed: {$e->getMessage()}");
            Log::error('Drill-down analysis command failed', [
                'organization_id' => $organizationId,
                'error' => $e->getMessage(),
            ]);
            return 1;
        }
    }

    protected function displayAnalysisResults(array $analysis): void
    {
        $this->info('📊 Drill-Down Analysis Results:');
        
        // Level 1: Corporate Total
        $this->displayCorporateTotal($analysis['drill_down_levels']['level_1_corporate']);
        
        // Level 2: Scope Breakdown
        $this->displayScopeBreakdown($analysis['drill_down_levels']['level_2_scope']);
        
        // Level 3: Facility Breakdown
        $this->displayFacilityBreakdown($analysis['drill_down_levels']['level_3_facility']);
        
        // Level 4: Source Breakdown
        $this->displaySourceBreakdown($analysis['drill_down_levels']['level_4_source']);
        
        // Level 5: Activity Detail
        $this->displayActivityDetail($analysis['drill_down_levels']['level_5_activity']);
        
        // Level 6: Supplier Breakdown (if included)
        if (isset($analysis['drill_down_levels']['level_6_supplier'])) {
            $this->displaySupplierBreakdown($analysis['drill_down_levels']['level_6_supplier']);
        }
        
        // Summary Statistics
        $this->displaySummaryStatistics($analysis['summary_statistics']);
        
        // Traceability Matrix
        $this->displayTraceabilityMatrix($analysis['traceability_matrix']);
    }

    protected function displayCorporateTotal(array $corporateTotal): void
    {
        $this->info('  📈 Level 1 - Corporate Total:');
        $this->line("    Total Emissions: " . number_format($corporateTotal['total_emissions'], 2) . " tCO2e");
        $this->line("    Scope 1: " . number_format($corporateTotal['scope_1'], 2) . " tCO2e");
        $this->line("    Scope 2: " . number_format($corporateTotal['scope_2'], 2) . " tCO2e");
        $this->line("    Scope 3: " . number_format($corporateTotal['scope_3'], 2) . " tCO2e");
        $this->line("    Data Points: " . number_format($corporateTotal['data_points']));
        $this->line("    Facilities: " . $corporateTotal['facilities_count']);
        $this->line("    Consolidation: " . $corporateTotal['consolidation_approach']);
        $this->line('');
    }

    protected function displayScopeBreakdown(array $scopeBreakdown): void
    {
        $this->info('  🎯 Level 2 - Scope Breakdown:');
        
        foreach ($scopeBreakdown['scopes'] as $scope) {
            $this->line("    Scope {$scope['scope']} ({$scope['scope_name']}):");
            $this->line("      Emissions: " . number_format($scope['total_emissions'], 2) . " tCO2e ({$scope['percentage_of_total']}%)");
            $this->line("      Data Points: " . number_format($scope['data_points']));
            $this->line("      Facilities: " . $scope['facilities_involved']);
            $this->line("      Sources: " . count($scope['emission_sources']));
            
            // Show top 3 sources for this scope
            $topSources = array_slice($scope['emission_sources'], 0, 3);
            foreach ($topSources as $source) {
                $this->line("        - {$source['source_name']}: " . number_format($source['emissions'], 2) . " tCO2e");
            }
            $this->line('');
        }
    }

    protected function displayFacilityBreakdown(array $facilityBreakdown): void
    {
        $this->info('  🏭 Level 3 - Facility Breakdown:');
        $this->line("    Total Facilities: " . count($facilityBreakdown['facilities']));
        
        // Show top 5 facilities by emissions
        $topFacilities = array_slice($facilityBreakdown['facility_ranking'], 0, 5);
        $this->line("    Top 5 Facilities by Emissions:");
        
        foreach ($topFacilities as $index => $facility) {
            $rank = $index + 1;
            $this->line("      {$rank}. {$facility['facility_name']}:");
            $this->line("         Total: " . number_format($facility['total_emissions'], 2) . " tCO2e ({$facility['percentage_of_corporate']}%)");
            $this->line("         Location: {$facility['location']['city']}, {$facility['location']['country']}");
            $this->line("         Type: {$facility['facility_type']}");
            $this->line("         Sources: " . count($facility['emission_sources']));
        }
        $this->line('');
    }

    protected function displaySourceBreakdown(array $sourceBreakdown): void
    {
        $this->info('  ⚡ Level 4 - Source Breakdown:');
        $this->line("    Total Sources: " . count($sourceBreakdown['sources']));
        
        // Show top 5 sources by emissions
        $topSources = array_slice($sourceBreakdown['source_ranking'], 0, 5);
        $this->line("    Top 5 Sources by Emissions:");
        
        foreach ($topSources as $index => $source) {
            $rank = $index + 1;
            $this->line("      {$rank}. {$source['source_name']}:");
            $this->line("         Total: " . number_format($source['total_emissions'], 2) . " tCO2e");
            $this->line("         Category: {$source['source_category']}");
            $this->line("         Facilities: " . count($source['facility_distribution']));
            $this->line("         Factors Used: " . count($source['emission_factors_used']));
            
            // Show scope distribution
            $scopeDist = $source['scope_distribution'];
            $this->line("         Scope Distribution: S1: " . number_format($scopeDist['scope_1'], 1) . 
                       ", S2: " . number_format($scopeDist['scope_2'], 1) . 
                       ", S3: " . number_format($scopeDist['scope_3'], 1) . " tCO2e");
        }
        $this->line('');
    }

    protected function displayActivityDetail(array $activityDetail): void
    {
        $this->info('  📋 Level 5 - Activity Detail:');
        $this->line("    Showing: " . count($activityDetail['activities']) . " activities");
        $this->line("    Total Activities: " . number_format($activityDetail['total_activities']));
        
        // Show top 5 activities by emissions
        $topActivities = array_slice($activityDetail['activities'], 0, 5);
        $this->line("    Top 5 Activities by Emissions:");
        
        foreach ($topActivities as $index => $activity) {
            $rank = $index + 1;
            $this->line("      {$rank}. Activity ID {$activity['activity_id']}:");
            $this->line("         Facility: {$activity['facility_name']}");
            $this->line("         Source: {$activity['emission_source']}");
            $this->line("         Scope: {$activity['scope']}");
            $this->line("         Emissions: " . number_format($activity['calculated_emissions'], 2) . " tCO2e");
            $this->line("         Quantity: " . number_format($activity['quantity'], 2) . " {$activity['unit']}");
            $this->line("         Factor: {$activity['emission_factor']['name']} ({$activity['emission_factor']['data_source']})");
            $this->line("         Date: {$activity['date_recorded']}");
            $this->line("         Quality: {$activity['data_quality']}");
        }
        $this->line('');
    }

    protected function displaySupplierBreakdown(array $supplierBreakdown): void
    {
        if (empty($supplierBreakdown['suppliers'])) {
            $this->info('  🏢 Level 6 - Supplier Breakdown:');
            $this->line("    " . $supplierBreakdown['message']);
            $this->line('');
            return;
        }
        
        $this->info('  🏢 Level 6 - Supplier Breakdown:');
        $this->line("    Total Suppliers: " . count($supplierBreakdown['suppliers']));
        
        // Show top 5 suppliers by emissions
        $topSuppliers = array_slice($supplierBreakdown['supplier_ranking'], 0, 5);
        $this->line("    Top 5 Suppliers by Emissions:");
        
        foreach ($topSuppliers as $index => $supplier) {
            $rank = $index + 1;
            $this->line("      {$rank}. {$supplier['supplier_name']}:");
            $this->line("         Total: " . number_format($supplier['total_emissions'], 2) . " tCO2e");
            $this->line("         Type: {$supplier['supplier_type']}");
            $this->line("         Sector: {$supplier['industry_sector']}");
            $this->line("         Country: {$supplier['country']}");
            $this->line("         Categories: " . count($supplier['scope3_categories']));
            $this->line("         Data Quality: {$supplier['data_quality_rating']}");
            $this->line("         Annual Spend: $" . number_format($supplier['annual_spend'], 0));
        }
        $this->line('');
    }

    protected function displaySummaryStatistics(array $summaryStats): void
    {
        $this->info('  📊 Summary Statistics:');
        $this->line("    Total Emissions: " . number_format($summaryStats['total_emissions'], 2) . " tCO2e");
        $this->line("    Total Facilities: " . $summaryStats['total_facilities']);
        $this->line("    Total Data Points: " . number_format($summaryStats['total_data_points']));
        $this->line("    Largest Facility: {$summaryStats['largest_facility_percentage']}% of total");
        $this->line("    Top 5 Facilities: {$summaryStats['top_5_facilities_percentage']}% of total");
        $this->line('');
    }

    protected function displayTraceabilityMatrix(array $traceabilityMatrix): void
    {
        $this->info('  🔗 Traceability Matrix:');
        
        foreach ($traceabilityMatrix as $level => $trace) {
            $levelName = str_replace('_', ' ', ucwords($level, '_'));
            $this->line("    {$levelName}: Available");
        }
        $this->line('');
    }

    protected function saveAnalysisToDatabase(Organization $organization, array $analysis): object
    {
        return \App\Models\DrillDownAnalysis::create([
            'organization_id' => $organization->id,
            'analysis_name' => "Drill-Down Analysis - " . now()->format('Y-m-d'),
            'description' => "Comprehensive drill-down analysis from corporate totals to individual sources",
            'reporting_year' => $analysis['reporting_year'],
            'corporate_total' => $analysis['drill_down_levels']['level_1_corporate'],
            'scope_breakdown' => $analysis['drill_down_levels']['level_2_scope'],
            'facility_breakdown' => $analysis['drill_down_levels']['level_3_facility'],
            'source_breakdown' => $analysis['drill_down_levels']['level_4_source'],
            'activity_detail' => $analysis['drill_down_levels']['level_5_activity'],
            'supplier_breakdown' => $analysis['drill_down_levels']['level_6_supplier'] ?? null,
            'traceability_matrix' => $analysis['traceability_matrix'],
            'drill_down_paths' => $analysis['drill_down_paths'],
            'summary_statistics' => $analysis['summary_statistics'],
            'analysis_date' => now(),
            'analyzed_by' => auth()->user()?->name ?? 'System',
            'status' => 'completed',
        ]);
    }
}
