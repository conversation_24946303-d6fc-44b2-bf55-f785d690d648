<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\MeasurementUnit;
use App\Models\GreenhouseGas;
use Illuminate\Support\Facades\DB;

class ImportTable1PublicTransportFactors extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:table1-public-transport-factors';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Table 1: CO2, CH4, and N2O Emission Factors by Passenger/Vehicle Distance (Public Transport)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of Table 1: Public Transport Emission Factors...');

        // Table 1 data: [Region, Transport_Type, Class, CO2_EF, CO2_Unit, CH4_EF, CH4_Unit, N2O_EF, N2O_Unit]
        $table1_data = [
            // UK Air Travel
            ['UK', 'Air - Domestic', 'Average Passenger', 0.129, 'kg/passenger-kilometer', 0.0040, 'g/passenger-kilometer', 0.0041, 'g/passenger-kilometer'],
            ['UK', 'Air - Short Haul', 'Average Passenger', 0.085, 'kg/passenger-kilometer', 0.0004, 'g/passenger-kilometer', 0.0026, 'g/passenger-kilometer'],
            ['UK', 'Air - Short Haul', 'Economy Class', 0.079, 'kg/passenger-kilometer', 0.0004, 'g/passenger-kilometer', 0.0025, 'g/passenger-kilometer'],
            ['UK', 'Air - Short Haul', 'Business Class', 0.119, 'kg/passenger-kilometer', 0.0004, 'g/passenger-kilometer', 0.0038, 'g/passenger-kilometer'],
            ['UK', 'Air - Long Haul', 'Average Passenger', 0.101, 'kg/passenger-kilometer', 0.0004, 'g/passenger-kilometer', 0.0032, 'g/passenger-kilometer'],
            ['UK', 'Air - Long Haul', 'Economy Class', 0.077, 'kg/passenger-kilometer', 0.0004, 'g/passenger-kilometer', 0.0024, 'g/passenger-kilometer'],
            ['UK', 'Air - Long Haul', 'Premium Economy Class', 0.124, 'kg/passenger-kilometer', 0.0004, 'g/passenger-kilometer', 0.0039, 'g/passenger-kilometer'],
            ['UK', 'Air - Long Haul', 'Business Class', 0.225, 'kg/passenger-kilometer', 0.0008, 'g/passenger-kilometer', 0.0071, 'g/passenger-kilometer'],
            ['UK', 'Air - Long Haul', 'First Class', 0.310, 'kg/passenger-kilometer', 0.0008, 'g/passenger-kilometer', 0.0098, 'g/passenger-kilometer'],
            ['UK', 'Air - International', 'Average Passenger', 0.095, 'kg/passenger-kilometer', 0.0004, 'g/passenger-kilometer', 0.0031, 'g/passenger-kilometer'],
            ['UK', 'Air - International', 'Economy class', 0.078, 'kg/passenger-kilometer', 0.0004, 'g/passenger-kilometer', 0.0025, 'g/passenger-kilometer'],
            ['UK', 'Air - International', 'Premium economy class', 0.118, 'kg/passenger-kilometer', 0.0004, 'g/passenger-kilometer', 0.0037, 'g/passenger-kilometer'],
            ['UK', 'Air - International', 'Business class', 0.214, 'kg/passenger-kilometer', 0.0004, 'g/passenger-kilometer', 0.0068, 'g/passenger-kilometer'],
            ['UK', 'Air - International', 'First class', 0.295, 'kg/passenger-kilometer', 0.0008, 'g/passenger-kilometer', 0.0094, 'g/passenger-kilometer'],
            ['UK', 'Rail', 'Light Rail and Tram', 0.026, 'kg/passenger-kilometer', 0.0014, 'g/passenger-kilometer', 0.0006, 'g/passenger-kilometer'],
            ['UK', 'Rail', 'National Rail', 0.035, 'kg/passenger-kilometer', 0.0029, 'g/passenger-kilometer', 0.0013, 'g/passenger-kilometer'],
            ['UK', 'Taxi', '', 0.147, 'kg/passenger-kilometer', 0.0001, 'g/passenger-kilometer', 0.0145, 'g/passenger-kilometer'],
            ['UK', 'Bus', 'Local Bus', 0.117, 'kg/passenger-kilometer', 0.0004, 'g/passenger-kilometer', 0.0016, 'g/passenger-kilometer'],
            ['UK', 'Bus', 'Coach', 0.027, 'kg/passenger-kilometer', 0.0004, 'g/passenger-kilometer', 0.0018, 'g/passenger-kilometer'],
            ['UK', 'Bus', 'Average Local Bus', 0.101, 'kg/passenger-kilometer', 0.0004, 'g/passenger-kilometer', 0.0026, 'g/passenger-kilometer'],
            ['UK', 'Average Ferry', '', 0.111, 'kg/passenger-kilometer', 0.0013, 'g/passenger-kilometer', 0.0051, 'g/passenger-kilometer'],

            // US Air Travel
            ['US', 'Air Travel - Short Haul', '< 300 miles', 0.201, 'kg/passenger-mile', 0.0064, 'g/passenger-mile', 0.0066, 'g/passenger-mile'],
            ['US', 'Air - Medium Haul', '> 300 miles and < 2300 miles', 0.129, 'kg/passenger-mile', 0.0006, 'g/passenger-mile', 0.0041, 'g/passenger-mile'],
            ['US', 'Air - Long Haul', '>= 2300 miles', 0.103, 'kg/passenger-mile', 0.0006, 'g/passenger-mile', 0.0032, 'g/passenger-mile'],
            ['US', 'Intercity Rail', 'Northeast Corridor', 0.058, 'kg/passenger-mile', 0.0555, 'g/passenger-mile', 0.0007, 'g/passenger-mile'],
            ['US', 'Intercity Rail', 'Other Routes', 0.15, 'kg/passenger-mile', 0.0117, 'g/passenger-mile', 0.0038, 'g/passenger-mile'],
            ['US', 'Intercity Rail', 'National Average', 0.113, 'kg/passenger-mile', 0.0562, 'g/passenger-mile', 0.0026, 'g/passenger-mile'],
            ['US', 'Commuter Rail', '', 0.103, 'kg/passenger-mile', 0.0105, 'g/passenger-mile', 0.0009, 'g/passenger-mile'],
            ['US', 'Transit Rail', 'Subway, Tram', 0.069, 'kg/passenger-mile', 0.0076, 'g/passenger-mile', 0.001, 'g/passenger-mile'],
            ['US', 'Bus', '', 0.071, 'kg/passenger-mile', 0.00, 'g/passenger-mile', 0.0021, 'g/passenger-mile'],

            // Vehicle-Distance factors
            ['US', 'Passenger Car', '', 0.175, 'kg/vehicle-mile', 0.005, 'g/vehicle-mile', 0.003, 'g/vehicle-mile'],
            ['US', 'Light-Duty Truck', '', 0.265, 'kg/vehicle-mile', 0.026, 'g/vehicle-mile', 0.023, 'g/vehicle-mile'],
            ['US', 'Motorcycle', '', 0.377, 'kg/vehicle-mile', 0, 'g/vehicle-mile', 0.019, 'g/vehicle-mile']
        ];

        try {
            DB::beginTransaction();

            // Get or create mobile combustion sector
            $sector = GhgProtocolSector::firstOrCreate([
                'name' => 'mobile_combustion'
            ], [
                'code' => 'MOBILE_COMB',
                'description' => 'Mobile Combustion Sources',
                'scope_category' => 'Scope 1',
                'is_active' => true,
                'sort_order' => 1
            ]);

            // Get greenhouse gases
            $co2_gas = GreenhouseGas::where('chemical_formula', 'CO2')->first();
            $ch4_gas = GreenhouseGas::where('chemical_formula', 'CH4')->first();
            $n2o_gas = GreenhouseGas::where('chemical_formula', 'N2O')->first();

            if (!$co2_gas || !$ch4_gas || !$n2o_gas) {
                $this->error('Required gas records not found. Please ensure greenhouse gases are seeded.');
                return 1;
            }

            // Get or create measurement units
            $unit_kg_pass_km = MeasurementUnit::firstOrCreate([
                'name' => 'kg/passenger-kilometer'
            ], [
                'symbol' => 'kg/pass-km',
                'description' => 'Kilograms per passenger-kilometer',
                'unit_type' => 'emission_factor'
            ]);

            $unit_g_pass_km = MeasurementUnit::firstOrCreate([
                'name' => 'g/passenger-kilometer'
            ], [
                'symbol' => 'g/pass-km',
                'description' => 'Grams per passenger-kilometer',
                'unit_type' => 'emission_factor'
            ]);

            $unit_kg_pass_mile = MeasurementUnit::firstOrCreate([
                'name' => 'kg/passenger-mile'
            ], [
                'symbol' => 'kg/pass-mi',
                'description' => 'Kilograms per passenger-mile',
                'unit_type' => 'emission_factor'
            ]);

            $unit_g_pass_mile = MeasurementUnit::firstOrCreate([
                'name' => 'g/passenger-mile'
            ], [
                'symbol' => 'g/pass-mi',
                'description' => 'Grams per passenger-mile',
                'unit_type' => 'emission_factor'
            ]);

            $unit_kg_veh_mile = MeasurementUnit::firstOrCreate([
                'name' => 'kg/vehicle-mile'
            ], [
                'symbol' => 'kg/veh-mi',
                'description' => 'Kilograms per vehicle-mile',
                'unit_type' => 'emission_factor'
            ]);

            $unit_g_veh_mile = MeasurementUnit::firstOrCreate([
                'name' => 'g/vehicle-mile'
            ], [
                'symbol' => 'g/veh-mi',
                'description' => 'Grams per vehicle-mile',
                'unit_type' => 'emission_factor'
            ]);

            $imported_count = 0;

            foreach ($table1_data as $row) {
                [$region_name, $transport_type, $class, $co2_ef, $co2_unit, $ch4_ef, $ch4_unit, $n2o_ef, $n2o_unit] = $row;

                // Get region
                $region_code = match($region_name) {
                    'UK' => 'GB',
                    'US' => 'US'
                };

                $region = GhgProtocolRegion::where('code', $region_code)->first();
                if (!$region) {
                    $this->error("Region {$region_code} not found.");
                    continue;
                }

                // Create activity name
                $activity_parts = array_filter([$transport_type, $class]);
                $activity_name = implode(' - ', $activity_parts);

                // Find or create activity
                $activity = GhgProtocolActivity::where('name', $activity_name)
                    ->where('sector_id', $sector->id)
                    ->first();

                if (!$activity) {
                    // Create shorter code for database constraint with region prefix
                    $base_code = strtoupper(str_replace([' ', '-', '(', ')', ',', '.', '/', '%'], ['_', '_', '', '', '_', '', '_', 'PCT'], $activity_name));
                    $short_code = $region_code . '_' . substr($base_code, 0, 40);

                    $activity = GhgProtocolActivity::create([
                        'name' => $activity_name,
                        'code' => $short_code,
                        'sector_id' => $sector->id,
                        'description' => "Public transport - {$activity_name}",
                        'activity_type' => 'public_transport',
                        'transport_type' => $transport_type,
                        'transport_class' => $class ?: null,
                        'applicable_scopes' => [3],
                        'calculation_methods' => ['distance_based'],
                        'is_active' => true,
                        'sort_order' => 0
                    ]);
                }

                // Determine the correct units
                $co2_unit_id = match($co2_unit) {
                    'kg/passenger-kilometer' => $unit_kg_pass_km->id,
                    'kg/passenger-mile' => $unit_kg_pass_mile->id,
                    'kg/vehicle-mile' => $unit_kg_veh_mile->id,
                    default => $unit_kg_pass_km->id
                };

                $ch4_unit_id = match($ch4_unit) {
                    'g/passenger-kilometer' => $unit_g_pass_km->id,
                    'g/passenger-mile' => $unit_g_pass_mile->id,
                    'g/vehicle-mile' => $unit_g_veh_mile->id,
                    default => $unit_g_pass_km->id
                };

                $n2o_unit_id = match($n2o_unit) {
                    'g/passenger-kilometer' => $unit_g_pass_km->id,
                    'g/passenger-mile' => $unit_g_pass_mile->id,
                    'g/vehicle-mile' => $unit_g_veh_mile->id,
                    default => $unit_g_pass_km->id
                };

                // Create CO2 emission factor
                if ($co2_ef !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "{$region_name}-{$transport_type}-{$class}-CO2",
                        'name' => "CO2 Factor - {$activity_name} ({$region_name})",
                        'description' => "CO2 emission factor for {$activity_name} in {$region_name}",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $region->id,
                        'gas_id' => $co2_gas->id,
                        'calculation_method' => 'Distance-based emission factor',
                        'methodology_reference' => 'GHG Protocol Mobile Combustion Tool',
                        'factor_value' => $co2_ef,
                        'co2_factor' => $co2_ef,
                        'input_unit_id' => $co2_unit_id,
                        'output_unit_id' => $co2_unit_id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 1. CO2, CH4, and N2O Emission Factors by Passenger/Vehicle Distance (Public Transport)',
                        'metadata' => [
                            'transport_type' => $transport_type,
                            'transport_class' => $class,
                            'table_source' => 'Table 1 - Public Transport Distance-based',
                            'region' => $region_name,
                            'calculation_basis' => 'passenger_distance'
                        ],
                        'is_default' => true,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 400
                    ]);
                    $imported_count++;
                }

                // Create CH4 emission factor
                if ($ch4_ef !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "{$region_name}-{$transport_type}-{$class}-CH4",
                        'name' => "CH4 Factor - {$activity_name} ({$region_name})",
                        'description' => "CH4 emission factor for {$activity_name} in {$region_name}",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $region->id,
                        'gas_id' => $ch4_gas->id,
                        'calculation_method' => 'Distance-based emission factor',
                        'methodology_reference' => 'GHG Protocol Mobile Combustion Tool',
                        'factor_value' => $ch4_ef,
                        'ch4_factor' => $ch4_ef,
                        'input_unit_id' => $ch4_unit_id,
                        'output_unit_id' => $ch4_unit_id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 1. CO2, CH4, and N2O Emission Factors by Passenger/Vehicle Distance (Public Transport)',
                        'metadata' => [
                            'transport_type' => $transport_type,
                            'transport_class' => $class,
                            'table_source' => 'Table 1 - Public Transport Distance-based',
                            'region' => $region_name,
                            'calculation_basis' => 'passenger_distance'
                        ],
                        'is_default' => true,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 400
                    ]);
                    $imported_count++;
                }

                // Create N2O emission factor
                if ($n2o_ef !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "{$region_name}-{$transport_type}-{$class}-N2O",
                        'name' => "N2O Factor - {$activity_name} ({$region_name})",
                        'description' => "N2O emission factor for {$activity_name} in {$region_name}",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $region->id,
                        'gas_id' => $n2o_gas->id,
                        'calculation_method' => 'Distance-based emission factor',
                        'methodology_reference' => 'GHG Protocol Mobile Combustion Tool',
                        'factor_value' => $n2o_ef,
                        'n2o_factor' => $n2o_ef,
                        'input_unit_id' => $n2o_unit_id,
                        'output_unit_id' => $n2o_unit_id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 1. CO2, CH4, and N2O Emission Factors by Passenger/Vehicle Distance (Public Transport)',
                        'metadata' => [
                            'transport_type' => $transport_type,
                            'transport_class' => $class,
                            'table_source' => 'Table 1 - Public Transport Distance-based',
                            'region' => $region_name,
                            'calculation_basis' => 'passenger_distance'
                        ],
                        'is_default' => true,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 400
                    ]);
                    $imported_count++;
                }

                $this->info("Imported: {$activity_name} ({$region_name})");
            }

            DB::commit();

            $this->info("\nSuccessfully imported {$imported_count} public transport emission factors from Table 1.");

        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Error importing data: " . $e->getMessage());
            return 1;
        }

        $this->info("\nImport completed successfully!");
        $this->info("Table 1 public transport emission factors cover:");
        $this->info("- Air travel: Domestic, short/long haul, international with class distinctions");
        $this->info("- Rail: Light rail, tram, national rail, intercity, commuter, transit");
        $this->info("- Road: Taxi, bus (local/coach), passenger cars, light-duty trucks, motorcycles");
        $this->info("- Ferry: Average ferry transport");
        $this->info("- Regional coverage: UK (kg/passenger-km) and US (kg/passenger-mile)");
        $this->info("- All three GHGs: CO2, CH4, and N2O for comprehensive Scope 3 reporting");
        return 0;
    }
}
