<?php

namespace App\Console\Commands;

use App\Services\DataLakeService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ManageDataLakeCommand extends Command
{
    protected $signature = 'data-lake:manage 
                            {action : Action to perform (stats, archive, cleanup, query)}
                            {--source= : Filter by data source}
                            {--type= : Filter by data type}
                            {--facility= : Filter by facility ID}
                            {--from-date= : Start date for filtering (Y-m-d)}
                            {--to-date= : End date for filtering (Y-m-d)}
                            {--limit=100 : Limit number of results}
                            {--dry-run : Run without making changes}';

    protected $description = 'Manage data lake operations (stats, archival, cleanup, queries)';

    protected DataLakeService $dataLakeService;

    public function __construct(DataLakeService $dataLakeService)
    {
        parent::__construct();
        $this->dataLakeService = $dataLakeService;
    }

    public function handle(): int
    {
        $action = $this->argument('action');
        $dryRun = $this->option('dry-run');

        if ($dryRun && in_array($action, ['archive', 'cleanup'])) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
        }

        try {
            switch ($action) {
                case 'stats':
                    return $this->showStatistics();
                
                case 'archive':
                    return $this->archiveData($dryRun);
                
                case 'cleanup':
                    return $this->cleanupData($dryRun);
                
                case 'query':
                    return $this->queryData();
                
                default:
                    $this->error("❌ Unknown action: {$action}");
                    $this->info("Available actions: stats, archive, cleanup, query");
                    return 1;
            }

        } catch (\Exception $e) {
            $this->error("❌ Data lake operation failed: {$e->getMessage()}");
            Log::error('Data lake command failed', [
                'action' => $action,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }

    protected function showStatistics(): int
    {
        $this->info('📊 Data Lake Statistics');
        $this->line('');

        $stats = $this->dataLakeService->getStatistics();

        // Overall statistics
        $this->info('🏗️ Overall Statistics:');
        $this->line("  📋 Total Records: " . number_format($stats['total_records']));
        $this->line("  💾 Total Size: " . number_format($stats['total_size_mb'], 2) . " MB");
        $this->line("  📦 Archived Records: " . number_format($stats['archived_records']));
        $this->line("  🔗 Data Sources: {$stats['sources']}");
        $this->line("  📂 Data Types: {$stats['data_types']}");
        $this->line("  📈 Recent Activity (30 days): " . number_format($stats['recent_activity']));
        $this->line('');

        // By source
        if (!empty($stats['by_source'])) {
            $this->info('📊 Records by Source:');
            foreach ($stats['by_source'] as $source => $count) {
                $percentage = $stats['total_records'] > 0 ? ($count / $stats['total_records']) * 100 : 0;
                $this->line("  📍 {$source}: " . number_format($count) . " (" . number_format($percentage, 1) . "%)");
            }
            $this->line('');
        }

        // By data type
        if (!empty($stats['by_data_type'])) {
            $this->info('📂 Records by Data Type:');
            foreach ($stats['by_data_type'] as $type => $count) {
                $percentage = $stats['total_records'] > 0 ? ($count / $stats['total_records']) * 100 : 0;
                $this->line("  📄 {$type}: " . number_format($count) . " (" . number_format($percentage, 1) . "%)");
            }
            $this->line('');
        }

        // Storage recommendations
        $this->info('💡 Recommendations:');
        if ($stats['total_size_mb'] > 1000) {
            $this->line("  ⚠️ Large data lake size ({$stats['total_size_mb']} MB) - consider archiving old data");
        }
        
        if ($stats['archived_records'] > $stats['total_records'] * 0.5) {
            $this->line("  ⚠️ High archived ratio - consider cleanup of old archived data");
        }
        
        if ($stats['recent_activity'] < $stats['total_records'] * 0.1) {
            $this->line("  ℹ️ Low recent activity - data lake appears stable");
        }

        return 0;
    }

    protected function archiveData(bool $dryRun): int
    {
        $this->info('📦 Starting data archival process...');

        $fromDate = $this->option('from-date');
        $cutoffDate = $fromDate ? Carbon::parse($fromDate) : null;

        if ($dryRun) {
            $this->warn('🧪 DRY RUN - Would archive data older than ' . ($cutoffDate ? $cutoffDate->format('Y-m-d') : 'default retention period'));
            return 0;
        }

        $success = $this->dataLakeService->archiveOldData($cutoffDate);

        if ($success) {
            $this->info('✅ Data archival completed successfully');
            
            // Show updated statistics
            $stats = $this->dataLakeService->getStatistics();
            $this->line("  📦 Total Archived Records: {$stats['archived_records']}");
        } else {
            $this->error('❌ Data archival failed');
            return 1;
        }

        return 0;
    }

    protected function cleanupData(bool $dryRun): int
    {
        $this->info('🧹 Starting data cleanup process...');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN - Would cleanup expired archived data');
            return 0;
        }

        $deletedCount = $this->dataLakeService->cleanupExpiredData();

        $this->info("✅ Data cleanup completed successfully");
        $this->line("  🗑️ Deleted Records: {$deletedCount}");

        // Show updated statistics
        $stats = $this->dataLakeService->getStatistics();
        $this->line("  📋 Remaining Records: {$stats['total_records']}");
        $this->line("  💾 Current Size: " . number_format($stats['total_size_mb'], 2) . " MB");

        return 0;
    }

    protected function queryData(): int
    {
        $this->info('🔍 Querying data lake...');

        $criteria = [];

        // Build query criteria from options
        if ($source = $this->option('source')) {
            $criteria['source'] = $source;
        }

        if ($type = $this->option('type')) {
            $criteria['data_type'] = $type;
        }

        if ($facilityId = $this->option('facility')) {
            $criteria['facility_id'] = $facilityId;
        }

        if ($fromDate = $this->option('from-date')) {
            $criteria['from_date'] = Carbon::parse($fromDate);
        }

        if ($toDate = $this->option('to-date')) {
            $criteria['to_date'] = Carbon::parse($toDate);
        }

        $criteria['limit'] = (int) $this->option('limit');
        $criteria['latest_version_only'] = true;

        $this->line('🔍 Query Criteria:');
        foreach ($criteria as $key => $value) {
            if ($value instanceof Carbon) {
                $value = $value->format('Y-m-d H:i:s');
            }
            $this->line("  {$key}: {$value}");
        }
        $this->line('');

        $results = $this->dataLakeService->queryRawData($criteria);

        $this->info("📋 Query Results: {$results->count()} records found");
        $this->line('');

        if ($results->isEmpty()) {
            $this->warn('No records found matching the criteria');
            return 0;
        }

        // Display sample results
        $this->info('📄 Sample Records:');
        foreach ($results->take(5) as $index => $record) {
            $this->line("  " . ($index + 1) . ". ID: {$record['id']}");
            $this->line("     Source: {$record['source']}");
            $this->line("     Type: {$record['data_type']}");
            $this->line("     Created: {$record['created_at']}");
            
            if (isset($record['facility_id'])) {
                $this->line("     Facility: {$record['facility_id']}");
            }
            
            // Show sample of raw data
            if (isset($record['raw_data']) && is_array($record['raw_data'])) {
                $sampleData = array_slice($record['raw_data'], 0, 3, true);
                $this->line("     Sample Data: " . json_encode($sampleData));
            }
            
            $this->line('');
        }

        if ($results->count() > 5) {
            $remaining = $results->count() - 5;
            $this->line("... and {$remaining} more records");
        }

        // Summary by source and type
        $bySource = $results->groupBy('source')->map->count();
        $byType = $results->groupBy('data_type')->map->count();

        if ($bySource->count() > 1) {
            $this->info('📊 Results by Source:');
            foreach ($bySource as $source => $count) {
                $this->line("  {$source}: {$count}");
            }
            $this->line('');
        }

        if ($byType->count() > 1) {
            $this->info('📂 Results by Type:');
            foreach ($byType as $type => $count) {
                $this->line("  {$type}: {$count}");
            }
        }

        return 0;
    }
}
