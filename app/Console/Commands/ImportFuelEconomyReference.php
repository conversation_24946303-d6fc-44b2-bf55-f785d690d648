<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\MeasurementUnit;
use Illuminate\Support\Facades\DB;

class ImportFuelEconomyReference extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:fuel-economy-reference';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Reference Table: Average Fuel Economy per Vehicle Type';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of Fuel Economy Reference Table...');

        // Reference table data - Average Fuel Economy per Vehicle Type
        // Format: [Vehicle Type, Average Fuel Economy (mpg), Unit]
        $fuel_economy_data = [
            ['Passenger Cars', 24.5, 'mpg'],
            ['Motorcycles', 44, 'mpg'],
            ['Other Buses (Urban Heavy-Duty Vehicles)', 7.3, 'mpg'],
            ['Other Buses (City Vehicles)', 17.8, 'mpg'],
            ['School Buses (Urban Heavy-Duty Trucks)', 7.46, 'mpg'],
            ['Combination Trucks', 6.4, 'mpg']
        ];

        try {
            DB::beginTransaction();

            // Get or create the mobile combustion sector
            $sector = GhgProtocolSector::firstOrCreate([
                'name' => 'mobile_combustion'
            ], [
                'display_name' => 'Mobile Combustion',
                'description' => 'Mobile combustion sources including vehicles, aircraft, ships, etc.',
                'sort_order' => 2,
                'is_active' => true
            ]);

            // Get or create global region for reference data
            $global_region = GhgProtocolRegion::firstOrCreate([
                'code' => 'GLOBAL'
            ], [
                'name' => 'Global',
                'region_type' => 'global',
                'description' => 'Global/International reference values',
                'is_active' => true,
                'sort_order' => 0
            ]);

            // Get or create measurement units
            $unit_mpg = MeasurementUnit::firstOrCreate([
                'name' => 'mpg'
            ], [
                'symbol' => 'mpg',
                'description' => 'Miles per gallon',
                'unit_type' => 'fuel_economy'
            ]);

            $imported_count = 0;

            foreach ($fuel_economy_data as $row) {
                [$vehicle_type, $fuel_economy, $unit] = $row;

                // Get or create activity
                $activity_name = "{$vehicle_type} - Fuel Economy Reference";
                // Create shorter code for database constraint
                $short_code = strtoupper(str_replace([' ', '(', ')', '-', ','], ['_', '', '', '_', ''], $vehicle_type));
                $short_code = substr($short_code, 0, 30) . '_FUEL_REF'; // Limit to 40 chars total

                $activity = GhgProtocolActivity::firstOrCreate([
                    'name' => $activity_name,
                    'sector_id' => $sector->id
                ], [
                    'code' => $short_code,
                    'description' => "Reference fuel economy data for {$vehicle_type}",
                    'activity_type' => 'reference_data',
                    'vehicle_type' => $vehicle_type,
                    'applicable_scopes' => [1],
                    'calculation_methods' => ['fuel_economy_reference'],
                    'is_active' => true,
                    'sort_order' => 0
                ]);

                // Create fuel economy reference factor
                GhgProtocolEmissionFactor::create([
                    'code' => "REF-FUEL-ECONOMY-{$vehicle_type}",
                    'name' => "Fuel Economy Reference - {$vehicle_type}",
                    'description' => "Average fuel economy reference value for {$vehicle_type}",
                    'sector_id' => $sector->id,
                    'activity_id' => $activity->id,
                    'region_id' => $global_region->id,
                    'gas_id' => 1, // Using CO2 as placeholder since fuel economy relates to CO2 emissions
                    'calculation_method' => 'Reference fuel economy value',
                    'methodology_reference' => 'GHG Protocol Mobile Combustion Tool - Reference Table',
                    'factor_value' => $fuel_economy,
                    'input_unit_id' => $unit_mpg->id,
                    'output_unit_id' => $unit_mpg->id,
                    'data_quality_rating' => 'Medium',
                    'data_source' => 'GHG Protocol',
                    'data_source_detail' => 'Reference Table: Average Fuel Economy per Vehicle Type',
                    'metadata' => [
                        'vehicle_type' => $vehicle_type,
                        'fuel_economy_mpg' => $fuel_economy,
                        'data_type' => 'fuel_economy_reference',
                        'table_source' => 'Reference Table'
                    ],
                    'is_default' => true,
                    'is_active' => true,
                    'tier_level' => 3, // Reference data is typically Tier 3
                    'priority' => 50 // Lower priority than specific emission factors
                ]);

                $imported_count++;
                $this->info("Imported: {$vehicle_type} - {$fuel_economy} {$unit}");
            }

            DB::commit();

            $this->info("\nSuccessfully imported {$imported_count} fuel economy reference values.");
            $this->info("Total records created:");
            $this->info("- Reference factors: {$imported_count}");
            $this->info("- Activities: {$imported_count}");

        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Error importing data: " . $e->getMessage());
            return 1;
        }

        $this->info("\nImport completed successfully!");
        $this->info("Note: These are reference fuel economy values for calculation purposes.");
        return 0;
    }
}
