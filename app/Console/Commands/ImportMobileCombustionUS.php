<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\MeasurementUnit;
use Illuminate\Support\Facades\DB;

class ImportMobileCombustionUS extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:mobile-combustion-us';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Mobile Combustion CH4 and N2O Emission Factors for U.S. and Other Regions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of Mobile Combustion CH4 and N2O Emission Factors...');

        // Mobile combustion data from Table 1 - CH4 and N2O Emission Factors
        // Format: [Region, Vehicle, Vehicle Year, Fuel, CH4_EF, CH4_Unit, N2O_EF, N2O_Unit]
        $mobile_data = [
            // Other region data
            ['Other', 'Bus', '', 'Methanol', 0.403, 'g/mi', 0.105, 'g/mi'],
            ['Other', 'Bus', '', 'CNG', 7.715, 'g/mi', 0.104, 'g/mi'],
            ['Other', 'Bus', '', 'LNG', 7.715, 'g/mi', 0.104, 'g/mi'],
            ['Other', 'Light Duty Vehicles', '', 'Methanol', 0.006, 'g/mi', 0.098, 'g/mi'],
            ['Other', 'Light Duty Vehicles', '', 'CNG', 0.215, 'g/mi', 0.007, 'g/mi'],
            ['Other', 'Light Duty Vehicles', '', 'LPG', 0.215, 'g/mi', 0.007, 'g/mi'],
            ['Other', 'Light Duty Vehicles', '', 'Ethanol', 0.327, 'g/mi', 0.012, 'g/mi'],
            ['Other', 'Light Duty Vehicles', '', 'Methanol', 0.403, 'g/mi', 0.136, 'g/mi'],
            ['Other', 'Heavy Duty Vehicles', '', 'CNG', 2.583, 'g/mi', 0.185, 'g/mi'],
            ['Other', 'Heavy Duty Vehicles', '', 'LNG', 2.583, 'g/mi', 0.185, 'g/mi'],
            ['Other', 'Heavy Duty Vehicles', '', 'Ethanol', 4.301, 'g/mi', 0.274, 'g/mi'],
            ['Other', 'Heavy Duty Vehicles', '', 'Methanol', 4.301, 'g/mi', 0.274, 'g/mi'],
            ['Other', 'Heavy Duty Vehicles', '', 'Ethanol', 1.227, 'g/mi', 0.191, 'g/mi'],

            // US Passenger Car data - comprehensive years
            ['US', 'Passenger Car', '1970-1974', 'Gasoline', 1.623, 'g/mi', 0.0187, 'g/mi'],
            ['US', 'Passenger Car', '1975-1977', 'Gasoline', 0.1406, 'g/mi', 0.0468, 'g/mi'],
            ['US', 'Passenger Car', '1978-1979', 'Gasoline', 0.1326, 'g/mi', 0.0473, 'g/mi'],
            ['US', 'Passenger Car', '1980', 'Gasoline', 0.1326, 'g/mi', 0.0473, 'g/mi'],
            ['US', 'Passenger Car', '1981', 'Gasoline', 0.0827, 'g/mi', 0.0506, 'g/mi'],
            ['US', 'Passenger Car', '1982', 'Gasoline', 0.0702, 'g/mi', 0.0507, 'g/mi'],
            ['US', 'Passenger Car', '1983', 'Gasoline', 0.0702, 'g/mi', 0.0507, 'g/mi'],
            ['US', 'Passenger Car', '1984', 'Gasoline', 0.0702, 'g/mi', 0.0507, 'g/mi'],
            ['US', 'Passenger Car', '1985', 'Gasoline', 0.0631, 'g/mi', 0.0508, 'g/mi'],
            ['US', 'Passenger Car', '1986', 'Gasoline', 0.0631, 'g/mi', 0.0508, 'g/mi'],
            ['US', 'Passenger Car', '1987', 'Gasoline', 0.0337, 'g/mi', 0.0468, 'g/mi'],
            ['US', 'Passenger Car', '1988', 'Gasoline', 0.0326, 'g/mi', 0.0389, 'g/mi'],
            ['US', 'Passenger Car', '1989', 'Gasoline', 0.0326, 'g/mi', 0.0389, 'g/mi'],
            ['US', 'Passenger Car', '1990', 'Gasoline', 0.0176, 'g/mi', 0.0304, 'g/mi'],
            ['US', 'Passenger Car', '2000', 'Gasoline', 0.0176, 'g/mi', 0.0304, 'g/mi'],
            ['US', 'Passenger Car', '2001', 'Gasoline', 0.0100, 'g/mi', 0.0212, 'g/mi'],
            ['US', 'Passenger Car', '2002', 'Gasoline', 0.0100, 'g/mi', 0.0212, 'g/mi'],
            ['US', 'Passenger Car', '2003', 'Gasoline', 0.0069, 'g/mi', 0.0181, 'g/mi'],
            ['US', 'Passenger Car', '2004', 'Gasoline', 0.0079, 'g/mi', 0.0165, 'g/mi'],
            ['US', 'Passenger Car', '2005', 'Gasoline', 0.0079, 'g/mi', 0.0165, 'g/mi'],
            ['US', 'Passenger Car', '2006', 'Gasoline', 0.0076, 'g/mi', 0.0075, 'g/mi'],
            ['US', 'Passenger Car', '2007', 'Gasoline', 0.0072, 'g/mi', 0.0065, 'g/mi'],
            ['US', 'Passenger Car', '2008', 'Gasoline', 0.0072, 'g/mi', 0.0065, 'g/mi'],
            ['US', 'Passenger Car', '2009', 'Gasoline', 0.0071, 'g/mi', 0.0046, 'g/mi'],
            ['US', 'Passenger Car', '2010', 'Gasoline', 0.0071, 'g/mi', 0.0046, 'g/mi'],
            ['US', 'Passenger Car', '2011', 'Gasoline', 0.0071, 'g/mi', 0.0046, 'g/mi'],
            ['US', 'Passenger Car', '2012', 'Gasoline', 0.0071, 'g/mi', 0.0046, 'g/mi'],
            ['US', 'Passenger Car', '2013', 'Gasoline', 0.0071, 'g/mi', 0.0046, 'g/mi'],
            ['US', 'Passenger Car', '2014', 'Gasoline', 0.0071, 'g/mi', 0.0046, 'g/mi'],
            ['US', 'Passenger Car', '2015', 'Gasoline', 0.0071, 'g/mi', 0.0046, 'g/mi'],
            ['US', 'Passenger Car', '2016', 'Gasoline', 0.0060, 'g/mi', 0.0039, 'g/mi'],
            ['US', 'Passenger Car', '2017', 'Gasoline', 0.0054, 'g/mi', 0.0018, 'g/mi'],
            ['US', 'Passenger Car', '2018', 'Gasoline', 0.0054, 'g/mi', 0.0018, 'g/mi'],
            ['US', 'Passenger Car', '2019', 'Gasoline', 0.0041, 'g/mi', 0.0015, 'g/mi'],
            ['US', 'Passenger Car', '2020', 'Gasoline', 0.0040, 'g/mi', 0.0014, 'g/mi'],
            ['US', 'Passenger Car', '2021', 'Gasoline', 0.0040, 'g/mi', 0.0014, 'g/mi'],
            ['US', 'Passenger Car', '1990-1995', 'Diesel', 0.0008, 'g/mi', 0.0012, 'g/mi'],
            ['US', 'Passenger Car', '1996-2006', 'Diesel', 0.0002, 'g/mi', 0.0011, 'g/mi'],
            ['US', 'Passenger Car', '2007-2030', 'Diesel', 0.0001, 'g/mi', 0.0011, 'g/mi'],

            // Light-Duty Cars
            ['US', 'Light-Duty Cars', '1990-1995', 'Methanol', 0.013, 'g/mi', 0.004, 'g/mi'],
            ['US', 'Light-Duty Cars', '1990-2006', 'Ethanol', 0.013, 'g/mi', 0.004, 'g/mi'],
            ['US', 'Light-Duty Cars', '2007-2030', 'Ethanol', 0.013, 'g/mi', 0.004, 'g/mi'],
            ['US', 'Light-Duty Cars', '1973-1974', 'LNG', 0.036, 'g/mi', 0.018, 'g/mi'],
            ['US', 'Light-Duty Cars', '1975', 'Biodiesel', 0.036, 'g/mi', 0.018, 'g/mi'],

            // Light-Duty Trucks - comprehensive data
            ['US', 'Light-Duty Trucks', '1973-1974', 'Gasoline', 1.036, 'g/mi', 0.018, 'g/mi'],
            ['US', 'Light-Duty Trucks', '1975', 'Gasoline', 0.1564, 'g/mi', 0.0471, 'g/mi'],
            ['US', 'Light-Duty Trucks', '1976', 'Gasoline', 0.1564, 'g/mi', 0.0465, 'g/mi'],
            ['US', 'Light-Duty Trucks', '1977-1978', 'Gasoline', 0.1516, 'g/mi', 0.0534, 'g/mi'],
            ['US', 'Light-Duty Trucks', '1979', 'Gasoline', 0.1516, 'g/mi', 0.0534, 'g/mi'],
            ['US', 'Light-Duty Trucks', '1979-1980', 'Gasoline', 0.1360, 'g/mi', 0.0508, 'g/mi'],
            ['US', 'Light-Duty Trucks', '1981', 'Gasoline', 0.1479, 'g/mi', 0.0860, 'g/mi'],
            ['US', 'Light-Duty Trucks', '1982', 'Gasoline', 0.1368, 'g/mi', 0.0722, 'g/mi'],
            ['US', 'Light-Duty Trucks', '1983', 'Gasoline', 0.1368, 'g/mi', 0.0722, 'g/mi'],
            ['US', 'Light-Duty Trucks', '1984', 'Gasoline', 0.1368, 'g/mi', 0.0722, 'g/mi'],
            ['US', 'Light-Duty Trucks', '1985', 'Gasoline', 0.1368, 'g/mi', 0.0722, 'g/mi'],
            ['US', 'Light-Duty Trucks', '1986', 'Gasoline', 0.1146, 'g/mi', 0.0866, 'g/mi'],
            ['US', 'Light-Duty Trucks', '1987-1993', 'Gasoline', 0.0813, 'g/mi', 0.0145, 'g/mi'],
            ['US', 'Light-Duty Trucks', '1994', 'Gasoline', 0.0813, 'g/mi', 0.0145, 'g/mi'],

            // Medium-Duty Trucks
            ['US', 'Medium-Duty Trucks', 'CNG', 'CNG', 1.907, 'g/mi', 0.034, 'g/mi'],
            ['US', 'Medium-Duty Trucks', 'LPG', 'LPG', 1.907, 'g/mi', 0.034, 'g/mi'],
            ['US', 'Medium-Duty Trucks', 'LNG', 'LNG', 1.907, 'g/mi', 0.034, 'g/mi'],
            ['US', 'Medium-Duty Trucks', '1990-2006', 'Biodiesel', 0.040, 'g/mi', 0.005, 'g/mi'],
            ['US', 'Medium-Duty Trucks', '2007-2030', 'Diesel', 0.0051, 'g/mi', 0.0048, 'g/mi'],

            // Heavy-Duty Vehicles
            ['US', 'Heavy-Duty Vehicles', '<1960', 'Gasoline', 0.4904, 'g/mi', 0.090, 'g/mi'],
            ['US', 'Heavy-Duty Vehicles', '1960-1969', 'Gasoline', 0.4902, 'g/mi', 0.090, 'g/mi'],
            ['US', 'Heavy-Duty Vehicles', '1970-1984', 'Gasoline', 0.4902, 'g/mi', 0.1754, 'g/mi'],
            ['US', 'Heavy-Duty Vehicles', '1985-1987', 'Gasoline', 0.4902, 'g/mi', 0.1754, 'g/mi'],
            ['US', 'Heavy-Duty Vehicles', '1988', 'Gasoline', 0.4575, 'g/mi', 0.0569, 'g/mi'],
            ['US', 'Heavy-Duty Vehicles', '1989-1990', 'Gasoline', 0.3462, 'g/mi', 0.0415, 'g/mi'],
            ['US', 'Heavy-Duty Vehicles', '1991-1993', 'Gasoline', 0.3462, 'g/mi', 0.0415, 'g/mi'],
            ['US', 'Heavy-Duty Vehicles', '1994', 'Gasoline', 0.1724, 'g/mi', 0.0415, 'g/mi'],
            ['US', 'Heavy-Duty Vehicles', '1995', 'Gasoline', 0.0517, 'g/mi', 0.0069, 'g/mi'],
            ['US', 'Heavy-Duty Vehicles', '1996', 'Gasoline', 0.0462, 'g/mi', 0.0071, 'g/mi'],
            ['US', 'Heavy-Duty Vehicles', '1997', 'Gasoline', 0.0462, 'g/mi', 0.0071, 'g/mi'],
            ['US', 'Heavy-Duty Vehicles', '1998', 'Gasoline', 0.0412, 'g/mi', 0.0797, 'g/mi'],
            ['US', 'Heavy-Duty Vehicles', '1999', 'Gasoline', 0.0331, 'g/mi', 0.0018, 'g/mi'],
            ['US', 'Heavy-Duty Vehicles', '2000', 'Gasoline', 0.0331, 'g/mi', 0.0018, 'g/mi'],

            // Buses
            ['US', 'Bus', 'CNG', 'CNG', 2.753, 'g/mi', 0.017, 'g/mi'],
            ['US', 'Bus', 'LPG', 'LPG', 2.753, 'g/mi', 0.017, 'g/mi'],
            ['US', 'Bus', 'LNG', 'LNG', 2.753, 'g/mi', 0.017, 'g/mi'],
            ['US', 'Bus', '1990-1993', 'Biodiesel', 0.016, 'g/mi', 0.003, 'g/mi'],
            ['US', 'Bus', '1994-2006', 'Biodiesel', 0.016, 'g/mi', 0.003, 'g/mi'],
            ['US', 'Bus', '1996-2005', 'Gasoline', 0, 'g/mi', 0, 'g/mi'],
            ['US', 'Bus', '2006-2030', 'Gasoline', 0, 'g/mi', 0, 'g/mi'],
            ['US', 'Motorcycles', '1996-2005', 'Gasoline', 0, 'g/mi', 0, 'g/mi'],
            ['US', 'Motorcycles', '2006-2030', 'Gasoline', 0.027, 'g/mi', 0.0003, 'g/mi']
        ];

        try {
            DB::beginTransaction();

            // Get or create the mobile combustion sector
            $sector = GhgProtocolSector::firstOrCreate([
                'name' => 'Mobile Combustion'
            ], [
                'code' => 'MOBILE',
                'description' => 'Mobile combustion sources including vehicles, aircraft, ships, etc.',
                'scope' => 'Scope 1'
            ]);

            // Get or create measurement units
            $unit_gmi = MeasurementUnit::firstOrCreate([
                'name' => 'g/mi'
            ], [
                'symbol' => 'g/mi',
                'description' => 'Grams per mile',
                'unit_type' => 'emission_factor'
            ]);

            $imported_count = 0;

            foreach ($mobile_data as $row) {
                [$region_name, $vehicle_type, $vehicle_year, $fuel_type, $ch4_ef, $ch4_unit, $n2o_ef, $n2o_unit] = $row;

                // Get or create region
                $region = GhgProtocolRegion::firstOrCreate([
                    'name' => $region_name === 'US' ? 'United States' : $region_name
                ], [
                    'code' => $region_name === 'US' ? 'US' : strtoupper(substr($region_name, 0, 3)),
                    'description' => $region_name === 'US' ? 'United States of America' : $region_name
                ]);

                // Get or create activity
                $activity_name = "{$vehicle_type} - {$fuel_type}";
                $activity = GhgProtocolActivity::firstOrCreate([
                    'name' => $activity_name,
                    'sector_id' => $sector->id
                ], [
                    'code' => strtoupper(str_replace([' ', '-'], ['_', '_'], $activity_name)),
                    'description' => "Mobile combustion from {$vehicle_type} using {$fuel_type}",
                    'scope' => 'Scope 1'
                ]);

                // Create CH4 emission factor
                $ch4_factor = GhgProtocolEmissionFactor::create([
                    'code' => "{$region_name}-{$vehicle_type}-{$fuel_type}-{$vehicle_year}-CH4",
                    'name' => "Mobile Combustion CH4 Factor - {$vehicle_type} {$fuel_type} ({$region_name})",
                    'description' => "CH4 emission factor for {$vehicle_type} using {$fuel_type} in {$region_name}" .
                                   ($vehicle_year ? " for model years {$vehicle_year}" : ""),
                    'sector_id' => $sector->id,
                    'activity_id' => $activity->id,
                    'region_id' => $region->id,
                    'gas_id' => 2, // Assuming CH4 is gas_id 2
                    'calculation_method' => 'Vehicle distance-based emission factor',
                    'methodology_reference' => 'GHG Protocol Mobile Combustion Tool',
                    'factor_value' => $ch4_ef,
                    'ch4_factor' => $ch4_ef,
                    'input_unit_id' => $unit_gmi->id,
                    'output_unit_id' => $unit_gmi->id,
                    'data_quality_rating' => 'High',
                    'vintage_year' => $vehicle_year ? (int)substr($vehicle_year, 0, 4) : null,
                    'data_source' => 'GHG Protocol',
                    'data_source_detail' => 'Table 1. CH4 and N2O Emission Factors for U.S. and Other Regions by Vehicle Distance',
                    'metadata' => [
                        'vehicle_type' => $vehicle_type,
                        'fuel_type' => $fuel_type,
                        'vehicle_year' => $vehicle_year,
                        'gas_type' => 'CH4',
                        'table_source' => 'Table 1'
                    ],
                    'is_default' => false,
                    'is_active' => true,
                    'tier_level' => 2,
                    'priority' => 100
                ]);

                // Create N2O emission factor
                $n2o_factor = GhgProtocolEmissionFactor::create([
                    'code' => "{$region_name}-{$vehicle_type}-{$fuel_type}-{$vehicle_year}-N2O",
                    'name' => "Mobile Combustion N2O Factor - {$vehicle_type} {$fuel_type} ({$region_name})",
                    'description' => "N2O emission factor for {$vehicle_type} using {$fuel_type} in {$region_name}" .
                                   ($vehicle_year ? " for model years {$vehicle_year}" : ""),
                    'sector_id' => $sector->id,
                    'activity_id' => $activity->id,
                    'region_id' => $region->id,
                    'gas_id' => 3, // Assuming N2O is gas_id 3
                    'calculation_method' => 'Vehicle distance-based emission factor',
                    'methodology_reference' => 'GHG Protocol Mobile Combustion Tool',
                    'factor_value' => $n2o_ef,
                    'n2o_factor' => $n2o_ef,
                    'input_unit_id' => $unit_gmi->id,
                    'output_unit_id' => $unit_gmi->id,
                    'data_quality_rating' => 'High',
                    'vintage_year' => $vehicle_year ? (int)substr($vehicle_year, 0, 4) : null,
                    'data_source' => 'GHG Protocol',
                    'data_source_detail' => 'Table 1. CH4 and N2O Emission Factors for U.S. and Other Regions by Vehicle Distance',
                    'metadata' => [
                        'vehicle_type' => $vehicle_type,
                        'fuel_type' => $fuel_type,
                        'vehicle_year' => $vehicle_year,
                        'gas_type' => 'N2O',
                        'table_source' => 'Table 1'
                    ],
                    'is_default' => false,
                    'is_active' => true,
                    'tier_level' => 2,
                    'priority' => 100
                ]);

                $imported_count += 2; // CH4 + N2O factors
                $this->info("Imported: {$vehicle_type} - {$fuel_type} ({$region_name}) {$vehicle_year}");
            }

            DB::commit();

            $this->info("\nSuccessfully imported {$imported_count} mobile combustion emission factors.");
            $this->info("Total records created:");
            $this->info("- Emission factors: {$imported_count}");
            $this->info("- Activities: " . ($imported_count / 2));

        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Error importing data: " . $e->getMessage());
            return 1;
        }

        $this->info("\nImport completed successfully!");
        $this->info("Note: This is a sample import. The full dataset contains many more vehicle types and years.");
        return 0;
    }
}
