<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\MeasurementUnit;
use Illuminate\Support\Facades\DB;

class ImportUSElectricity2012 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:us-electricity-2012';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import 2012 US Regional Electricity Emission Factors from EPA eGRID';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of 2012 US Regional Electricity Emission Factors...');
        
        // 2012 US Regional Electricity Emission Factors Data
        $data_2012 = [
            ['ASCC Alaska Grid', 1268.73, 26.34, 7.59],
            ['ASCC Miscellaneous', 481.17, 18.65, 3.55],
            ['ERCOT All', 1143.04, 16.70, 12.33],
            ['FRCC All', 1125.35, 40.05, 11.85],
            ['HICC Miscellaneous', 1200.10, 68.08, 12.68],
            ['HICC Oahu', 1576.38, 90.41, 21.55],
            ['MRO East', 1522.57, 24.30, 25.55],
            ['MRO West', 1425.15, 27.60, 24.26],
            ['NPCC Long Island', 1201.20, 78.20, 9.87],
            ['NPCC New England', 637.90, 72.84, 10.71],
            ['NPCC NYC/Westchester', 696.70, 25.51, 2.93],
            ['NPCC Upstate NY', 408.80, 15.59, 3.83],
            ['RFC East', 858.56, 26.44, 11.49],
            ['RFC Michigan', 1569.23, 30.36, 24.12],
            ['RFC West', 1379.48, 17.11, 21.67],
            ['SERC Midwest', 1710.75, 19.58, 27.50],
            ['SERC Mississippi Valley', 1052.92, 20.95, 10.61],
            ['SERC South', 1149.05, 22.66, 15.49],
            ['SERC Tennessee Valley', 1337.15, 17.39, 20.78],
            ['SERC Virginia/Carolina', 932.87, 23.95, 14.60],
            ['SPP North', 1721.65, 20.22, 27.14],
            ['SPP South', 1538.63, 23.75, 19.98],
            ['WECC California', 650.31, 31.12, 5.67],
            ['WECC Northwest', 665.75, 12.60, 10.38],
            ['WECC Rockies', 1822.65, 21.66, 28.13],
            ['WECC Southwest', 1152.89, 18.65, 15.11]
        ];

        try {
            DB::beginTransaction();
            
            // Get or create the electricity sector
            $sector = GhgProtocolSector::firstOrCreate([
                'name' => 'Electricity'
            ], [
                'code' => 'ELEC',
                'description' => 'Electricity generation and consumption',
                'scope' => 'Scope 2'
            ]);
            
            // Get or create the electricity activity
            $activity = GhgProtocolActivity::firstOrCreate([
                'name' => 'Electricity Consumption',
                'sector_id' => $sector->id
            ], [
                'code' => 'ELEC-CONS',
                'description' => 'Electricity consumption from grid',
                'scope' => 'Scope 2'
            ]);
            
            // Get or create US region
            $region = GhgProtocolRegion::firstOrCreate([
                'name' => 'United States'
            ], [
                'code' => 'US',
                'description' => 'United States of America'
            ]);
            
            // Get lb/MWh unit
            $unit = MeasurementUnit::firstOrCreate([
                'name' => 'lb/MWh'
            ], [
                'symbol' => 'lb/MWh',
                'description' => 'Pounds per Megawatt hour',
                'unit_type' => 'emission_factor'
            ]);
            
            $imported_count = 0;
            
            foreach ($data_2012 as $row) {
                [$region_name, $co2_rate, $ch4_rate, $n2o_rate] = $row;
                
                // Create emission factor record
                $emission_factor = GhgProtocolEmissionFactor::create([
                    'code' => "US-ELEC-{$region_name}-2012",
                    'name' => "US Electricity - {$region_name} (2012)",
                    'description' => "2012 US Regional Electricity Emission Factors for {$region_name}",
                    'sector_id' => $sector->id,
                    'activity_id' => $activity->id,
                    'region_id' => $region->id,
                    'gas_id' => 1, // Assuming CO2 is gas_id 1, this is a multi-gas factor
                    'calculation_method' => 'eGRID subregion annual output emission rates',
                    'methodology_reference' => 'EPA eGRID 2012',
                    'factor_value' => $co2_rate, // Primary factor is CO2
                    'co2_factor' => $co2_rate,
                    'ch4_factor' => $ch4_rate,
                    'n2o_factor' => $n2o_rate,
                    'input_unit_id' => $unit->id,
                    'output_unit_id' => $unit->id,
                    'data_quality_rating' => 'High',
                    'vintage_year' => 2012,
                    'valid_from' => '2012-01-01',
                    'valid_until' => '2012-12-31',
                    'data_source' => 'EPA eGRID',
                    'data_source_detail' => 'EPA eGRID 2012 - Year 2012 US Regional Electricity Emission Factors for CO2, CH4 and N2O',
                    'metadata' => [
                        'egrid_subregion' => $region_name,
                        'table_source' => 'Table 8. Year 2012 US Regional Electricity Emission Factors for CO2, CH4 and N2O'
                    ],
                    'is_default' => false,
                    'is_active' => true,
                    'tier_level' => 2,
                    'priority' => 100
                ]);
                
                $imported_count++;
                $this->info("Imported: {$region_name}");
            }
            
            DB::commit();
            
            $this->info("\nSuccessfully imported {$imported_count} emission factors for 2012 US Regional Electricity.");
            $this->info("Total records created:");
            $this->info("- Emission factors: {$imported_count}");
            $this->info("- Emission factor values: " . ($imported_count * 3) . " (CO2, CH4, N2O for each region)");
            $this->info("- Metadata records: " . ($imported_count * 2));
            
        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Error importing data: " . $e->getMessage());
            return 1;
        }

        $this->info("\nImport completed successfully!");
        return 0;
    }
}
