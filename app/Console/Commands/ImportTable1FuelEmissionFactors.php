<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\MeasurementUnit;
use App\Models\GreenhouseGas;
use Illuminate\Support\Facades\DB;

class ImportTable1FuelEmissionFactors extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:table1-fuel-emission-factors';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Table 1: CO2 Emission Factors by Fuel';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of Table 1: CO2 Emission Factors by Fuel...');

        // Table 1 fuel data: [Fuel, Lower_Heating_Value, Primary_basis, Fuel_density, Liquid_basis, Gas_basis]
        $fuel_data = [
            // Oil products
            ['Crude oil', 42.3, 73.300, 3100.83, null, 2.780],
            ['Orimulsion', 27.5, 77.000, 2117.56, null, null],
            ['Natural Gas Liquids', 44.2, 64.200, 2835.73, null, null],
            ['Motor gasoline', 44.3, 69.300, 3089.93, 2.296, null],
            ['Aviation gasoline', 44.3, 70.000, 3101.00, 2.336, null],
            ['Jet kerosene', 43.8, 71.500, 3131.50, 2.520, null],
            ['Other kerosene', 43.8, 71.900, 3148.72, 2.575, null],
            ['Shale oil', 38.1, 73.300, 2792.73, null, null],
            ['Gas/Diesel oil', 43.0, 74.100, 3186.30, 2.610, null],
            ['Residual fuel oil', 40.4, 77.400, 3128.16, 3.156, null],
            ['Liquefied petroleum gases', 47.3, 63.100, 2983.30, 1.510, null],
            ['Ethane', 46.4, 61.600, 2859.24, null, null],
            ['Naphtha', 44.5, 73.300, 3261.85, 2.376, null],
            ['Bitumen', 40.2, 80.700, 3244.14, null, null],
            ['Lubricants', 40.2, 73.300, 2946.90, 3.640, null],
            ['Petroleum coke', 32.5, 97.500, 3169.75, null, null],
            ['Refinery feedstocks', 43.0, 73.300, 3151.90, null, null],
            ['Refinery gas', 49.5, 57.600, 2851.20, null, null],
            ['Paraffin waxes', 40.2, 73.300, 2946.66, null, null],
            ['White spirit/SBP', 40.2, 73.300, 2946.66, null, null],
            ['Other petroleum products', 40.2, 73.300, 2946.66, null, null],

            // Coal products
            ['Anthracite', 26.7, 98.300, 2624.61, null, null],
            ['Coking coal', 28.2, 94.600, 2667.72, null, null],
            ['Other bituminous coal', 25.8, 94.600, 2440.68, null, null],
            ['Sub-bituminous coal', 18.9, 96.100, 1816.29, null, null],
            ['Lignite', 11.9, 101.000, 1201.90, null, null],
            ['Oil shale and tar sands', 8.9, 107.000, 952.30, null, null],
            ['Brown coal briquettes', 20.7, 97.500, 2018.25, null, null],
            ['Patent fuel', 20.7, 97.500, 2018.25, null, null],
            ['Coke oven coke', 28.2, 107.000, 3017.40, null, null],
            ['Gas coke', 28.2, 107.000, 3017.40, null, null],
            ['Coal tar', 28.2, 107.000, 3017.40, null, null],
            ['Gas works gas', 38.7, 44.400, 1718.28, null, null],
            ['Coke oven gas', 38.7, 44.400, 1718.28, null, null],
            ['Blast furnace gas', 2.47, 260.000, 642.20, null, null],
            ['Other recovered gas', 7.06, 139.000, 981.34, null, 1.650],

            // Other wastes and biomass
            ['Municipal waste (Non biomass fraction)', null, 91.700, 917.00, null, null],
            ['Industrial waste', 31.0, 143.000, null, null, null],
            ['Waste oils', 40.2, 73.300, 2946.66, null, null],
            ['Wood/Wood waste', 15.6, 112.000, 1747.20, null, null],
            ['Sulphite lyes (black liquor)', 11.2, 95.300, 1066.36, null, null],
            ['Other primary solid biomass', 11.6, 100.000, 1160.00, null, null],
            ['Charcoal', 29.5, 112.000, 3304.00, null, null],
            ['Biogasoline', 27.0, 70.800, 1911.60, null, null],
            ['Biodiesels', 27.0, 70.800, 1911.60, null, null],
            ['Other liquid biofuels', 27.4, 79.600, 2181.04, null, null],
            ['Landfill gas', 50.4, 54.600, 2751.84, null, null],
            ['Sludge gas', 50.4, 54.600, 2751.84, null, null],
            ['Other biogas', 50.4, 54.600, 2751.84, null, null],
            ['Municipal waste (biomass fraction)', 11.6, 100.000, 1160.00, null, null]
        ];

        try {
            DB::beginTransaction();

            // Get or create fuel combustion sector
            $sector = GhgProtocolSector::firstOrCreate([
                'name' => 'fuel_combustion'
            ], [
                'display_name' => 'Fuel Combustion',
                'description' => 'Fuel Combustion Sources',
                'is_active' => true,
                'sort_order' => 1
            ]);

            // Get CO2 gas
            $co2_gas = GreenhouseGas::where('chemical_formula', 'CO2')->first();
            if (!$co2_gas) {
                $this->error('CO2 gas record not found. Please ensure greenhouse gases are seeded.');
                return 1;
            }

            // Get or create measurement units
            $units = [
                'kg_co2_gj' => MeasurementUnit::firstOrCreate([
                    'name' => 'kg CO2/GJ'
                ], [
                    'symbol' => 'kg CO2/GJ',
                    'description' => 'Kilograms CO2 per Gigajoule',
                    'unit_type' => 'emission_factor'
                ]),
                'kg_co2_tonne' => MeasurementUnit::firstOrCreate([
                    'name' => 'kg CO2/tonne'
                ], [
                    'symbol' => 'kg CO2/t',
                    'description' => 'Kilograms CO2 per tonne',
                    'unit_type' => 'emission_factor'
                ]),
                'kg_co2_litre' => MeasurementUnit::firstOrCreate([
                    'name' => 'kg CO2/litre'
                ], [
                    'symbol' => 'kg CO2/L',
                    'description' => 'Kilograms CO2 per litre',
                    'unit_type' => 'emission_factor'
                ]),
                'kg_co2_m3' => MeasurementUnit::firstOrCreate([
                    'name' => 'kg CO2/m³'
                ], [
                    'symbol' => 'kg CO2/m³',
                    'description' => 'Kilograms CO2 per cubic metre',
                    'unit_type' => 'emission_factor'
                ])
            ];

            // Get global region
            $global_region = GhgProtocolRegion::where('code', 'GLOBAL')->first();
            if (!$global_region) {
                $this->error('Global region not found.');
                return 1;
            }

            $imported_count = 0;

            foreach ($fuel_data as $row) {
                [$fuel_name, $lower_heating_value, $primary_basis, $fuel_density, $liquid_basis, $gas_basis] = $row;

                // Create activity for this fuel
                $activity_name = "Fuel Combustion - {$fuel_name}";
                $activity_code = 'FUEL_' . strtoupper(str_replace([' ', '/', '(', ')', '-', '.'], ['_', '_', '', '', '_', ''], $fuel_name));
                $activity_code = substr($activity_code, 0, 45);

                $activity = GhgProtocolActivity::firstOrCreate([
                    'name' => $activity_name,
                    'sector_id' => $sector->id
                ], [
                    'code' => $activity_code,
                    'description' => "Fuel combustion emission factors for {$fuel_name}",
                    'activity_type' => 'fuel_combustion',
                    'fuel_type' => $fuel_name,
                    'applicable_scopes' => [1],
                    'calculation_methods' => ['fuel_based'],
                    'is_active' => true,
                    'sort_order' => 0
                ]);

                // Create emission factors for different measurement bases

                // Primary basis (kg CO2/GJ)
                if ($primary_basis !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "FUEL_{$activity_code}_PRIMARY",
                        'name' => "Primary CO2 Factor - {$fuel_name}",
                        'description' => "Primary CO2 emission factor for {$fuel_name} (energy basis)",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $global_region->id,
                        'gas_id' => $co2_gas->id,
                        'calculation_method' => 'Energy content basis',
                        'methodology_reference' => 'GHG Protocol Fuel Combustion Tool',
                        'factor_value' => $primary_basis,
                        'co2_factor' => $primary_basis,
                        'input_unit_id' => $units['kg_co2_gj']->id,
                        'output_unit_id' => $units['kg_co2_gj']->id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 1. CO2 Emission Factors by Fuel',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'measurement_basis' => 'energy_content',
                            'lower_heating_value' => $lower_heating_value,
                            'table_source' => 'Table 1 - Fuel CO2 Factors',
                            'calculation_basis' => 'primary_energy'
                        ],
                        'is_default' => true,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 500
                    ]);
                    $imported_count++;
                }

                // Fuel density basis (kg CO2/tonne)
                if ($fuel_density !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "FUEL_{$activity_code}_MASS",
                        'name' => "Mass CO2 Factor - {$fuel_name}",
                        'description' => "Mass-based CO2 emission factor for {$fuel_name}",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $global_region->id,
                        'gas_id' => $co2_gas->id,
                        'calculation_method' => 'Mass basis',
                        'methodology_reference' => 'GHG Protocol Fuel Combustion Tool',
                        'factor_value' => $fuel_density,
                        'co2_factor' => $fuel_density,
                        'input_unit_id' => $units['kg_co2_tonne']->id,
                        'output_unit_id' => $units['kg_co2_tonne']->id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 1. CO2 Emission Factors by Fuel',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'measurement_basis' => 'mass',
                            'lower_heating_value' => $lower_heating_value,
                            'table_source' => 'Table 1 - Fuel CO2 Factors',
                            'calculation_basis' => 'fuel_mass'
                        ],
                        'is_default' => false,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 400
                    ]);
                    $imported_count++;
                }

                // Liquid basis (kg CO2/litre)
                if ($liquid_basis !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "FUEL_{$activity_code}_LIQUID",
                        'name' => "Liquid CO2 Factor - {$fuel_name}",
                        'description' => "Volume-based CO2 emission factor for {$fuel_name} (liquid)",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $global_region->id,
                        'gas_id' => $co2_gas->id,
                        'calculation_method' => 'Volume basis (liquid)',
                        'methodology_reference' => 'GHG Protocol Fuel Combustion Tool',
                        'factor_value' => $liquid_basis,
                        'co2_factor' => $liquid_basis,
                        'input_unit_id' => $units['kg_co2_litre']->id,
                        'output_unit_id' => $units['kg_co2_litre']->id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 1. CO2 Emission Factors by Fuel',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'measurement_basis' => 'volume_liquid',
                            'lower_heating_value' => $lower_heating_value,
                            'table_source' => 'Table 1 - Fuel CO2 Factors',
                            'calculation_basis' => 'fuel_volume_liquid'
                        ],
                        'is_default' => false,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 300
                    ]);
                    $imported_count++;
                }

                // Gas basis (kg CO2/m³)
                if ($gas_basis !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "FUEL_{$activity_code}_GAS",
                        'name' => "Gas CO2 Factor - {$fuel_name}",
                        'description' => "Volume-based CO2 emission factor for {$fuel_name} (gas)",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $global_region->id,
                        'gas_id' => $co2_gas->id,
                        'calculation_method' => 'Volume basis (gas)',
                        'methodology_reference' => 'GHG Protocol Fuel Combustion Tool',
                        'factor_value' => $gas_basis,
                        'co2_factor' => $gas_basis,
                        'input_unit_id' => $units['kg_co2_m3']->id,
                        'output_unit_id' => $units['kg_co2_m3']->id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 1. CO2 Emission Factors by Fuel',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'measurement_basis' => 'volume_gas',
                            'lower_heating_value' => $lower_heating_value,
                            'table_source' => 'Table 1 - Fuel CO2 Factors',
                            'calculation_basis' => 'fuel_volume_gas'
                        ],
                        'is_default' => false,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 300
                    ]);
                    $imported_count++;
                }

                $this->info("Imported: {$fuel_name}");
            }

            DB::commit();

            $this->info("\nSuccessfully imported {$imported_count} fuel CO2 emission factors from Table 1.");

        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Error importing data: " . $e->getMessage());
            return 1;
        }

        $this->info("\nImport completed successfully!");
        $this->info("Table 1 fuel emission factors cover:");
        $this->info("- Oil products: Crude oil, gasoline, diesel, jet fuel, kerosene, LPG, etc.");
        $this->info("- Coal products: Anthracite, coking coal, lignite, coke, etc.");
        $this->info("- Gas products: Natural gas, refinery gas, biogas, etc.");
        $this->info("- Biomass: Wood waste, biofuels, charcoal, etc.");
        $this->info("- Waste: Municipal waste, industrial waste, etc.");
        $this->info("- Multiple measurement bases: Energy (GJ), Mass (tonne), Volume (L/m³)");
        $this->info("- Comprehensive CO2 factors for Scope 1 fuel combustion calculations");
        return 0;
    }
}
