<?php

namespace App\Console\Commands;

use App\Models\Organization;
use App\Models\User;
use App\Models\Supplier;
use App\Models\Facility;
use App\Services\StakeholderEngagementService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AssignStakeholderActionCommand extends Command
{
    protected $signature = 'stakeholders:assign-action 
                            {organization : Organization ID to assign action for}
                            {--title= : Action title}
                            {--description= : Action description}
                            {--type=decarbonization : Action type (decarbonization, data_collection, reporting, compliance, engagement, training)}
                            {--category= : Action category}
                            {--assign-to-user= : User ID to assign action to}
                            {--assign-to-supplier= : Supplier ID to assign action to}
                            {--assign-to-facility= : Facility ID to assign action to}
                            {--priority=medium : Priority level (low, medium, high, critical)}
                            {--due-date= : Due date (YYYY-MM-DD format)}
                            {--estimated-reduction= : Estimated emissions reduction (tCO2e)}
                            {--estimated-cost= : Estimated cost}
                            {--estimated-savings= : Estimated savings}
                            {--approval-required : Require approval for completion}
                            {--approver= : Approver user ID}
                            {--template= : Use action template ID}
                            {--dry-run : Run without creating action}';

    protected $description = 'Assign decarbonization tasks with approval workflows';

    protected StakeholderEngagementService $stakeholderService;

    public function __construct(StakeholderEngagementService $stakeholderService)
    {
        parent::__construct();
        $this->stakeholderService = $stakeholderService;
    }

    public function handle(): int
    {
        $this->info('📋 Assigning stakeholder action...');

        $organizationId = $this->argument('organization');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No action will be created');
        }

        try {
            $organization = Organization::find($organizationId);
            
            if (!$organization) {
                $this->error("❌ Organization not found: {$organizationId}");
                return 1;
            }

            $this->line("  📍 Organization: {$organization->name}");

            // Gather action data
            $actionData = $this->gatherActionData($organization);

            if (!$actionData) {
                $this->error("❌ Failed to gather action data");
                return 1;
            }

            // Validate action data
            $validation = $this->validateActionData($actionData);
            if (!$validation['valid']) {
                $this->error("❌ Action data validation failed:");
                foreach ($validation['errors'] as $error) {
                    $this->line("  - {$error}");
                }
                return 1;
            }

            if ($dryRun) {
                $this->displayActionPlan($actionData);
                return 0;
            }

            $result = $this->stakeholderService->createStakeholderAction($actionData);

            if (!$result['success']) {
                $this->error("❌ Action assignment failed: {$result['error']}");
                return 1;
            }

            $this->displayActionResults($result);
            $this->info('✅ Stakeholder action assigned successfully');

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Action assignment failed: {$e->getMessage()}");
            Log::error('Stakeholder action assignment failed', [
                'organization_id' => $organizationId,
                'error' => $e->getMessage(),
            ]);
            return 1;
        }
    }

    protected function gatherActionData(Organization $organization): ?array
    {
        $actionData = [
            'organization_id' => $organization->id,
        ];

        // Get title
        $title = $this->option('title');
        if (!$title) {
            $title = $this->ask('Action title');
        }
        if (!$title) {
            $this->error("Action title is required");
            return null;
        }
        $actionData['title'] = $title;

        // Get description
        $description = $this->option('description');
        if (!$description) {
            $description = $this->ask('Action description');
        }
        if (!$description) {
            $this->error("Action description is required");
            return null;
        }
        $actionData['description'] = $description;

        // Get action type
        $actionType = $this->option('type');
        $validTypes = ['decarbonization', 'data_collection', 'reporting', 'compliance', 'engagement', 'training'];
        if (!in_array($actionType, $validTypes)) {
            $actionType = $this->choice('Action type', $validTypes, 0);
        }
        $actionData['action_type'] = $actionType;

        // Get category
        $category = $this->option('category');
        if (!$category) {
            $category = $this->getDefaultCategory($actionType);
        }
        $actionData['category'] = $category;

        // Get assignment
        $assignment = $this->getAssignment($organization);
        if (!$assignment) {
            return null;
        }
        $actionData = array_merge($actionData, $assignment);

        // Get priority
        $priority = $this->option('priority');
        $validPriorities = ['low', 'medium', 'high', 'critical'];
        if (!in_array($priority, $validPriorities)) {
            $priority = $this->choice('Priority level', $validPriorities, 1);
        }
        $actionData['priority'] = $priority;

        // Get due date
        $dueDate = $this->option('due-date');
        if (!$dueDate) {
            $dueDate = $this->ask('Due date (YYYY-MM-DD)', now()->addDays(30)->format('Y-m-d'));
        }
        try {
            $actionData['due_date'] = Carbon::createFromFormat('Y-m-d', $dueDate);
        } catch (\Exception $e) {
            $this->error("Invalid due date format. Use YYYY-MM-DD");
            return null;
        }

        // Get optional financial data
        if ($estimatedReduction = $this->option('estimated-reduction')) {
            $actionData['estimated_emissions_reduction'] = (float) $estimatedReduction;
        }

        if ($estimatedCost = $this->option('estimated-cost')) {
            $actionData['estimated_cost'] = (float) $estimatedCost;
        }

        if ($estimatedSavings = $this->option('estimated-savings')) {
            $actionData['estimated_savings'] = (float) $estimatedSavings;
        }

        // Get approval settings
        $approvalRequired = $this->option('approval-required');
        if ($approvalRequired) {
            $actionData['approval_required'] = true;
            
            $approverId = $this->option('approver');
            if (!$approverId) {
                $approverId = $this->ask('Approver user ID');
            }
            
            if ($approverId) {
                $approver = User::find($approverId);
                if (!$approver) {
                    $this->error("Approver user not found: {$approverId}");
                    return null;
                }
                $actionData['approver_user_id'] = $approverId;
            }
        }

        return $actionData;
    }

    protected function getAssignment(Organization $organization): ?array
    {
        $assignmentType = $this->choice(
            'Assign action to',
            ['User', 'Supplier', 'Facility'],
            0
        );

        switch ($assignmentType) {
            case 'User':
                $userId = $this->option('assign-to-user');
                if (!$userId) {
                    $userId = $this->ask('User ID to assign to');
                }
                
                if (!$userId) {
                    $this->error("User ID is required");
                    return null;
                }
                
                $user = User::find($userId);
                if (!$user) {
                    $this->error("User not found: {$userId}");
                    return null;
                }
                
                return ['assigned_to_user_id' => $userId];

            case 'Supplier':
                $supplierId = $this->option('assign-to-supplier');
                if (!$supplierId) {
                    $supplierId = $this->ask('Supplier ID to assign to');
                }
                
                if (!$supplierId) {
                    $this->error("Supplier ID is required");
                    return null;
                }
                
                $supplier = Supplier::where('organization_id', $organization->id)
                    ->where('id', $supplierId)
                    ->first();
                    
                if (!$supplier) {
                    $this->error("Supplier not found: {$supplierId}");
                    return null;
                }
                
                return ['assigned_to_supplier_id' => $supplierId];

            case 'Facility':
                $facilityId = $this->option('assign-to-facility');
                if (!$facilityId) {
                    $facilityId = $this->ask('Facility ID to assign to');
                }
                
                if (!$facilityId) {
                    $this->error("Facility ID is required");
                    return null;
                }
                
                $facility = Facility::where('organization_id', $organization->id)
                    ->where('id', $facilityId)
                    ->first();
                    
                if (!$facility) {
                    $this->error("Facility not found: {$facilityId}");
                    return null;
                }
                
                return ['assigned_to_facility_id' => $facilityId];

            default:
                return null;
        }
    }

    protected function getDefaultCategory(string $actionType): string
    {
        return match($actionType) {
            'decarbonization' => 'Energy Efficiency',
            'data_collection' => 'Emissions Data',
            'reporting' => 'Compliance Reporting',
            'compliance' => 'Regulatory Compliance',
            'engagement' => 'Stakeholder Engagement',
            'training' => 'Capacity Building',
            default => 'General',
        };
    }

    protected function validateActionData(array $actionData): array
    {
        $errors = [];

        // Required fields
        $requiredFields = ['title', 'description', 'action_type', 'category', 'priority', 'due_date'];
        foreach ($requiredFields as $field) {
            if (!isset($actionData[$field]) || empty($actionData[$field])) {
                $errors[] = "Missing required field: {$field}";
            }
        }

        // Assignment validation
        $hasAssignment = isset($actionData['assigned_to_user_id']) || 
                        isset($actionData['assigned_to_supplier_id']) || 
                        isset($actionData['assigned_to_facility_id']);
        
        if (!$hasAssignment) {
            $errors[] = "Action must be assigned to a user, supplier, or facility";
        }

        // Due date validation
        if (isset($actionData['due_date']) && $actionData['due_date'] < now()) {
            $errors[] = "Due date cannot be in the past";
        }

        // Approval validation
        if (isset($actionData['approval_required']) && $actionData['approval_required'] && 
            !isset($actionData['approver_user_id'])) {
            $errors[] = "Approver is required when approval is required";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    protected function displayActionPlan(array $actionData): void
    {
        $this->info('📋 Action Assignment Plan:');
        
        $this->line('  📝 Action Details:');
        $this->line("    Title: {$actionData['title']}");
        $this->line("    Description: {$actionData['description']}");
        $this->line("    Type: {$actionData['action_type']}");
        $this->line("    Category: {$actionData['category']}");
        $this->line("    Priority: {$actionData['priority']}");
        $this->line("    Due Date: {$actionData['due_date']->format('Y-m-d')}");
        
        $this->line('');
        $this->line('  👤 Assignment:');
        if (isset($actionData['assigned_to_user_id'])) {
            $user = User::find($actionData['assigned_to_user_id']);
            $this->line("    Assigned to User: {$user->name} (ID: {$user->id})");
        } elseif (isset($actionData['assigned_to_supplier_id'])) {
            $supplier = Supplier::find($actionData['assigned_to_supplier_id']);
            $this->line("    Assigned to Supplier: {$supplier->name} (ID: {$supplier->id})");
        } elseif (isset($actionData['assigned_to_facility_id'])) {
            $facility = Facility::find($actionData['assigned_to_facility_id']);
            $this->line("    Assigned to Facility: {$facility->name} (ID: {$facility->id})");
        }
        
        if (isset($actionData['estimated_emissions_reduction'])) {
            $this->line('');
            $this->line('  💰 Financial Impact:');
            $this->line("    Estimated Emissions Reduction: {$actionData['estimated_emissions_reduction']} tCO2e");
            
            if (isset($actionData['estimated_cost'])) {
                $this->line("    Estimated Cost: $" . number_format($actionData['estimated_cost'], 2));
            }
            
            if (isset($actionData['estimated_savings'])) {
                $this->line("    Estimated Savings: $" . number_format($actionData['estimated_savings'], 2));
            }
        }
        
        if (isset($actionData['approval_required']) && $actionData['approval_required']) {
            $this->line('');
            $this->line('  ✅ Approval Workflow:');
            $this->line("    Approval Required: Yes");
            
            if (isset($actionData['approver_user_id'])) {
                $approver = User::find($actionData['approver_user_id']);
                $this->line("    Approver: {$approver->name} (ID: {$approver->id})");
            }
        }
    }

    protected function displayActionResults(array $result): void
    {
        $action = $result['action'];
        
        $this->info('🎉 Action Assigned Successfully!');
        
        $this->line('');
        $this->line('  📋 Action Information:');
        $this->line("    ID: {$action->id}");
        $this->line("    Title: {$action->title}");
        $this->line("    Type: {$action->action_type_display_name}");
        $this->line("    Priority: {$action->priority}");
        $this->line("    Status: {$action->status}");
        $this->line("    Due Date: {$action->due_date->format('Y-m-d')}");
        $this->line("    Days Until Due: {$action->getDaysUntilDue()}");
        
        $this->line('');
        $this->line('  👤 Assignment Details:');
        $this->line("    Assigned To: {$action->assignee_name}");
        $this->line("    Assignee Type: {$action->assignee_type}");
        
        if ($action->approval_required) {
            $this->line('');
            $this->line('  ✅ Approval Workflow:');
            $this->line("    Approval Required: Yes");
            $this->line("    Approver: {$action->approverUser->name}");
        }
        
        if ($action->estimated_emissions_reduction || $action->estimated_cost || $action->estimated_savings) {
            $this->line('');
            $this->line('  💰 Estimated Impact:');
            
            if ($action->estimated_emissions_reduction) {
                $this->line("    Emissions Reduction: {$action->estimated_emissions_reduction} tCO2e");
            }
            
            if ($action->estimated_cost) {
                $this->line("    Cost: $" . number_format($action->estimated_cost, 2));
            }
            
            if ($action->estimated_savings) {
                $this->line("    Savings: $" . number_format($action->estimated_savings, 2));
            }
            
            if ($action->calculateROI()) {
                $this->line("    ROI: " . number_format($action->calculateROI(), 1) . "%");
            }
        }
        
        $this->line('');
        $this->info('📧 Next Steps:');
        $this->line('  1. Assignment notification sent to assignee');
        $this->line('  2. Action appears in assignee dashboard');
        $this->line('  3. Progress tracking is now available');
        $this->line('  4. Reminder notifications will be sent as due date approaches');
        
        $this->line('');
        $this->line('🔧 Management Commands:');
        $this->line("  - Update progress: php artisan stakeholders:update-action-progress {$action->id}");
        $this->line("  - View action: php artisan stakeholders:view-action {$action->id}");
        $this->line("  - Complete action: php artisan stakeholders:complete-action {$action->id}");
    }
}
