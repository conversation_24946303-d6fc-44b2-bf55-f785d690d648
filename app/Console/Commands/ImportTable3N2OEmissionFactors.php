<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolRegion;
use App\Models\MeasurementUnit;
use App\Models\GreenhouseGas;
use Illuminate\Support\Facades\DB;

class ImportTable3N2OEmissionFactors extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:table3-n2o-emission-factors';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Table 3: N2O Emission Factors by Fuel';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of Table 3: N2O Emission Factors by Fuel...');
        
        // Table 3 N2O fuel data: [Fuel, Lower_Heating_Value, Primary_basis, Fuel_density, Liquid_basis, Gas_basis]
        $fuel_data = [
            // Oil products
            ['Crude oil', 42.3, 0.6, 0.025, null, 0.000019],
            ['Orimulsion', 27.5, 0.6, 0.017, null, null],
            ['Natural Gas Liquids', 44.2, 0.6, 0.027, null, null],
            ['Motor gasoline', 44.3, 0.6, 0.027, 0.020, 0.000020],
            ['Aviation gasoline', 44.3, 0.6, 0.027, 0.020, 0.000020],
            ['Jet gasoline', 43.8, 0.6, 0.026, 0.020, 0.000020],
            ['Jet kerosene', 43.8, 0.6, 0.026, 0.020, 0.000020],
            ['Other kerosene', 43.8, 0.6, 0.026, 0.020, 0.000020],
            ['Shale oil', 38.1, 0.6, 0.023, null, null],
            ['Gas/Diesel oil', 43.0, 0.6, 0.026, 0.020, 0.000020],
            ['Residual fuel oil', 40.4, 0.6, 0.024, 0.020, 0.000020],
            ['Liquefied Petroleum Gases', 47.3, 0.1, 0.005, 0.004, 0.000004],
            ['Ethane', 46.4, 0.1, 0.005, null, null],
            ['Naphtha', 44.5, 0.6, 0.027, 0.020, 0.000020],
            ['Bitumen', 40.2, 0.6, 0.024, null, null],
            ['Lubricants', 40.2, 0.6, 0.024, 0.018, 0.000018],
            ['Petroleum coke', 32.5, 0.6, 0.020, null, null],
            ['Refinery feedstocks', 43, 0.6, 0.026, null, null],
            ['Refinery gas', 49.5, 0.1, 0.005, null, null],
            ['Paraffin waxes', 40.2, 0.6, 0.024, null, null],
            ['White spirit/SBP', 40.2, 0.6, 0.024, null, null],
            ['Other petroleum products', 40.2, 0.6, 0.024, null, null],
            
            // Coal products
            ['Anthracite', 26.7, 1.5, 0.040, null, null],
            ['Coking coal', 28.2, 1.5, 0.042, null, null],
            ['Other bituminous coal', 25.8, 1.5, 0.039, null, null],
            ['Sub-bituminous coal', 18.9, 1.5, 0.028, null, null],
            ['Lignite', 11.9, 1.5, 0.018, null, null],
            ['Oil shale and tar sands', 8.9, 1.5, 0.013, null, null],
            ['Brown coal briquettes', 20.7, 1.5, 0.031, null, null],
            ['Patent fuel', 20.7, 1.5, 0.031, null, null],
            ['Coke oven coke', 28.2, 1.5, 0.042, null, null],
            ['Lignite coke', 28.2, 1.5, 0.042, null, null],
            ['Gas coke', 28.2, 1.5, 0.042, null, null],
            ['Coal tar', 28.2, 1.5, 0.042, null, null],
            ['Gas works gas', 38.7, 0.1, 0.004, null, null],
            ['Coke oven gas', 38.7, 0.1, 0.004, null, null],
            ['Blast furnace gas', 2.47, 0.1, 0.0002, null, null],
            ['Oxygen steel furnace gas', 7.06, 0.1, 0.0007, null, null],
            
            // Natural gas
            ['Natural gas', 48, 0.1, 0.005, null, 0.0000037],
            ['Municipal waste (Non biomass fraction)', null, 4, null, null, null],
            
            // Other wastes
            ['Industrial waste', null, 4, null, null, null],
            ['Waste oils', 40.2, 4, 0.161, null, null],
            ['Wood/Wood waste', 15.6, 4, 0.062, null, null],
            ['Sulphite lyes (black liquor)', 11.2, 4, 0.045, null, null],
            ['Other primary solid biomass fuels', 11.6, 4, 0.046, null, null],
            ['Charcoal', 29.5, 4, 0.118, null, null],
            ['Biogasoline', 27, 0.6, 0.016, null, null],
            ['Biodiesels', 27, 0.6, 0.016, null, null],
            ['Other liquid biofuels', 27.4, 0.6, 0.016, null, null],
            ['Landfill gas', 50.4, 0.1, 0.005, null, null],
            ['Sludge gas', 50.4, 0.1, 0.005, null, null],
            ['Other biogas', 50.4, 0.1, 0.005, null, null],
            ['Municipal waste (Biomass fraction)', 11.6, 4, 0.046, null, null],
            ['Peat', null, 4, null, null, null]
        ];

        try {
            DB::beginTransaction();
            
            // Get fuel combustion sector
            $sector = GhgProtocolSector::where('name', 'fuel_combustion')->first();
            if (!$sector) {
                $this->error('Fuel combustion sector not found. Please run Table 1 import first.');
                return 1;
            }
            
            // Get N2O gas
            $n2o_gas = GreenhouseGas::where('chemical_formula', 'N2O')->first();
            if (!$n2o_gas) {
                $this->error('N2O gas record not found. Please ensure greenhouse gases are seeded.');
                return 1;
            }
            
            // Get or create measurement units
            $units = [
                'kg_n2o_gj' => MeasurementUnit::firstOrCreate([
                    'name' => 'kg N2O/GJ'
                ], [
                    'symbol' => 'kg N2O/GJ',
                    'description' => 'Kilograms N2O per Gigajoule',
                    'unit_type' => 'emission_factor'
                ]),
                'kg_n2o_tonne' => MeasurementUnit::firstOrCreate([
                    'name' => 'kg N2O/tonne'
                ], [
                    'symbol' => 'kg N2O/t',
                    'description' => 'Kilograms N2O per tonne',
                    'unit_type' => 'emission_factor'
                ]),
                'kg_n2o_litre' => MeasurementUnit::firstOrCreate([
                    'name' => 'kg N2O/litre'
                ], [
                    'symbol' => 'kg N2O/L',
                    'description' => 'Kilograms N2O per litre',
                    'unit_type' => 'emission_factor'
                ]),
                'kg_n2o_m3' => MeasurementUnit::firstOrCreate([
                    'name' => 'kg N2O/m³'
                ], [
                    'symbol' => 'kg N2O/m³',
                    'description' => 'Kilograms N2O per cubic metre',
                    'unit_type' => 'emission_factor'
                ])
            ];
            
            // Get global region
            $global_region = GhgProtocolRegion::where('code', 'GLOBAL')->first();
            if (!$global_region) {
                $this->error('Global region not found.');
                return 1;
            }
            
            $imported_count = 0;
            
            foreach ($fuel_data as $row) {
                [$fuel_name, $lower_heating_value, $primary_basis, $fuel_density, $liquid_basis, $gas_basis] = $row;
                
                // Find existing activity for this fuel
                $activity_name = "Fuel Combustion - {$fuel_name}";
                $activity = GhgProtocolActivity::where('name', $activity_name)
                    ->where('sector_id', $sector->id)
                    ->first();
                
                if (!$activity) {
                    $this->warn("Activity not found for fuel: {$fuel_name}. Skipping...");
                    continue;
                }
                
                // Create N2O emission factors for different measurement bases
                
                // Primary basis (kg N2O/GJ)
                if ($primary_basis !== null) {
                    $factor_value = $primary_basis / 1000; // Convert from g/GJ to kg/GJ
                    
                    GhgProtocolEmissionFactor::create([
                        'code' => "FUEL_N2O_{$activity->code}_PRIMARY",
                        'name' => "Primary N2O Factor - {$fuel_name}",
                        'description' => "Primary N2O emission factor for {$fuel_name} (energy basis)",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $global_region->id,
                        'gas_id' => $n2o_gas->id,
                        'calculation_method' => 'Energy content basis',
                        'methodology_reference' => 'GHG Protocol Fuel Combustion Tool',
                        'factor_value' => $factor_value,
                        'n2o_factor' => $factor_value,
                        'input_unit_id' => $units['kg_n2o_gj']->id,
                        'output_unit_id' => $units['kg_n2o_gj']->id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 3. N2O Emission Factors by Fuel',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'measurement_basis' => 'energy_content',
                            'lower_heating_value' => $lower_heating_value,
                            'table_source' => 'Table 3 - Fuel N2O Factors',
                            'calculation_basis' => 'primary_energy',
                            'original_value_g_gj' => $primary_basis
                        ],
                        'is_default' => true,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 500
                    ]);
                    $imported_count++;
                }
                
                // Fuel density basis (kg N2O/tonne)
                if ($fuel_density !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "FUEL_N2O_{$activity->code}_MASS",
                        'name' => "Mass N2O Factor - {$fuel_name}",
                        'description' => "Mass-based N2O emission factor for {$fuel_name}",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $global_region->id,
                        'gas_id' => $n2o_gas->id,
                        'calculation_method' => 'Mass basis',
                        'methodology_reference' => 'GHG Protocol Fuel Combustion Tool',
                        'factor_value' => $fuel_density,
                        'n2o_factor' => $fuel_density,
                        'input_unit_id' => $units['kg_n2o_tonne']->id,
                        'output_unit_id' => $units['kg_n2o_tonne']->id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 3. N2O Emission Factors by Fuel',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'measurement_basis' => 'mass',
                            'lower_heating_value' => $lower_heating_value,
                            'table_source' => 'Table 3 - Fuel N2O Factors',
                            'calculation_basis' => 'fuel_mass'
                        ],
                        'is_default' => false,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 400
                    ]);
                    $imported_count++;
                }
                
                // Liquid basis (kg N2O/litre)
                if ($liquid_basis !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "FUEL_N2O_{$activity->code}_LIQUID",
                        'name' => "Liquid N2O Factor - {$fuel_name}",
                        'description' => "Volume-based N2O emission factor for {$fuel_name} (liquid)",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $global_region->id,
                        'gas_id' => $n2o_gas->id,
                        'calculation_method' => 'Volume basis (liquid)',
                        'methodology_reference' => 'GHG Protocol Fuel Combustion Tool',
                        'factor_value' => $liquid_basis / 1000, // Convert from g/L to kg/L
                        'n2o_factor' => $liquid_basis / 1000,
                        'input_unit_id' => $units['kg_n2o_litre']->id,
                        'output_unit_id' => $units['kg_n2o_litre']->id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 3. N2O Emission Factors by Fuel',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'measurement_basis' => 'volume_liquid',
                            'lower_heating_value' => $lower_heating_value,
                            'table_source' => 'Table 3 - Fuel N2O Factors',
                            'calculation_basis' => 'fuel_volume_liquid',
                            'original_value_g_l' => $liquid_basis
                        ],
                        'is_default' => false,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 300
                    ]);
                    $imported_count++;
                }
                
                // Gas basis (kg N2O/m³)
                if ($gas_basis !== null) {
                    GhgProtocolEmissionFactor::create([
                        'code' => "FUEL_N2O_{$activity->code}_GAS",
                        'name' => "Gas N2O Factor - {$fuel_name}",
                        'description' => "Volume-based N2O emission factor for {$fuel_name} (gas)",
                        'sector_id' => $sector->id,
                        'activity_id' => $activity->id,
                        'region_id' => $global_region->id,
                        'gas_id' => $n2o_gas->id,
                        'calculation_method' => 'Volume basis (gas)',
                        'methodology_reference' => 'GHG Protocol Fuel Combustion Tool',
                        'factor_value' => $gas_basis,
                        'n2o_factor' => $gas_basis,
                        'input_unit_id' => $units['kg_n2o_m3']->id,
                        'output_unit_id' => $units['kg_n2o_m3']->id,
                        'data_quality_rating' => 'High',
                        'data_source' => 'GHG Protocol',
                        'data_source_detail' => 'Table 3. N2O Emission Factors by Fuel',
                        'metadata' => [
                            'fuel_type' => $fuel_name,
                            'measurement_basis' => 'volume_gas',
                            'lower_heating_value' => $lower_heating_value,
                            'table_source' => 'Table 3 - Fuel N2O Factors',
                            'calculation_basis' => 'fuel_volume_gas'
                        ],
                        'is_default' => false,
                        'is_active' => true,
                        'tier_level' => 2,
                        'priority' => 300
                    ]);
                    $imported_count++;
                }
                
                $this->info("Imported N2O factors for: {$fuel_name}");
            }
            
            DB::commit();
            
            $this->info("\nSuccessfully imported {$imported_count} fuel N2O emission factors from Table 3.");
            
        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Error importing data: " . $e->getMessage());
            return 1;
        }

        $this->info("\nImport completed successfully!");
        $this->info("Table 3 N2O emission factors complete the fuel combustion trilogy:");
        $this->info("- Nitrous oxide emissions for all major fuel types");
        $this->info("- Multiple measurement bases: Energy (GJ), Mass (tonne), Volume (L/m³)");
        $this->info("- Higher factors for coal and biomass fuels");
        $this->info("- Essential for complete GHG calculations including N2O impacts");
        $this->info("\n🎉 COMPLETE: All three GHG fuel combustion tables imported!");
        $this->info("Your database now contains comprehensive CO2, CH4, and N2O factors");
        return 0;
    }
}
