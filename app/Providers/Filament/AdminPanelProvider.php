<?php

namespace App\Providers\Filament;

use App\Filament\Pages\EmissionsDashboard;
use App\Filament\Pages\UserDashboard;
use App\Filament\Pages\AddDataPage;
use App\Filament\Pages\MyProgressPage;
use App\Filament\Pages\MyTasksPage;
use App\Filament\Pages\SimpleReportsPage;
use App\Http\Middleware\AutoLoginMiddleware;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;

use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->topNavigation(fn () => []) // 🧹 removes GitHub and Docs links
            ->default()
            ->id('admin')
            ->path('admin')
            // Removed login requirement
            //->login()
            //->registration()
            //->passwordReset()
            ->colors([
                'primary' => Color::Amber,
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                UserDashboard::class,
                AddDataPage::class,
                MyProgressPage::class,
                MyTasksPage::class,
                SimpleReportsPage::class,
                \App\Filament\Pages\UserGuide::class,
                // Admin pages (lower priority)
                EmissionsDashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                //Widgets\FilamentInfoWidget::class,
                \App\Filament\Widgets\EmissionsOverviewWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                AutoLoginMiddleware::class,
            ]);
    }


}
