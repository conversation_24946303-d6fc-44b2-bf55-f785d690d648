<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupplierSpend extends Model
{
    use HasFactory;

    protected $fillable = [
        'supplier_id',
        'scope3_category_id',
        'period_start',
        'period_end',
        'spend_amount',
        'currency',
        'spend_category',
        'description',
    ];

    protected $casts = [
        'period_start' => 'date',
        'period_end' => 'date',
        'spend_amount' => 'decimal:2',
    ];

    /**
     * Get the supplier
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the Scope 3 category
     */
    public function scope3Category(): BelongsTo
    {
        return $this->belongsTo(Scope3Category::class);
    }
}
