<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StakeholderDashboard extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'supplier_id',
        'role_name',
        'dashboard_name',
        'description',
        'dashboard_config',
        'widget_preferences',
        'filter_preferences',
        'is_default',
        'is_active',
        'last_accessed_at',
        'access_count',
    ];

    protected $casts = [
        'dashboard_config' => 'array',
        'widget_preferences' => 'array',
        'filter_preferences' => 'array',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
        'last_accessed_at' => 'datetime',
        'access_count' => 'integer',
    ];

    /**
     * Get the user
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the supplier
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }
}
