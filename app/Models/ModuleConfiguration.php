<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ModuleConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'module_name',
        'configuration',
        'schema',
        'is_valid',
        'validation_errors',
    ];

    protected $casts = [
        'configuration' => 'array',
        'schema' => 'array',
        'is_valid' => 'boolean',
        'validation_errors' => 'array',
    ];

    /**
     * Get the system module
     */
    public function systemModule(): BelongsTo
    {
        return $this->belongsTo(SystemModule::class, 'module_name', 'module_name');
    }

    /**
     * Get configuration value
     */
    public function get(string $key, $default = null)
    {
        return data_get($this->configuration, $key, $default);
    }

    /**
     * Set configuration value
     */
    public function set(string $key, $value): void
    {
        $config = $this->configuration ?? [];
        data_set($config, $key, $value);
        $this->configuration = $config;
    }

    /**
     * Check if configuration has key
     */
    public function has(string $key): bool
    {
        return data_get($this->configuration, $key) !== null;
    }

    /**
     * Merge configuration
     */
    public function merge(array $config): void
    {
        $this->configuration = array_merge($this->configuration ?? [], $config);
    }
}
