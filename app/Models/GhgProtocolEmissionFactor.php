<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class GhgProtocolEmissionFactor extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'description',
        'sector_id',
        'activity_id',
        'region_id',
        'gas_id',
        'calculation_method',
        'methodology_reference',
        'factor_value',
        'co2_factor',
        'ch4_factor',
        'n2o_factor',
        'carbon_content',
        'oxidation_factor',
        'heating_value',
        'heating_value_type',
        'input_unit_id',
        'output_unit_id',
        'data_quality_rating',
        'uncertainty_percentage',
        'uncertainty_lower',
        'uncertainty_upper',
        'uncertainty_type',
        'vintage_year',
        'valid_from',
        'valid_until',
        'data_source',
        'data_source_detail',
        'reference_link',
        'notes',
        'metadata',
        'is_default',
        'is_active',
        'tier_level',
        'priority',
    ];

    protected $casts = [
        'factor_value' => 'decimal:10',
        'co2_factor' => 'decimal:10',
        'ch4_factor' => 'decimal:10',
        'n2o_factor' => 'decimal:10',
        'carbon_content' => 'decimal:8',
        'oxidation_factor' => 'decimal:6',
        'heating_value' => 'decimal:6',
        'uncertainty_percentage' => 'decimal:4',
        'uncertainty_lower' => 'decimal:4',
        'uncertainty_upper' => 'decimal:4',
        'valid_from' => 'date',
        'valid_until' => 'date',
        'metadata' => 'array',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
        'vintage_year' => 'integer',
        'tier_level' => 'integer',
        'priority' => 'integer',
    ];

    /**
     * Get the sector this factor belongs to
     */
    public function sector(): BelongsTo
    {
        return $this->belongsTo(GhgProtocolSector::class, 'sector_id');
    }

    /**
     * Get the activity this factor is for
     */
    public function activity(): BelongsTo
    {
        return $this->belongsTo(GhgProtocolActivity::class, 'activity_id');
    }

    /**
     * Get the region this factor applies to
     */
    public function region(): BelongsTo
    {
        return $this->belongsTo(GhgProtocolRegion::class, 'region_id');
    }

    /**
     * Get the greenhouse gas this factor is for
     */
    public function gas(): BelongsTo
    {
        return $this->belongsTo(GreenhouseGas::class, 'gas_id');
    }

    /**
     * Get the input unit
     */
    public function inputUnit(): BelongsTo
    {
        return $this->belongsTo(MeasurementUnit::class, 'input_unit_id');
    }

    /**
     * Get the output unit
     */
    public function outputUnit(): BelongsTo
    {
        return $this->belongsTo(MeasurementUnit::class, 'output_unit_id');
    }

    /**
     * Get all variants of this emission factor
     */
    public function variants(): HasMany
    {
        return $this->hasMany(EmissionFactorVariant::class, 'base_factor_id');
    }

    /**
     * Get all unit relationships for this factor
     */
    public function units(): HasMany
    {
        return $this->hasMany(EmissionFactorUnit::class, 'ghg_protocol_factor_id');
    }

    /**
     * Scope to only return active factors
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by sector
     */
    public function scopeBySector($query, $sectorId)
    {
        return $query->where('sector_id', $sectorId);
    }

    /**
     * Scope to filter by activity
     */
    public function scopeByActivity($query, $activityId)
    {
        return $query->where('activity_id', $activityId);
    }

    /**
     * Scope to filter by region
     */
    public function scopeByRegion($query, $regionId)
    {
        return $query->where('region_id', $regionId);
    }

    /**
     * Scope to filter by gas
     */
    public function scopeByGas($query, $gasId)
    {
        return $query->where('gas_id', $gasId);
    }

    /**
     * Scope to filter by calculation method
     */
    public function scopeByMethod($query, $method)
    {
        return $query->where('calculation_method', $method);
    }

    /**
     * Scope to get default factors
     */
    public function scopeDefaults($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope to order by priority
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc')->orderBy('tier_level', 'desc');
    }

    /**
     * Check if this factor is valid for a given date
     */
    public function isValidForDate($date): bool
    {
        $checkDate = is_string($date) ? \Carbon\Carbon::parse($date) : $date;
        
        if ($this->valid_from && $checkDate->lt($this->valid_from)) {
            return false;
        }
        
        if ($this->valid_until && $checkDate->gt($this->valid_until)) {
            return false;
        }
        
        return true;
    }

    /**
     * Get the best variant for given conditions
     */
    public function getBestVariant($conditions = [])
    {
        $query = $this->variants()->active();
        
        // Apply conditions
        if (isset($conditions['region_id'])) {
            $query->where('region_id', $conditions['region_id']);
        }
        
        if (isset($conditions['vintage_year'])) {
            $query->where('vintage_year', $conditions['vintage_year']);
        }
        
        if (isset($conditions['variant_type'])) {
            $query->where('variant_type', $conditions['variant_type']);
        }
        
        return $query->orderBy('priority', 'desc')->first();
    }

    /**
     * Calculate emissions for given activity data
     */
    public function calculateEmissions($quantity, $variant = null): ?float
    {
        $factor = $variant ? $variant->factor_value ?? $this->factor_value : $this->factor_value;
        
        if (!$factor || !$quantity) {
            return null;
        }
        
        return $quantity * $factor;
    }
}
