<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class GhgProtocolActivity extends Model
{
    use HasFactory;

    protected $fillable = [
        'sector_id',
        'code',
        'name',
        'display_name',
        'description',
        'activity_type',
        'fuel_type',
        'vehicle_type',
        'equipment_type',
        'applicable_scopes',
        'calculation_methods',
        'sort_order',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'applicable_scopes' => 'array',
        'calculation_methods' => 'array',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the sector this activity belongs to
     */
    public function sector(): BelongsTo
    {
        return $this->belongsTo(GhgProtocolSector::class, 'sector_id');
    }

    /**
     * Get all emission factors for this activity
     */
    public function emissionFactors(): HasMany
    {
        return $this->hasMany(GhgProtocolEmissionFactor::class, 'activity_id');
    }

    /**
     * Scope to only return active activities
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order and name
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Scope to filter by activity type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('activity_type', $type);
    }

    /**
     * Scope to filter by fuel type
     */
    public function scopeByFuelType($query, $fuelType)
    {
        return $query->where('fuel_type', $fuelType);
    }

    /**
     * Scope to filter by vehicle type
     */
    public function scopeByVehicleType($query, $vehicleType)
    {
        return $query->where('vehicle_type', $vehicleType);
    }

    /**
     * Check if this activity applies to a specific scope
     */
    public function appliesToScope($scope): bool
    {
        if (!$this->applicable_scopes) {
            return false;
        }
        
        return in_array($scope, $this->applicable_scopes);
    }

    /**
     * Get available calculation methods for this activity
     */
    public function getCalculationMethods(): array
    {
        return $this->calculation_methods ?? [];
    }

    /**
     * Get display name or fall back to name
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->attributes['display_name'] ?? $this->attributes['name'];
    }
}
