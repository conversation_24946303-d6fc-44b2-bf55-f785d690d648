<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ScenarioAnalysis extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'analysis_name',
        'description',
        'scenario_type',
        'scenario_parameters',
        'baseline_emissions',
        'scenario_results',
        'comparison_analysis',
        'recommendations',
        'analysis_date',
        'status',
    ];

    protected $casts = [
        'scenario_parameters' => 'array',
        'baseline_emissions' => 'decimal:2',
        'scenario_results' => 'array',
        'comparison_analysis' => 'array',
        'recommendations' => 'array',
        'analysis_date' => 'date',
    ];

    /**
     * Get the organization
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get scenario projections
     */
    public function projections(): HasMany
    {
        return $this->hasMany(ScenarioProjection::class);
    }
}
