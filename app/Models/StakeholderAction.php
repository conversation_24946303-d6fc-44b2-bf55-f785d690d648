<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class StakeholderAction extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'title',
        'description',
        'action_type',
        'category',
        'assigned_to_user_id',
        'assigned_to_supplier_id',
        'assigned_to_facility_id',
        'priority',
        'due_date',
        'estimated_emissions_reduction',
        'estimated_cost',
        'estimated_savings',
        'success_criteria',
        'approval_required',
        'approver_user_id',
        'status',
        'progress_percentage',
        'started_at',
        'completed_at',
        'created_by_user_id',
        'metadata',
    ];

    protected $casts = [
        'due_date' => 'date',
        'estimated_emissions_reduction' => 'decimal:2',
        'estimated_cost' => 'decimal:2',
        'estimated_savings' => 'decimal:2',
        'success_criteria' => 'array',
        'progress_percentage' => 'decimal:2',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'metadata' => 'array',
        'approval_required' => 'boolean',
    ];

    /**
     * Get the organization
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the assigned user
     */
    public function assignedToUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to_user_id');
    }

    /**
     * Get the assigned supplier
     */
    public function assignedToSupplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class, 'assigned_to_supplier_id');
    }

    /**
     * Get the assigned facility
     */
    public function assignedToFacility(): BelongsTo
    {
        return $this->belongsTo(Facility::class, 'assigned_to_facility_id');
    }

    /**
     * Get the approver user
     */
    public function approverUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approver_user_id');
    }

    /**
     * Get the creator user
     */
    public function createdByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }

    /**
     * Get progress updates
     */
    public function progressUpdates(): HasMany
    {
        return $this->hasMany(ActionProgressUpdate::class);
    }

    /**
     * Scope for specific status
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for specific priority
     */
    public function scopeWithPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope for overdue actions
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    /**
     * Scope for actions due soon
     */
    public function scopeDueSoon($query, int $days = 7)
    {
        return $query->whereBetween('due_date', [now(), now()->addDays($days)])
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    /**
     * Scope for assigned to user
     */
    public function scopeAssignedToUser($query, int $userId)
    {
        return $query->where('assigned_to_user_id', $userId);
    }

    /**
     * Scope for assigned to supplier
     */
    public function scopeAssignedToSupplier($query, int $supplierId)
    {
        return $query->where('assigned_to_supplier_id', $supplierId);
    }

    /**
     * Check if action is overdue
     */
    public function isOverdue(): bool
    {
        return $this->due_date < now() && !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * Check if action is due soon
     */
    public function isDueSoon(int $days = 7): bool
    {
        return $this->due_date <= now()->addDays($days) && 
               $this->due_date >= now() && 
               !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * Get days until due
     */
    public function getDaysUntilDue(): int
    {
        return now()->diffInDays($this->due_date, false);
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'assigned' => 'gray',
            'in_progress' => 'blue',
            'pending_approval' => 'yellow',
            'approved' => 'green',
            'completed' => 'green',
            'cancelled' => 'red',
            'overdue' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get priority color for UI
     */
    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            'low' => 'gray',
            'medium' => 'blue',
            'high' => 'yellow',
            'critical' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get assignee name
     */
    public function getAssigneeNameAttribute(): string
    {
        if ($this->assigned_to_user_id) {
            return $this->assignedToUser->name ?? 'Unknown User';
        }
        
        if ($this->assigned_to_supplier_id) {
            return $this->assignedToSupplier->name ?? 'Unknown Supplier';
        }
        
        if ($this->assigned_to_facility_id) {
            return $this->assignedToFacility->name ?? 'Unknown Facility';
        }
        
        return 'Unassigned';
    }

    /**
     * Get assignee type
     */
    public function getAssigneeTypeAttribute(): string
    {
        if ($this->assigned_to_user_id) return 'user';
        if ($this->assigned_to_supplier_id) return 'supplier';
        if ($this->assigned_to_facility_id) return 'facility';
        return 'none';
    }

    /**
     * Calculate ROI if cost and savings are available
     */
    public function calculateROI(): ?float
    {
        if (!$this->estimated_cost || !$this->estimated_savings || $this->estimated_cost <= 0) {
            return null;
        }
        
        return (($this->estimated_savings - $this->estimated_cost) / $this->estimated_cost) * 100;
    }

    /**
     * Get latest progress update
     */
    public function getLatestProgressUpdate(): ?ActionProgressUpdate
    {
        return $this->progressUpdates()->latest('update_date')->first();
    }

    /**
     * Get progress trend (improving, declining, stable)
     */
    public function getProgressTrend(): string
    {
        $recentUpdates = $this->progressUpdates()
            ->orderBy('update_date', 'desc')
            ->limit(3)
            ->get();
            
        if ($recentUpdates->count() < 2) {
            return 'stable';
        }
        
        $latest = $recentUpdates->first()->progress_percentage;
        $previous = $recentUpdates->skip(1)->first()->progress_percentage;
        
        if ($latest > $previous + 5) return 'improving';
        if ($latest < $previous - 5) return 'declining';
        return 'stable';
    }

    /**
     * Mark as started
     */
    public function markAsStarted(): bool
    {
        if ($this->status !== 'assigned') {
            return false;
        }
        
        $this->update([
            'status' => 'in_progress',
            'started_at' => now(),
        ]);
        
        return true;
    }

    /**
     * Mark as completed
     */
    public function markAsCompleted(): bool
    {
        if (in_array($this->status, ['completed', 'cancelled'])) {
            return false;
        }
        
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'progress_percentage' => 100,
        ]);
        
        return true;
    }

    /**
     * Request approval
     */
    public function requestApproval(): bool
    {
        if (!$this->approval_required || !$this->approver_user_id) {
            return false;
        }
        
        if ($this->status !== 'in_progress') {
            return false;
        }
        
        $this->update(['status' => 'pending_approval']);
        
        return true;
    }

    /**
     * Approve action
     */
    public function approve(): bool
    {
        if ($this->status !== 'pending_approval') {
            return false;
        }
        
        $this->update(['status' => 'approved']);
        
        return true;
    }

    /**
     * Get completion percentage based on success criteria
     */
    public function getCompletionPercentage(): float
    {
        if (!$this->success_criteria || empty($this->success_criteria)) {
            return $this->progress_percentage;
        }
        
        $completedCriteria = 0;
        $totalCriteria = count($this->success_criteria);
        
        foreach ($this->success_criteria as $criteria) {
            if (isset($criteria['completed']) && $criteria['completed']) {
                $completedCriteria++;
            }
        }
        
        return $totalCriteria > 0 ? ($completedCriteria / $totalCriteria) * 100 : 0;
    }

    /**
     * Get estimated impact summary
     */
    public function getEstimatedImpactSummary(): array
    {
        return [
            'emissions_reduction' => $this->estimated_emissions_reduction ?? 0,
            'cost' => $this->estimated_cost ?? 0,
            'savings' => $this->estimated_savings ?? 0,
            'roi' => $this->calculateROI(),
            'payback_period' => $this->calculatePaybackPeriod(),
        ];
    }

    /**
     * Calculate payback period in months
     */
    public function calculatePaybackPeriod(): ?float
    {
        if (!$this->estimated_cost || !$this->estimated_savings || $this->estimated_savings <= 0) {
            return null;
        }
        
        // Assuming annual savings, convert to months
        $annualSavings = $this->estimated_savings;
        return ($this->estimated_cost / $annualSavings) * 12;
    }

    /**
     * Get action type display name
     */
    public function getActionTypeDisplayNameAttribute(): string
    {
        return match($this->action_type) {
            'decarbonization' => 'Decarbonization',
            'data_collection' => 'Data Collection',
            'reporting' => 'Reporting',
            'compliance' => 'Compliance',
            'engagement' => 'Stakeholder Engagement',
            'training' => 'Training & Education',
            default => ucfirst(str_replace('_', ' ', $this->action_type)),
        };
    }

    /**
     * Check if action can be edited
     */
    public function canBeEdited(): bool
    {
        return in_array($this->status, ['assigned', 'in_progress']);
    }

    /**
     * Check if action can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * Get action urgency level
     */
    public function getUrgencyLevel(): string
    {
        $daysUntilDue = $this->getDaysUntilDue();
        
        if ($daysUntilDue < 0) return 'overdue';
        if ($daysUntilDue <= 3) return 'urgent';
        if ($daysUntilDue <= 7) return 'high';
        if ($daysUntilDue <= 14) return 'medium';
        return 'low';
    }

    /**
     * Boot method to handle status updates
     */
    protected static function boot()
    {
        parent::boot();
        
        static::updating(function ($action) {
            // Auto-update status to overdue if past due date
            if ($action->due_date < now() && !in_array($action->status, ['completed', 'cancelled'])) {
                $action->status = 'overdue';
            }
        });
    }
}
