<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'organization_id',
        'organizational_unit_id',
        'facility_id',
        'job_title',
        'department',
        'user_type',
        'experience_level',
        'preferred_interface',
        'default_scope',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the organization this user belongs to
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the organizational unit this user belongs to
     */
    public function organizationalUnit(): BelongsTo
    {
        return $this->belongsTo(OrganizationalUnit::class);
    }

    /**
     * Get the facility this user is primarily associated with
     */
    public function facility(): BelongsTo
    {
        return $this->belongsTo(Facility::class);
    }

    /**
     * Get activity data created by this user
     */
    public function activityData(): HasMany
    {
        return $this->hasMany(ActivityData::class);
    }

    /**
     * Check if user prefers simplified interface
     */
    public function prefersSimplifiedInterface(): bool
    {
        return $this->preferred_interface === 'simplified' ||
               $this->experience_level === 'novice';
    }

    /**
     * Check if user is an expert user
     */
    public function isExpertUser(): bool
    {
        return $this->experience_level === 'expert' ||
               $this->user_type === 'sustainability_manager';
    }

    /**
     * Check if user is an executive
     */
    public function isExecutive(): bool
    {
        return $this->user_type === 'executive' ||
               $this->hasRole(['c-suite', 'director', 'vp']);
    }

    /**
     * Get user's default dashboard type
     */
    public function getDefaultDashboardType(): string
    {
        if ($this->isExecutive()) {
            return 'executive';
        } elseif ($this->isExpertUser()) {
            return 'advanced';
        } else {
            return 'simplified';
        }
    }

    /**
     * Get user's organizational context for data entry
     */
    public function getOrganizationalContext(): array
    {
        return [
            'organization_id' => $this->organization_id,
            'organizational_unit_id' => $this->organizational_unit_id,
            'facility_id' => $this->facility_id,
            'department' => $this->department,
            'default_scope' => $this->default_scope,
        ];
    }
}
