<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ActivityData extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'activity_data';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'organization_id',
        'facility_id',
        'source_id',
        'emission_source_id', // Added as alias for source_id for compatibility
        'emission_factor_id',
        'quantity',
        'activity_unit_id',
        'date_recorded',
        'date_start',
        'date_end',
        'scope',
        'material_type',
        'evidence',
        'notes',
        'recorded_by',
        // GHG Protocol fields
        'ghg_protocol_sector_id',
        'ghg_protocol_activity_id',
        'use_ghg_protocol',
        // Enhanced scope fields
        'scope3_category_id',
        'supplier_id',
        'calculation_methodology',
        'emission_type', // combustion, fugitive, process
        'scope2_method', // location_based, market_based
        'contractual_factor',
        'supplier_factor',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'quantity' => 'decimal:6',
        'date_recorded' => 'date',
        'date_start' => 'date',
        'date_end' => 'date',
        'scope' => 'string',
        'material_type' => 'string',
        'evidence' => 'string',
        'use_ghg_protocol' => 'boolean',
        'contractual_factor' => 'decimal:8',
        'supplier_factor' => 'decimal:8',
    ];

    /**
     * The model's attributes.
     *
     * @var array
     */
    protected $attributes = [
        // Default values for new records
        'quantity' => 0,
    ];

    /**
     * Handle attribute access to make source_id and emission_source_id interchangeable
     */
    public function getEmissionSourceIdAttribute()
    {
        return $this->source_id;
    }

    /**
     * Handle attribute setting to make source_id and emission_source_id interchangeable
     */
    public function setEmissionSourceIdAttribute($value)
    {
        $this->attributes['source_id'] = $value;
    }

    /**
     * Get the facility associated with the activity data.
     */
    public function facility(): BelongsTo
    {
        return $this->belongsTo(Facility::class);
    }

    /**
     * Get the source associated with the activity data.
     * This maintains your existing relationship.
     */
    public function source(): BelongsTo
    {
        return $this->belongsTo(EmissionSource::class, 'source_id');
    }

    /**
     * Get the emission source associated with the activity data.
     * This adds compatibility with the form's expected relationship name.
     */
    public function emissionSource(): BelongsTo
    {
        return $this->belongsTo(EmissionSource::class, 'source_id');
    }

    /**
     * Get the emission factor associated with the activity data.
     */
    public function emissionFactor(): BelongsTo
    {
        return $this->belongsTo(EmissionFactor::class);
    }

    /**
     * Get the unit associated with the activity data.
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(MeasurementUnit::class, 'activity_unit_id');
    }

    /**
     * Get the emissions records that were calculated from this activity data.
     */
    public function emissions(): HasMany
    {
        return $this->hasMany(Emission::class);
    }

    /**
     * Calculate emissions based on activity data and emission factor.
     *
     * @return float|null
     */
    public function calculateEmissions(): ?float
    {
        if (!$this->emissionFactor) {
            return null;
        }

        return $this->quantity * $this->emissionFactor->factor_value; // Changed from value to factor_value
    }

    /**
     * Scope a query to only include activity data from a specific facility.
     */
    public function scopeForFacility($query, $facilityId)
    {
        return $query->where('facility_id', $facilityId);
    }

    /**
     * Scope a query to only include activity data for a specific date range.
     */
    public function scopeInDateRange($query, $start, $end)
    {
        return $query->whereBetween('date_recorded', [$start, $end]);
    }

    /**
     * Scope a query to only include activity data for a specific source.
     */
    public function scopeForSource($query, $sourceId)
    {
        return $query->where('source_id', $sourceId);
    }

    /**
     * Get the organization associated with the activity data.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    /**
     * Get the GHG Protocol sector associated with the activity data.
     */
    public function ghgProtocolSector(): BelongsTo
    {
        return $this->belongsTo(GhgProtocolSector::class, 'ghg_protocol_sector_id');
    }

    /**
     * Get the GHG Protocol activity associated with the activity data.
     */
    public function ghgProtocolActivity(): BelongsTo
    {
        return $this->belongsTo(GhgProtocolActivity::class, 'ghg_protocol_activity_id');
    }

    /**
     * Get the Scope 3 category associated with the activity data.
     */
    public function scope3Category(): BelongsTo
    {
        return $this->belongsTo(Scope3Category::class, 'scope3_category_id');
    }

    /**
     * Get the supplier associated with the activity data.
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class, 'supplier_id');
    }
}