<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'user_id',
        'user_name',
        'action',
        'table_name',
        'record_id',
        'old_values',
        'new_values',
        'action_timestamp',
        'ip_address',
        'user_agent',
        'session_id',
        'request_id',
        'context_data',
        'integrity_hash',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'context_data' => 'array',
        'action_timestamp' => 'datetime',
    ];

    /**
     * Get the organization
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the user
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for specific table
     */
    public function scopeForTable($query, string $tableName)
    {
        return $query->where('table_name', $tableName);
    }

    /**
     * Scope for specific record
     */
    public function scopeForRecord($query, string $tableName, int $recordId)
    {
        return $query->where('table_name', $tableName)
                    ->where('record_id', $recordId);
    }

    /**
     * Scope for specific action
     */
    public function scopeForAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('action_timestamp', [$startDate, $endDate]);
    }

    /**
     * Get changes summary
     */
    public function getChangesSummary(): array
    {
        $changes = [];
        
        if ($this->old_values && $this->new_values) {
            foreach ($this->new_values as $field => $newValue) {
                $oldValue = $this->old_values[$field] ?? null;
                
                if ($oldValue !== $newValue) {
                    $changes[$field] = [
                        'old' => $oldValue,
                        'new' => $newValue,
                    ];
                }
            }
        }
        
        return $changes;
    }

    /**
     * Check if this is a sensitive action
     */
    public function isSensitiveAction(): bool
    {
        $sensitiveActions = [
            'delete',
            'bulk_delete',
            'approve',
            'submit',
            'publish',
            'archive',
        ];
        
        return in_array(strtolower($this->action), $sensitiveActions);
    }

    /**
     * Get action description
     */
    public function getActionDescription(): string
    {
        return match(strtolower($this->action)) {
            'create' => 'Created new record',
            'update' => 'Updated record',
            'delete' => 'Deleted record',
            'bulk_delete' => 'Bulk deleted records',
            'approve' => 'Approved record',
            'submit' => 'Submitted record',
            'publish' => 'Published record',
            'archive' => 'Archived record',
            'restore' => 'Restored record',
            'export' => 'Exported data',
            'import' => 'Imported data',
            default => ucfirst($this->action),
        };
    }

    /**
     * Generate integrity hash
     */
    public function generateIntegrityHash(): string
    {
        $data = [
            'user_id' => $this->user_id,
            'action' => $this->action,
            'table_name' => $this->table_name,
            'record_id' => $this->record_id,
            'old_values' => $this->old_values,
            'new_values' => $this->new_values,
            'action_timestamp' => $this->action_timestamp->toISOString(),
        ];
        
        return hash('sha256', json_encode($data, JSON_SORT_KEYS));
    }

    /**
     * Verify integrity hash
     */
    public function verifyIntegrityHash(): bool
    {
        if (!$this->integrity_hash) {
            return false;
        }
        
        $calculatedHash = $this->generateIntegrityHash();
        return hash_equals($this->integrity_hash, $calculatedHash);
    }

    /**
     * Boot method to auto-generate integrity hash
     */
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($auditLog) {
            $auditLog->integrity_hash = $auditLog->generateIntegrityHash();
        });
    }
}
