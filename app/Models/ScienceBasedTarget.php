<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Carbon\Carbon;

class ScienceBasedTarget extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'target_name',
        'description',
        'temperature_scenario',
        'base_year',
        'target_year',
        'baseline_emissions',
        'target_emissions',
        'reduction_percentage',
        'scope_coverage',
        'yearly_milestones',
        'sector_pathway',
        'is_sbti_validated',
        'sbti_validation_date',
        'validation_body',
        'status',
        'methodology_details',
        'notes',
    ];

    protected $casts = [
        'baseline_emissions' => 'decimal:2',
        'target_emissions' => 'decimal:2',
        'reduction_percentage' => 'decimal:2',
        'scope_coverage' => 'array',
        'yearly_milestones' => 'array',
        'methodology_details' => 'array',
        'is_sbti_validated' => 'boolean',
        'sbti_validation_date' => 'date',
        'base_year' => 'integer',
        'target_year' => 'integer',
    ];

    /**
     * Get the organization that owns the target
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the net-zero roadmap associated with this target
     */
    public function netZeroRoadmap(): HasOne
    {
        return $this->hasOne(NetZeroRoadmap::class);
    }

    /**
     * Get progress tracking records
     */
    public function progressTracking(): HasMany
    {
        return $this->hasMany(TargetProgressTracking::class);
    }

    /**
     * Scope for active targets
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for SBTi validated targets
     */
    public function scopeSbtiValidated($query)
    {
        return $query->where('is_sbti_validated', true);
    }

    /**
     * Scope by temperature scenario
     */
    public function scopeByScenario($query, $scenario)
    {
        return $query->where('temperature_scenario', $scenario);
    }

    /**
     * Check if target is 1.5°C aligned
     */
    public function is15CAligned(): bool
    {
        return $this->temperature_scenario === '1.5C' && $this->reduction_percentage >= 42;
    }

    /**
     * Check if target covers all required scopes
     */
    public function hasRequiredScopesCoverage(): bool
    {
        $coverage = $this->scope_coverage ?? [];
        
        // Must include Scope 1 and 2
        if (!in_array('1', $coverage) || !in_array('2', $coverage)) {
            return false;
        }

        // If Scope 3 is significant (>40% of total), it must be included
        if ($this->isScope3Significant() && !in_array('3', $coverage)) {
            return false;
        }

        return true;
    }

    /**
     * Check if Scope 3 is significant for this organization
     */
    protected function isScope3Significant(): bool
    {
        // This would need to be calculated based on actual emissions data
        // For now, return a placeholder
        return true;
    }

    /**
     * Get current progress towards target
     */
    public function getCurrentProgress(): array
    {
        $currentYear = now()->year;
        $latestProgress = $this->progressTracking()
            ->where('tracking_year', '<=', $currentYear)
            ->orderBy('tracking_year', 'desc')
            ->first();

        if (!$latestProgress) {
            return [
                'progress_percentage' => 0,
                'on_track' => false,
                'message' => 'No progress data available',
            ];
        }

        return [
            'progress_percentage' => $latestProgress->progress_percentage,
            'on_track' => $latestProgress->on_track,
            'actual_emissions' => $latestProgress->actual_emissions,
            'target_emissions' => $latestProgress->target_emissions,
            'variance' => $latestProgress->variance_from_target,
            'tracking_year' => $latestProgress->tracking_year,
        ];
    }

    /**
     * Get milestone for specific year
     */
    public function getMilestoneForYear(int $year): ?float
    {
        $milestones = $this->yearly_milestones ?? [];
        return $milestones[$year] ?? null;
    }

    /**
     * Calculate required annual reduction rate
     */
    public function getRequiredAnnualReductionRate(): float
    {
        $years = $this->target_year - $this->base_year;
        if ($years <= 0) return 0;

        $totalReduction = $this->reduction_percentage / 100;
        return $totalReduction / $years;
    }

    /**
     * Check if target meets SBTi minimum criteria
     */
    public function meetsSBTiCriteria(): array
    {
        $criteria = [
            'minimum_ambition' => false,
            'scope_coverage' => false,
            'timeframe' => false,
            'absolute_target' => false,
        ];

        $messages = [];

        // Check minimum ambition (42% for 1.5°C, 25% for well below 2°C)
        $minReduction = $this->temperature_scenario === '1.5C' ? 42 : 25;
        if ($this->reduction_percentage >= $minReduction) {
            $criteria['minimum_ambition'] = true;
        } else {
            $messages[] = "Reduction percentage ({$this->reduction_percentage}%) below minimum ({$minReduction}%)";
        }

        // Check scope coverage
        if ($this->hasRequiredScopesCoverage()) {
            $criteria['scope_coverage'] = true;
        } else {
            $messages[] = "Insufficient scope coverage";
        }

        // Check timeframe (5-15 years)
        $timeframe = $this->target_year - $this->base_year;
        if ($timeframe >= 5 && $timeframe <= 15) {
            $criteria['timeframe'] = true;
        } else {
            $messages[] = "Timeframe ({$timeframe} years) outside 5-15 year range";
        }

        // Check for absolute target (assumed true for now)
        $criteria['absolute_target'] = true;

        return [
            'meets_criteria' => !in_array(false, $criteria),
            'criteria' => $criteria,
            'messages' => $messages,
        ];
    }

    /**
     * Generate target summary
     */
    public function getSummary(): array
    {
        $progress = $this->getCurrentProgress();
        $sbtiCompliance = $this->meetsSBTiCriteria();

        return [
            'target_name' => $this->target_name,
            'scenario' => $this->temperature_scenario,
            'reduction_target' => $this->reduction_percentage . '%',
            'timeframe' => $this->base_year . '-' . $this->target_year,
            'scope_coverage' => implode(', ', $this->scope_coverage ?? []),
            'sbti_validated' => $this->is_sbti_validated,
            'sbti_compliant' => $sbtiCompliance['meets_criteria'],
            'current_progress' => $progress['progress_percentage'] . '%',
            'on_track' => $progress['on_track'],
            'status' => $this->status,
        ];
    }

    /**
     * Get years remaining to target
     */
    public function getYearsRemaining(): int
    {
        return max(0, $this->target_year - now()->year);
    }

    /**
     * Check if target is overdue
     */
    public function isOverdue(): bool
    {
        return now()->year > $this->target_year && $this->status !== 'achieved';
    }

    /**
     * Get display name
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->target_name . ' (' . $this->temperature_scenario . ')';
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'gray',
            'submitted' => 'blue',
            'validated' => 'green',
            'active' => 'emerald',
            'achieved' => 'purple',
            default => 'gray',
        };
    }

    /**
     * Get temperature scenario description
     */
    public function getScenarioDescriptionAttribute(): string
    {
        return match($this->temperature_scenario) {
            '1.5C' => '1.5°C Paris Agreement aligned',
            '2C' => '2°C Paris Agreement aligned',
            'well_below_2C' => 'Well below 2°C aligned',
            default => 'Unknown scenario',
        };
    }
}
