<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MlModel extends Model
{
    use HasFactory;

    protected $fillable = [
        'model_name',
        'model_type',
        'model_purpose',
        'model_version',
        'model_description',
        'model_framework',
        'model_parameters',
        'training_data_sources',
        'feature_columns',
        'target_columns',
        'performance_metrics',
        'model_file_path',
        'status',
        'trained_at',
        'deployed_at',
        'last_prediction_at',
    ];

    protected $casts = [
        'model_parameters' => 'array',
        'training_data_sources' => 'array',
        'feature_columns' => 'array',
        'target_columns' => 'array',
        'performance_metrics' => 'array',
        'trained_at' => 'datetime',
        'deployed_at' => 'datetime',
        'last_prediction_at' => 'datetime',
    ];

    /**
     * Get predictions made by this model
     */
    public function predictions(): HasMany
    {
        return $this->hasMany(MlPrediction::class);
    }

    /**
     * Scope for specific model type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('model_type', $type);
    }

    /**
     * Scope for specific purpose
     */
    public function scopeForPurpose($query, string $purpose)
    {
        return $query->where('model_purpose', $purpose);
    }

    /**
     * Scope for trained models
     */
    public function scopeTrained($query)
    {
        return $query->where('status', 'trained');
    }

    /**
     * Scope for deployed models
     */
    public function scopeDeployed($query)
    {
        return $query->where('status', 'deployed');
    }

    /**
     * Check if model is ready for predictions
     */
    public function isReady(): bool
    {
        return in_array($this->status, ['trained', 'deployed']);
    }

    /**
     * Get model accuracy
     */
    public function getAccuracy(): ?float
    {
        return $this->performance_metrics['accuracy'] ?? null;
    }

    /**
     * Get model precision
     */
    public function getPrecision(): ?float
    {
        return $this->performance_metrics['precision'] ?? null;
    }

    /**
     * Get model recall
     */
    public function getRecall(): ?float
    {
        return $this->performance_metrics['recall'] ?? null;
    }

    /**
     * Get model F1 score
     */
    public function getF1Score(): ?float
    {
        return $this->performance_metrics['f1_score'] ?? null;
    }

    /**
     * Update last prediction timestamp
     */
    public function recordPrediction(): void
    {
        $this->update(['last_prediction_at' => now()]);
    }

    /**
     * Mark model as deployed
     */
    public function markAsDeployed(): void
    {
        $this->update([
            'status' => 'deployed',
            'deployed_at' => now(),
        ]);
    }

    /**
     * Mark model as deprecated
     */
    public function markAsDeprecated(): void
    {
        $this->update(['status' => 'deprecated']);
    }
}
