<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SpendEmissionFactor extends Model
{
    use HasFactory;

    protected $fillable = [
        'industry_sector',
        'spend_category',
        'region',
        'factor_value',
        'currency',
        'unit',
        'data_source',
        'vintage_year',
        'data_quality_rating',
        'priority',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'factor_value' => 'decimal:8',
        'vintage_year' => 'integer',
        'priority' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Scope for active factors
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by industry sector
     */
    public function scopeBySector($query, $sector)
    {
        return $query->where('industry_sector', $sector);
    }

    /**
     * Scope by region
     */
    public function scopeByRegion($query, $region)
    {
        return $query->where('region', $region);
    }

    /**
     * Get the best factor for a sector and region
     */
    public static function getBestFactor($industrySector, $region = 'global', $currency = 'USD')
    {
        return static::where('industry_sector', $industrySector)
            ->where('region', $region)
            ->where('currency', $currency)
            ->where('is_active', true)
            ->orderBy('priority', 'desc')
            ->orderBy('vintage_year', 'desc')
            ->first();
    }
}
