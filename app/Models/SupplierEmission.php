<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupplierEmission extends Model
{
    use HasFactory;

    protected $fillable = [
        'supplier_id',
        'scope3_category_id',
        'reporting_period',
        'emissions_value',
        'unit',
        'data_quality',
        'calculation_method',
        'verification_status',
        'notes',
    ];

    protected $casts = [
        'reporting_period' => 'date',
        'emissions_value' => 'decimal:6',
    ];

    /**
     * Get the supplier
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the Scope 3 category
     */
    public function scope3Category(): BelongsTo
    {
        return $this->belongsTo(Scope3Category::class);
    }
}
