<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class SupplierPortalAccess extends Model
{
    use HasFactory;

    protected $fillable = [
        'supplier_id',
        'user_id',
        'access_token',
        'portal_url',
        'access_level',
        'data_submission_enabled',
        'dashboard_access_enabled',
        'reporting_access_enabled',
        'last_login_at',
        'expires_at',
        'is_active',
        'access_permissions',
        'dashboard_config',
    ];

    protected $casts = [
        'data_submission_enabled' => 'boolean',
        'dashboard_access_enabled' => 'boolean',
        'reporting_access_enabled' => 'boolean',
        'last_login_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
        'access_permissions' => 'array',
        'dashboard_config' => 'array',
    ];

    /**
     * Get the supplier
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the user
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for active access
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Scope for expired access
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    /**
     * Check if access is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at < now();
    }

    /**
     * Check if access is valid
     */
    public function isValid(): bool
    {
        return $this->is_active && !$this->isExpired();
    }

    /**
     * Get days until expiration
     */
    public function getDaysUntilExpiration(): ?int
    {
        if (!$this->expires_at) {
            return null;
        }
        
        return now()->diffInDays($this->expires_at, false);
    }

    /**
     * Record login
     */
    public function recordLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }

    /**
     * Extend access
     */
    public function extendAccess(int $days): bool
    {
        $newExpiryDate = $this->expires_at 
            ? $this->expires_at->addDays($days)
            : now()->addDays($days);
            
        return $this->update(['expires_at' => $newExpiryDate]);
    }

    /**
     * Revoke access
     */
    public function revokeAccess(): bool
    {
        return $this->update([
            'is_active' => false,
            'expires_at' => now(),
        ]);
    }

    /**
     * Get access level display name
     */
    public function getAccessLevelDisplayNameAttribute(): string
    {
        return match($this->access_level) {
            'basic' => 'Basic Access',
            'standard' => 'Standard Access',
            'premium' => 'Premium Access',
            default => ucfirst($this->access_level),
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        if (!$this->is_active) return 'red';
        if ($this->isExpired()) return 'red';
        if ($this->getDaysUntilExpiration() && $this->getDaysUntilExpiration() <= 7) return 'yellow';
        return 'green';
    }

    /**
     * Get status text
     */
    public function getStatusTextAttribute(): string
    {
        if (!$this->is_active) return 'Inactive';
        if ($this->isExpired()) return 'Expired';
        
        $daysUntilExpiration = $this->getDaysUntilExpiration();
        if ($daysUntilExpiration === null) return 'Active (No Expiry)';
        if ($daysUntilExpiration <= 0) return 'Expired';
        if ($daysUntilExpiration <= 7) return "Expires in {$daysUntilExpiration} days";
        
        return 'Active';
    }

    /**
     * Get enabled features
     */
    public function getEnabledFeatures(): array
    {
        $features = [];
        
        if ($this->data_submission_enabled) {
            $features[] = 'Data Submission';
        }
        
        if ($this->dashboard_access_enabled) {
            $features[] = 'Dashboard Access';
        }
        
        if ($this->reporting_access_enabled) {
            $features[] = 'Reporting Access';
        }
        
        return $features;
    }

    /**
     * Check if feature is enabled
     */
    public function hasFeature(string $feature): bool
    {
        return match($feature) {
            'data_submission' => $this->data_submission_enabled,
            'dashboard_access' => $this->dashboard_access_enabled,
            'reporting_access' => $this->reporting_access_enabled,
            default => false,
        };
    }

    /**
     * Get login frequency (logins per month)
     */
    public function getLoginFrequency(): float
    {
        if (!$this->last_login_at) {
            return 0;
        }
        
        $monthsSinceCreation = $this->created_at->diffInMonths(now());
        if ($monthsSinceCreation === 0) {
            return 1; // At least one login in the creation month
        }
        
        // This is a simplified calculation - in practice, you'd track login history
        return 1 / $monthsSinceCreation;
    }

    /**
     * Generate new access token
     */
    public function regenerateAccessToken(): string
    {
        $newToken = \Illuminate\Support\Str::random(32);
        $this->update(['access_token' => $newToken]);
        
        return $newToken;
    }

    /**
     * Get portal access summary
     */
    public function getAccessSummary(): array
    {
        return [
            'supplier_name' => $this->supplier->name,
            'user_email' => $this->user->email,
            'access_level' => $this->access_level_display_name,
            'status' => $this->status_text,
            'enabled_features' => $this->getEnabledFeatures(),
            'last_login' => $this->last_login_at?->format('Y-m-d H:i:s'),
            'expires_at' => $this->expires_at?->format('Y-m-d'),
            'days_until_expiration' => $this->getDaysUntilExpiration(),
        ];
    }
}
