<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ComplianceReport extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'report_name',
        'description',
        'framework_type',
        'reporting_year',
        'report_data',
        'audit_trail_summary',
        'file_path',
        'file_format',
        'file_size',
        'status',
        'generated_at',
        'generated_by',
        'approved_at',
        'approved_by',
        'submitted_at',
        'submission_reference',
        'validation_results',
        'report_hash',
    ];

    protected $casts = [
        'report_data' => 'array',
        'audit_trail_summary' => 'array',
        'validation_results' => 'array',
        'generated_at' => 'datetime',
        'approved_at' => 'datetime',
        'submitted_at' => 'datetime',
        'reporting_year' => 'integer',
        'file_size' => 'integer',
    ];

    /**
     * Get the organization
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get report sections
     */
    public function sections(): HasMany
    {
        return $this->hasMany(ReportSection::class);
    }

    /**
     * Get report validations
     */
    public function validations(): HasMany
    {
        return $this->hasMany(ReportValidation::class);
    }

    /**
     * Get report approvals
     */
    public function approvals(): HasMany
    {
        return $this->hasMany(ReportApproval::class);
    }

    /**
     * Get external assurance
     */
    public function externalAssurance(): HasOne
    {
        return $this->hasOne(ExternalAssurance::class);
    }

    /**
     * Scope for specific framework
     */
    public function scopeForFramework($query, string $framework)
    {
        return $query->where('framework_type', $framework);
    }

    /**
     * Scope for specific year
     */
    public function scopeForYear($query, int $year)
    {
        return $query->where('reporting_year', $year);
    }

    /**
     * Scope for specific status
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for published reports
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope for pending approval
     */
    public function scopePendingApproval($query)
    {
        return $query->where('status', 'in_review');
    }

    /**
     * Get framework display name
     */
    public function getFrameworkDisplayNameAttribute(): string
    {
        return match($this->framework_type) {
            'sec_climate_rule' => 'SEC Climate Disclosure Rule',
            'csrd' => 'Corporate Sustainability Reporting Directive',
            'tcfd' => 'Task Force on Climate-related Financial Disclosures',
            'cdp' => 'Carbon Disclosure Project',
            'ghg_protocol' => 'GHG Protocol Corporate Standard',
            'sbti' => 'Science Based Targets initiative',
            'issb' => 'International Sustainability Standards Board',
            default => ucfirst(str_replace('_', ' ', $this->framework_type)),
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'gray',
            'in_review' => 'yellow',
            'approved' => 'green',
            'submitted' => 'blue',
            'published' => 'purple',
            default => 'gray',
        };
    }

    /**
     * Check if report is editable
     */
    public function isEditable(): bool
    {
        return in_array($this->status, ['draft', 'in_review']);
    }

    /**
     * Check if report can be approved
     */
    public function canBeApproved(): bool
    {
        return $this->status === 'in_review' && $this->hasPassedValidation();
    }

    /**
     * Check if report can be submitted
     */
    public function canBeSubmitted(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if report has passed validation
     */
    public function hasPassedValidation(): bool
    {
        $failedValidations = $this->validations()
            ->where('validation_status', 'failed')
            ->count();
            
        return $failedValidations === 0;
    }

    /**
     * Get validation summary
     */
    public function getValidationSummary(): array
    {
        $validations = $this->validations;
        
        return [
            'total' => $validations->count(),
            'passed' => $validations->where('validation_status', 'passed')->count(),
            'failed' => $validations->where('validation_status', 'failed')->count(),
            'warnings' => $validations->where('validation_status', 'warning')->count(),
            'skipped' => $validations->where('validation_status', 'skipped')->count(),
        ];
    }

    /**
     * Get approval progress
     */
    public function getApprovalProgress(): array
    {
        $approvals = $this->approvals()->orderBy('approval_order')->get();
        
        $totalStages = $approvals->count();
        $completedStages = $approvals->where('approval_status', 'approved')->count();
        
        return [
            'total_stages' => $totalStages,
            'completed_stages' => $completedStages,
            'progress_percentage' => $totalStages > 0 ? round(($completedStages / $totalStages) * 100, 1) : 0,
            'current_stage' => $approvals->where('approval_status', 'pending')->first()?->approval_stage,
            'is_complete' => $completedStages === $totalStages,
        ];
    }

    /**
     * Generate report hash for integrity
     */
    public function generateReportHash(): string
    {
        $data = [
            'organization_id' => $this->organization_id,
            'framework_type' => $this->framework_type,
            'reporting_year' => $this->reporting_year,
            'report_data' => $this->report_data,
            'generated_at' => $this->generated_at->toISOString(),
        ];
        
        return hash('sha256', json_encode($data, JSON_SORT_KEYS));
    }

    /**
     * Verify report integrity
     */
    public function verifyReportIntegrity(): bool
    {
        if (!$this->report_hash) {
            return false;
        }
        
        $calculatedHash = $this->generateReportHash();
        return hash_equals($this->report_hash, $calculatedHash);
    }

    /**
     * Get file size in human readable format
     */
    public function getFileSizeHumanAttribute(): string
    {
        if (!$this->file_size) {
            return 'Unknown';
        }
        
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get report age in days
     */
    public function getReportAgeAttribute(): int
    {
        return $this->generated_at->diffInDays(now());
    }

    /**
     * Check if report is stale
     */
    public function isStale(): bool
    {
        // Consider report stale if it's more than 30 days old and still in draft
        return $this->status === 'draft' && $this->report_age > 30;
    }

    /**
     * Get next approval stage
     */
    public function getNextApprovalStage(): ?ReportApproval
    {
        return $this->approvals()
            ->where('approval_status', 'pending')
            ->orderBy('approval_order')
            ->first();
    }

    /**
     * Mark as submitted
     */
    public function markAsSubmitted(string $submissionReference = null): bool
    {
        if (!$this->canBeSubmitted()) {
            return false;
        }
        
        $this->update([
            'status' => 'submitted',
            'submitted_at' => now(),
            'submission_reference' => $submissionReference,
        ]);
        
        return true;
    }

    /**
     * Mark as published
     */
    public function markAsPublished(): bool
    {
        if ($this->status !== 'submitted') {
            return false;
        }
        
        $this->update(['status' => 'published']);
        
        return true;
    }

    /**
     * Get compliance score
     */
    public function getComplianceScore(): float
    {
        $validationSummary = $this->getValidationSummary();
        
        if ($validationSummary['total'] === 0) {
            return 0;
        }
        
        $score = ($validationSummary['passed'] / $validationSummary['total']) * 100;
        
        // Reduce score for warnings
        $warningPenalty = ($validationSummary['warnings'] / $validationSummary['total']) * 10;
        
        return max(0, $score - $warningPenalty);
    }

    /**
     * Get data completeness percentage
     */
    public function getDataCompletenessPercentage(): float
    {
        $reportData = $this->report_data ?? [];
        
        // This would be framework-specific logic
        // For now, return a placeholder calculation
        $requiredFields = $this->getRequiredFieldsForFramework();
        $completedFields = $this->getCompletedFields($reportData);
        
        if (empty($requiredFields)) {
            return 100;
        }
        
        return (count($completedFields) / count($requiredFields)) * 100;
    }

    /**
     * Get required fields for framework
     */
    protected function getRequiredFieldsForFramework(): array
    {
        // This would be implemented based on framework requirements
        return match($this->framework_type) {
            'sec_climate_rule' => ['governance', 'strategy', 'risk_management', 'metrics_targets'],
            'tcfd' => ['governance', 'strategy', 'risk_management', 'metrics_targets'],
            'cdp' => ['c1_governance', 'c2_risks_opportunities', 'c6_emissions_data'],
            'csrd' => ['double_materiality', 'esrs_climate', 'value_chain'],
            default => [],
        };
    }

    /**
     * Get completed fields from report data
     */
    protected function getCompletedFields(array $reportData): array
    {
        $completed = [];
        
        foreach ($reportData as $key => $value) {
            if (!empty($value) && $value !== null) {
                $completed[] = $key;
            }
        }
        
        return $completed;
    }

    /**
     * Boot method to auto-generate report hash
     */
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($report) {
            $report->report_hash = $report->generateReportHash();
        });
        
        static::updating(function ($report) {
            if ($report->isDirty(['report_data', 'framework_type', 'reporting_year'])) {
                $report->report_hash = $report->generateReportHash();
            }
        });
    }
}
