<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RegionalConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'region_code',
        'region_name',
        'region_type',
        'parent_region_code',
        'iso_country_code',
        'iso_subdivision_code',
        'currency_code',
        'language_code',
        'timezone',
        'regulatory_frameworks',
        'compliance_requirements',
        'localization_settings',
        'is_active',
    ];

    protected $casts = [
        'regulatory_frameworks' => 'array',
        'compliance_requirements' => 'array',
        'localization_settings' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the parent region
     */
    public function parentRegion(): BelongsTo
    {
        return $this->belongsTo(RegionalConfiguration::class, 'parent_region_code', 'region_code');
    }

    /**
     * Get child regions
     */
    public function childRegions(): HasMany
    {
        return $this->hasMany(RegionalConfiguration::class, 'parent_region_code', 'region_code');
    }

    /**
     * Get organizations in this region
     */
    public function organizations(): HasMany
    {
        return $this->hasMany(Organization::class, 'country_code', 'iso_country_code');
    }

    /**
     * Scope for active regions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific region type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('region_type', $type);
    }

    /**
     * Get localization setting
     */
    public function getLocalizationSetting(string $key, $default = null)
    {
        return data_get($this->localization_settings, $key, $default);
    }

    /**
     * Get compliance requirement
     */
    public function getComplianceRequirement(string $key, $default = null)
    {
        return data_get($this->compliance_requirements, $key, $default);
    }

    /**
     * Check if framework is applicable
     */
    public function hasFramework(string $frameworkCode): bool
    {
        return in_array($frameworkCode, $this->regulatory_frameworks ?? []);
    }
}
