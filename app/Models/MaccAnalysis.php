<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MaccAnalysis extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'analysis_name',
        'description',
        'target_reduction_percentage',
        'timeframe_years',
        'currency',
        'baseline_emissions',
        'curve_data',
        'optimal_portfolio',
        'total_investment_required',
        'average_cost_per_tonne',
        'analysis_date',
        'status',
    ];

    protected $casts = [
        'target_reduction_percentage' => 'decimal:2',
        'timeframe_years' => 'integer',
        'baseline_emissions' => 'decimal:2',
        'curve_data' => 'array',
        'optimal_portfolio' => 'array',
        'total_investment_required' => 'decimal:2',
        'average_cost_per_tonne' => 'decimal:2',
        'analysis_date' => 'date',
    ];

    /**
     * Get the organization
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get option selections
     */
    public function optionSelections(): HasMany
    {
        return $this->hasMany(MaccOptionSelection::class);
    }
}
