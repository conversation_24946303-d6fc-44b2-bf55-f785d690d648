<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Supplier extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'supplier_code',
        'contact_email',
        'contact_phone',
        'address',
        'country',
        'region',
        'industry_sector',
        'supplier_type',
        'annual_spend',
        'currency',
        'data_quality_rating',
        'carbon_disclosure_level',
        'sustainability_certifications',
        'scope3_categories',
        'primary_products_services',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'annual_spend' => 'decimal:2',
        'scope3_categories' => 'array',
        'sustainability_certifications' => 'array',
        'primary_products_services' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get activity data for this supplier
     */
    public function activityData(): HasMany
    {
        return $this->hasMany(ActivityData::class);
    }

    /**
     * Get Scope 3 categories this supplier contributes to
     */
    public function scope3Categories(): BelongsToMany
    {
        return $this->belongsToMany(
            Scope3Category::class,
            'supplier_scope3_categories',
            'supplier_id',
            'category_id'
        )->withPivot(['calculation_method', 'data_quality', 'annual_emissions']);
    }

    /**
     * Get supplier emissions data
     */
    public function supplierEmissions(): HasMany
    {
        return $this->hasMany(SupplierEmission::class);
    }

    /**
     * Get spend data for this supplier
     */
    public function spendData(): HasMany
    {
        return $this->hasMany(SupplierSpend::class);
    }

    /**
     * Scope for active suppliers
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by industry sector
     */
    public function scopeBySector($query, $sector)
    {
        return $query->where('industry_sector', $sector);
    }

    /**
     * Scope by country
     */
    public function scopeByCountry($query, $country)
    {
        return $query->where('country', $country);
    }

    /**
     * Scope by data quality rating
     */
    public function scopeByDataQuality($query, $rating)
    {
        return $query->where('data_quality_rating', $rating);
    }

    /**
     * Get suppliers with high data quality
     */
    public function scopeHighQuality($query)
    {
        return $query->whereIn('data_quality_rating', ['A', 'B']);
    }

    /**
     * Get suppliers with carbon disclosure
     */
    public function scopeWithCarbonDisclosure($query)
    {
        return $query->whereIn('carbon_disclosure_level', ['CDP', 'GRI', 'TCFD', 'Custom']);
    }

    /**
     * Calculate total emissions from this supplier
     */
    public function calculateTotalEmissions(int $year = null): array
    {
        $year = $year ?? now()->year;
        
        $emissions = $this->supplierEmissions()
            ->whereYear('reporting_period', $year)
            ->get();

        $total = $emissions->sum('emissions_value');
        $byCategory = $emissions->groupBy('scope3_category_id')
            ->map(function ($categoryEmissions) {
                return $categoryEmissions->sum('emissions_value');
            });

        return [
            'total_emissions' => $total,
            'by_category' => $byCategory->toArray(),
            'year' => $year,
            'data_quality' => $this->getAverageDataQuality($emissions),
        ];
    }

    /**
     * Get average data quality for supplier emissions
     */
    protected function getAverageDataQuality($emissions): string
    {
        if ($emissions->isEmpty()) {
            return 'N/A';
        }

        $qualityScores = [
            'A' => 4,
            'B' => 3,
            'C' => 2,
            'D' => 1,
        ];

        $totalScore = 0;
        $count = 0;

        foreach ($emissions as $emission) {
            if (isset($qualityScores[$emission->data_quality])) {
                $totalScore += $qualityScores[$emission->data_quality];
                $count++;
            }
        }

        if ($count === 0) {
            return 'N/A';
        }

        $averageScore = $totalScore / $count;
        
        if ($averageScore >= 3.5) return 'A';
        if ($averageScore >= 2.5) return 'B';
        if ($averageScore >= 1.5) return 'C';
        return 'D';
    }

    /**
     * Get recommended calculation method for this supplier
     */
    public function getRecommendedCalculationMethod(): string
    {
        // High quality suppliers with carbon disclosure
        if (in_array($this->data_quality_rating, ['A', 'B']) && 
            in_array($this->carbon_disclosure_level, ['CDP', 'GRI', 'TCFD'])) {
            return 'supplier_specific';
        }

        // Medium quality suppliers
        if (in_array($this->data_quality_rating, ['B', 'C'])) {
            return 'activity_based';
        }

        // Low quality or no data
        return 'spend_based';
    }

    /**
     * Check if supplier has primary data available
     */
    public function hasPrimaryData(): bool
    {
        return in_array($this->carbon_disclosure_level, ['CDP', 'GRI', 'TCFD', 'Custom']) &&
               in_array($this->data_quality_rating, ['A', 'B']);
    }

    /**
     * Get supplier's contribution to Scope 3 categories
     */
    public function getScope3Contribution(): array
    {
        $categories = $this->scope3Categories()->get();
        $contribution = [];

        foreach ($categories as $category) {
            $contribution[$category->category_number] = [
                'category_name' => $category->name,
                'calculation_method' => $category->pivot->calculation_method,
                'data_quality' => $category->pivot->data_quality,
                'annual_emissions' => $category->pivot->annual_emissions,
            ];
        }

        return $contribution;
    }

    /**
     * Update supplier emissions data
     */
    public function updateEmissionsData(array $emissionsData): bool
    {
        try {
            foreach ($emissionsData as $categoryId => $data) {
                SupplierEmission::updateOrCreate([
                    'supplier_id' => $this->id,
                    'scope3_category_id' => $categoryId,
                    'reporting_period' => $data['reporting_period'],
                ], [
                    'emissions_value' => $data['emissions_value'],
                    'unit' => $data['unit'] ?? 'tCO2e',
                    'data_quality' => $data['data_quality'] ?? $this->data_quality_rating,
                    'calculation_method' => $data['calculation_method'] ?? 'supplier_reported',
                    'verification_status' => $data['verification_status'] ?? 'unverified',
                    'notes' => $data['notes'] ?? null,
                ]);
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get spend-based emission factors for this supplier
     */
    public function getSpendBasedFactors(): array
    {
        // Get industry-specific spend factors
        $factors = SpendEmissionFactor::where('industry_sector', $this->industry_sector)
            ->orWhere('industry_sector', 'general')
            ->orderBy('priority', 'desc')
            ->get();

        return $factors->toArray();
    }

    /**
     * Calculate spend-based emissions
     */
    public function calculateSpendBasedEmissions(float $spendAmount, string $currency = null): array
    {
        $currency = $currency ?? $this->currency ?? 'USD';
        $factors = $this->getSpendBasedFactors();

        if (empty($factors)) {
            return [
                'success' => false,
                'error' => 'No spend-based factors available for this supplier',
            ];
        }

        $factor = $factors[0]; // Use highest priority factor
        $emissions = $spendAmount * $factor['factor_value'];

        return [
            'success' => true,
            'emissions' => $emissions,
            'spend_amount' => $spendAmount,
            'currency' => $currency,
            'factor_used' => $factor,
            'calculation_method' => 'spend_based',
        ];
    }

    /**
     * Get supplier risk assessment
     */
    public function getRiskAssessment(): array
    {
        $risk = 'low';
        $factors = [];

        // High spend = higher risk
        if ($this->annual_spend > 1000000) {
            $risk = 'high';
            $factors[] = 'High annual spend';
        } elseif ($this->annual_spend > 100000) {
            $risk = 'medium';
            $factors[] = 'Medium annual spend';
        }

        // Low data quality = higher risk
        if (in_array($this->data_quality_rating, ['D', 'C'])) {
            $risk = 'high';
            $factors[] = 'Low data quality';
        }

        // No carbon disclosure = higher risk
        if (empty($this->carbon_disclosure_level) || $this->carbon_disclosure_level === 'none') {
            $risk = $risk === 'high' ? 'high' : 'medium';
            $factors[] = 'No carbon disclosure';
        }

        // High-emission sectors
        $highEmissionSectors = ['energy', 'manufacturing', 'transportation', 'agriculture'];
        if (in_array(strtolower($this->industry_sector), $highEmissionSectors)) {
            $risk = $risk === 'low' ? 'medium' : 'high';
            $factors[] = 'High-emission industry sector';
        }

        return [
            'risk_level' => $risk,
            'risk_factors' => $factors,
            'recommendation' => $this->getRiskRecommendation($risk),
        ];
    }

    /**
     * Get recommendation based on risk level
     */
    protected function getRiskRecommendation(string $risk): string
    {
        switch ($risk) {
            case 'high':
                return 'Prioritize for direct engagement and primary data collection';
            case 'medium':
                return 'Consider supplier questionnaire and activity-based calculations';
            case 'low':
            default:
                return 'Spend-based calculations may be sufficient';
        }
    }

    /**
     * Get display name for the supplier
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->supplier_code ? "{$this->name} ({$this->supplier_code})" : $this->name;
    }
}
