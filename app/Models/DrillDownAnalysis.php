<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DrillDownAnalysis extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'analysis_name',
        'description',
        'reporting_year',
        'corporate_total',
        'scope_breakdown',
        'facility_breakdown',
        'source_breakdown',
        'activity_detail',
        'supplier_breakdown',
        'traceability_matrix',
        'drill_down_paths',
        'summary_statistics',
        'analysis_date',
        'analyzed_by',
        'status',
    ];

    protected $casts = [
        'corporate_total' => 'array',
        'scope_breakdown' => 'array',
        'facility_breakdown' => 'array',
        'source_breakdown' => 'array',
        'activity_detail' => 'array',
        'supplier_breakdown' => 'array',
        'traceability_matrix' => 'array',
        'drill_down_paths' => 'array',
        'summary_statistics' => 'array',
        'analysis_date' => 'datetime',
        'reporting_year' => 'integer',
    ];

    /**
     * Get the organization
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Scope for specific year
     */
    public function scopeForYear($query, int $year)
    {
        return $query->where('reporting_year', $year);
    }

    /**
     * Scope for completed analyses
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }
}
