<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmissionFactorVariant extends Model
{
    use HasFactory;

    protected $fillable = [
        'base_factor_id',
        'variant_type',
        'variant_code',
        'variant_name',
        'variant_description',
        'region_id',
        'factor_value',
        'co2_factor',
        'ch4_factor',
        'n2o_factor',
        'carbon_content',
        'oxidation_factor',
        'heating_value',
        'input_unit_id',
        'output_unit_id',
        'data_quality_rating',
        'uncertainty_percentage',
        'uncertainty_lower',
        'uncertainty_upper',
        'vintage_year',
        'valid_from',
        'valid_until',
        'variant_conditions',
        'notes',
        'priority',
        'is_active',
    ];

    protected $casts = [
        'factor_value' => 'decimal:10',
        'co2_factor' => 'decimal:10',
        'ch4_factor' => 'decimal:10',
        'n2o_factor' => 'decimal:10',
        'carbon_content' => 'decimal:8',
        'oxidation_factor' => 'decimal:6',
        'heating_value' => 'decimal:6',
        'uncertainty_percentage' => 'decimal:4',
        'uncertainty_lower' => 'decimal:4',
        'uncertainty_upper' => 'decimal:4',
        'valid_from' => 'date',
        'valid_until' => 'date',
        'variant_conditions' => 'array',
        'is_active' => 'boolean',
        'vintage_year' => 'integer',
        'priority' => 'integer',
    ];

    /**
     * Get the base emission factor
     */
    public function baseFactor(): BelongsTo
    {
        return $this->belongsTo(GhgProtocolEmissionFactor::class, 'base_factor_id');
    }

    /**
     * Get the region this variant applies to
     */
    public function region(): BelongsTo
    {
        return $this->belongsTo(GhgProtocolRegion::class, 'region_id');
    }

    /**
     * Get the input unit (if overridden)
     */
    public function inputUnit(): BelongsTo
    {
        return $this->belongsTo(MeasurementUnit::class, 'input_unit_id');
    }

    /**
     * Get the output unit (if overridden)
     */
    public function outputUnit(): BelongsTo
    {
        return $this->belongsTo(MeasurementUnit::class, 'output_unit_id');
    }

    /**
     * Scope to only return active variants
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by variant type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('variant_type', $type);
    }

    /**
     * Scope to filter by region
     */
    public function scopeByRegion($query, $regionId)
    {
        return $query->where('region_id', $regionId);
    }

    /**
     * Scope to order by priority
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * Get the effective factor value (variant or base)
     */
    public function getEffectiveFactorValue(): ?float
    {
        return $this->factor_value ?? $this->baseFactor->factor_value;
    }

    /**
     * Get the effective CO2 factor (variant or base)
     */
    public function getEffectiveCo2Factor(): ?float
    {
        return $this->co2_factor ?? $this->baseFactor->co2_factor;
    }

    /**
     * Get the effective CH4 factor (variant or base)
     */
    public function getEffectiveCh4Factor(): ?float
    {
        return $this->ch4_factor ?? $this->baseFactor->ch4_factor;
    }

    /**
     * Get the effective N2O factor (variant or base)
     */
    public function getEffectiveN2oFactor(): ?float
    {
        return $this->n2o_factor ?? $this->baseFactor->n2o_factor;
    }

    /**
     * Get the effective input unit (variant or base)
     */
    public function getEffectiveInputUnit()
    {
        return $this->inputUnit ?? $this->baseFactor->inputUnit;
    }

    /**
     * Get the effective output unit (variant or base)
     */
    public function getEffectiveOutputUnit()
    {
        return $this->outputUnit ?? $this->baseFactor->outputUnit;
    }

    /**
     * Check if this variant matches given conditions
     */
    public function matchesConditions($conditions): bool
    {
        if (!$this->variant_conditions) {
            return true;
        }

        foreach ($this->variant_conditions as $key => $value) {
            if (!isset($conditions[$key]) || $conditions[$key] !== $value) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if this variant is valid for a given date
     */
    public function isValidForDate($date): bool
    {
        $checkDate = is_string($date) ? \Carbon\Carbon::parse($date) : $date;
        
        if ($this->valid_from && $checkDate->lt($this->valid_from)) {
            return false;
        }
        
        if ($this->valid_until && $checkDate->gt($this->valid_until)) {
            return false;
        }
        
        return true;
    }

    /**
     * Get the full name including base factor name
     */
    public function getFullNameAttribute(): string
    {
        return $this->baseFactor->name . ' - ' . $this->variant_name;
    }
}
