<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class GhgProtocolSector extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get legacy emission factors (for backward compatibility)
     */
    public function emissionFactors(): HasMany
    {
        return $this->hasMany(EmissionFactor::class, 'ghg_protocol_sector_id');
    }

    /**
     * Get GHG Protocol emission factors
     */
    public function ghgProtocolEmissionFactors(): Has<PERSON>any
    {
        return $this->hasMany(GhgProtocolEmissionFactor::class, 'sector_id');
    }

    /**
     * Get activities in this sector
     */
    public function activities(): HasMany
    {
        return $this->hasMany(GhgProtocolActivity::class, 'sector_id');
    }

    /**
     * Scope to only return active sectors
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('display_name');
    }
}
