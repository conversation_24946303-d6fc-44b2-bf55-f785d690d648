<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmissionFactorUnit extends Model
{
    use HasFactory;

    protected $fillable = [
        'emission_factor_id',
        'ghg_protocol_factor_id',
        'unit_role',
        'measurement_unit_id',
        'conversion_factor',
        'conversion_context',
        'is_primary',
        'notes',
    ];

    protected $casts = [
        'conversion_factor' => 'decimal:8',
        'is_primary' => 'boolean',
    ];

    /**
     * Get the legacy emission factor (for backward compatibility)
     */
    public function emissionFactor(): BelongsTo
    {
        return $this->belongsTo(EmissionFactor::class);
    }

    /**
     * Get the GHG Protocol emission factor
     */
    public function ghgProtocolFactor(): BelongsTo
    {
        return $this->belongsTo(GhgProtocolEmissionFactor::class, 'ghg_protocol_factor_id');
    }

    /**
     * Get the measurement unit
     */
    public function measurementUnit(): BelongsTo
    {
        return $this->belongsTo(MeasurementUnit::class);
    }

    /**
     * Scope to filter by unit role
     */
    public function scopeOfRole($query, $role)
    {
        return $query->where('unit_role', $role);
    }

    /**
     * Scope to filter by unit type (legacy compatibility)
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('unit_role', $type);
    }

    /**
     * Scope to get primary units only
     */
    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    /**
     * Scope to get input units
     */
    public function scopeInputUnits($query)
    {
        return $query->where('unit_role', 'input');
    }

    /**
     * Scope to get output units
     */
    public function scopeOutputUnits($query)
    {
        return $query->where('unit_role', 'output');
    }

    /**
     * Get the factor this unit belongs to (either legacy or GHG Protocol)
     */
    public function getFactor()
    {
        return $this->ghgProtocolFactor ?? $this->emissionFactor;
    }
}
