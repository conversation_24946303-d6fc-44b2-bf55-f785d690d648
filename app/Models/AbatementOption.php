<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AbatementOption extends Model
{
    use HasFactory;

    protected $fillable = [
        'option_code',
        'name',
        'description',
        'category',
        'applicable_sectors',
        'applicable_scopes',
        'capital_cost_per_tonne',
        'operational_cost_per_tonne',
        'energy_savings_per_tonne',
        'other_savings_per_tonne',
        'implementation_complexity',
        'implementation_time',
        'max_reduction_percentage',
        'max_absolute_reduction',
        'co_benefits',
        'risks',
        'is_active',
    ];

    protected $casts = [
        'applicable_sectors' => 'array',
        'applicable_scopes' => 'array',
        'capital_cost_per_tonne' => 'decimal:2',
        'operational_cost_per_tonne' => 'decimal:2',
        'energy_savings_per_tonne' => 'decimal:2',
        'other_savings_per_tonne' => 'decimal:2',
        'implementation_complexity' => 'integer',
        'implementation_time' => 'integer',
        'max_reduction_percentage' => 'decimal:4',
        'max_absolute_reduction' => 'decimal:2',
        'co_benefits' => 'array',
        'risks' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get MACC option selections
     */
    public function maccSelections(): HasMany
    {
        return $this->hasMany(MaccOptionSelection::class);
    }

    /**
     * Get decarbonization initiatives using this option
     */
    public function initiatives(): HasMany
    {
        return $this->hasMany(DecarbonizationInitiative::class);
    }

    /**
     * Scope for active options
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope by applicable sector
     */
    public function scopeForSector($query, $sector)
    {
        return $query->where(function ($q) use ($sector) {
            $q->whereJsonContains('applicable_sectors', $sector)
              ->orWhereJsonContains('applicable_sectors', 'all');
        });
    }

    /**
     * Scope by applicable scope
     */
    public function scopeForScope($query, $scope)
    {
        return $query->whereJsonContains('applicable_scopes', $scope);
    }

    /**
     * Check if option is applicable to sector
     */
    public function isApplicableToSector(string $sector): bool
    {
        $sectors = $this->applicable_sectors ?? [];
        return in_array($sector, $sectors) || in_array('all', $sectors);
    }

    /**
     * Check if option is applicable to scope
     */
    public function isApplicableToScope(string $scope): bool
    {
        $scopes = $this->applicable_scopes ?? [];
        return in_array($scope, $scopes);
    }

    /**
     * Calculate total cost per tonne over timeframe
     */
    public function calculateTotalCostPerTonne(int $timeframe = 10, float $discountRate = 0.08): float
    {
        $capitalCost = $this->capital_cost_per_tonne;
        $annualOpCost = $this->operational_cost_per_tonne;
        
        // Present value of operational costs
        $presentValueOpCost = 0;
        for ($year = 1; $year <= $timeframe; $year++) {
            $presentValueOpCost += $annualOpCost / pow(1 + $discountRate, $year);
        }
        
        return $capitalCost + $presentValueOpCost;
    }

    /**
     * Calculate annual savings per tonne
     */
    public function calculateAnnualSavings(float $energyPrice = 50, float $carbonPrice = 25): float
    {
        $energySavings = ($this->energy_savings_per_tonne ?? 0) * $energyPrice;
        $carbonSavings = $carbonPrice; // $25/tCO2e saved
        $otherSavings = $this->other_savings_per_tonne ?? 0;
        
        return $energySavings + $carbonSavings + $otherSavings;
    }

    /**
     * Calculate payback period
     */
    public function calculatePaybackPeriod(float $energyPrice = 50, float $carbonPrice = 25): ?float
    {
        $capitalCost = $this->capital_cost_per_tonne;
        $annualSavings = $this->calculateAnnualSavings($energyPrice, $carbonPrice);
        $annualOpCost = $this->operational_cost_per_tonne;
        
        $netAnnualSavings = $annualSavings - $annualOpCost;
        
        if ($netAnnualSavings <= 0) {
            return null; // No payback
        }
        
        return $capitalCost / $netAnnualSavings;
    }

    /**
     * Get complexity description
     */
    public function getComplexityDescriptionAttribute(): string
    {
        return match(true) {
            $this->implementation_complexity <= 3 => 'Low',
            $this->implementation_complexity <= 6 => 'Medium',
            $this->implementation_complexity <= 8 => 'High',
            default => 'Very High',
        };
    }

    /**
     * Get implementation time description
     */
    public function getImplementationTimeDescriptionAttribute(): string
    {
        return match(true) {
            $this->implementation_time <= 6 => 'Short-term (≤6 months)',
            $this->implementation_time <= 18 => 'Medium-term (6-18 months)',
            $this->implementation_time <= 36 => 'Long-term (1.5-3 years)',
            default => 'Very Long-term (>3 years)',
        };
    }

    /**
     * Get cost effectiveness rating
     */
    public function getCostEffectivenessRating(int $timeframe = 10): string
    {
        $totalCost = $this->calculateTotalCostPerTonne($timeframe);
        
        return match(true) {
            $totalCost < 0 => 'Highly Profitable',
            $totalCost < 50 => 'Very Cost Effective',
            $totalCost < 100 => 'Cost Effective',
            $totalCost < 200 => 'Moderately Cost Effective',
            default => 'High Cost',
        };
    }

    /**
     * Get option summary
     */
    public function getSummary(int $timeframe = 10): array
    {
        return [
            'name' => $this->name,
            'category' => $this->category,
            'total_cost_per_tonne' => $this->calculateTotalCostPerTonne($timeframe),
            'payback_period' => $this->calculatePaybackPeriod(),
            'complexity' => $this->complexity_description,
            'implementation_time' => $this->implementation_time_description,
            'cost_effectiveness' => $this->getCostEffectivenessRating($timeframe),
            'max_reduction' => $this->max_reduction_percentage * 100 . '%',
            'applicable_sectors' => $this->applicable_sectors,
            'applicable_scopes' => $this->applicable_scopes,
        ];
    }

    /**
     * Get co-benefits list
     */
    public function getCoBenefitsList(): array
    {
        return $this->co_benefits ?? [];
    }

    /**
     * Get risks list
     */
    public function getRisksList(): array
    {
        return $this->risks ?? [];
    }

    /**
     * Check if option has co-benefits
     */
    public function hasCoBenefits(): bool
    {
        return !empty($this->co_benefits);
    }

    /**
     * Check if option has risks
     */
    public function hasRisks(): bool
    {
        return !empty($this->risks);
    }

    /**
     * Get display name with code
     */
    public function getDisplayNameAttribute(): string
    {
        return "{$this->option_code}: {$this->name}";
    }
}
