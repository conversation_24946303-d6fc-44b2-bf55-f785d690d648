<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ElectricityGridFactor extends Model
{
    use HasFactory;

    protected $fillable = [
        'grid_region',
        'country',
        'state_province',
        'location_based_factor',
        'residual_mix_factor',
        'vintage_year',
        'data_source',
        'data_quality',
        'is_active',
    ];

    protected $casts = [
        'location_based_factor' => 'decimal:8',
        'residual_mix_factor' => 'decimal:8',
        'vintage_year' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Scope for active factors
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by country
     */
    public function scopeByCountry($query, $country)
    {
        return $query->where('country', $country);
    }

    /**
     * Scope by vintage year
     */
    public function scopeByYear($query, $year)
    {
        return $query->where('vintage_year', $year);
    }

    /**
     * Get the most recent factor for a region
     */
    public static function getLatestForRegion($gridRegion)
    {
        return static::where('grid_region', $gridRegion)
            ->where('is_active', true)
            ->orderBy('vintage_year', 'desc')
            ->first();
    }
}
