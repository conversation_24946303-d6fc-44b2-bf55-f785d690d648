<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MaccOptionSelection extends Model
{
    use HasFactory;

    protected $fillable = [
        'macc_analysis_id',
        'abatement_option_id',
        'reduction_used',
        'cost_allocated',
        'cost_per_tonne',
        'implementation_priority',
        'implementation_status',
        'planned_start_date',
        'planned_completion_date',
        'notes',
    ];

    protected $casts = [
        'reduction_used' => 'decimal:2',
        'cost_allocated' => 'decimal:2',
        'cost_per_tonne' => 'decimal:2',
        'implementation_priority' => 'integer',
        'planned_start_date' => 'date',
        'planned_completion_date' => 'date',
    ];

    /**
     * Get the MACC analysis
     */
    public function maccAnalysis(): BelongsTo
    {
        return $this->belongsTo(MaccAnalysis::class);
    }

    /**
     * Get the abatement option
     */
    public function abatementOption(): BelongsTo
    {
        return $this->belongsTo(AbatementOption::class);
    }
}
