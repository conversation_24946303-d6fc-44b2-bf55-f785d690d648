<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ScenarioProjection extends Model
{
    use HasFactory;

    protected $fillable = [
        'scenario_analysis_id',
        'scenario_name',
        'projection_year',
        'projected_emissions',
        'scope_1_emissions',
        'scope_2_emissions',
        'scope_3_emissions',
        'growth_factor',
        'efficiency_factor',
        'key_assumptions',
    ];

    protected $casts = [
        'projection_year' => 'integer',
        'projected_emissions' => 'decimal:2',
        'scope_1_emissions' => 'decimal:2',
        'scope_2_emissions' => 'decimal:2',
        'scope_3_emissions' => 'decimal:2',
        'growth_factor' => 'decimal:4',
        'efficiency_factor' => 'decimal:4',
        'key_assumptions' => 'array',
    ];

    /**
     * Get the scenario analysis
     */
    public function scenarioAnalysis(): BelongsTo
    {
        return $this->belongsTo(ScenarioAnalysis::class);
    }
}
