<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class SystemModule extends Model
{
    use HasFactory;

    protected $fillable = [
        'module_name',
        'module_class',
        'module_version',
        'module_description',
        'module_dependencies',
        'is_core_module',
        'is_active',
        'configuration',
        'installed_at',
        'activated_at',
        'deactivated_at',
    ];

    protected $casts = [
        'module_dependencies' => 'array',
        'is_core_module' => 'boolean',
        'is_active' => 'boolean',
        'configuration' => 'array',
        'installed_at' => 'datetime',
        'activated_at' => 'datetime',
        'deactivated_at' => 'datetime',
    ];

    /**
     * Get the module configuration
     */
    public function moduleConfiguration(): HasOne
    {
        return $this->hasOne(ModuleConfiguration::class, 'module_name', 'module_name');
    }

    /**
     * Scope for active modules
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for core modules
     */
    public function scopeCore($query)
    {
        return $query->where('is_core_module', true);
    }

    /**
     * Scope for third-party modules
     */
    public function scopeThirdParty($query)
    {
        return $query->where('is_core_module', false);
    }

    /**
     * Check if module is installed
     */
    public function isInstalled(): bool
    {
        return !is_null($this->installed_at);
    }

    /**
     * Check if module has dependencies
     */
    public function hasDependencies(): bool
    {
        return !empty($this->module_dependencies);
    }

    /**
     * Get module status
     */
    public function getStatus(): string
    {
        if (!$this->isInstalled()) {
            return 'not_installed';
        }
        
        if ($this->is_active) {
            return 'active';
        }
        
        return 'inactive';
    }

    /**
     * Get module instance
     */
    public function getInstance()
    {
        if (!class_exists($this->module_class)) {
            return null;
        }
        
        return new $this->module_class();
    }
}
