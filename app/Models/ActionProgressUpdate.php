<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ActionProgressUpdate extends Model
{
    use HasFactory;

    protected $fillable = [
        'stakeholder_action_id',
        'update_date',
        'progress_percentage',
        'status',
        'notes',
        'attachments',
        'actual_emissions_reduction',
        'actual_cost',
        'actual_savings',
        'metrics',
        'updated_by_user_id',
    ];

    protected $casts = [
        'update_date' => 'date',
        'progress_percentage' => 'decimal:2',
        'attachments' => 'array',
        'actual_emissions_reduction' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'actual_savings' => 'decimal:2',
        'metrics' => 'array',
    ];

    /**
     * Get the stakeholder action
     */
    public function stakeholderAction(): BelongsTo
    {
        return $this->belongsTo(StakeholderAction::class);
    }

    /**
     * Get the user who updated
     */
    public function updatedByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by_user_id');
    }
}
