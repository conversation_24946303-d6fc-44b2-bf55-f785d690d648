<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupplierDataSubmission extends Model
{
    use HasFactory;

    protected $fillable = [
        'supplier_id',
        'submitted_by_user_id',
        'submission_type',
        'reporting_year',
        'reporting_period',
        'submission_data',
        'data_quality_rating',
        'validation_results',
        'status',
        'review_comments',
        'reviewed_by_user_id',
        'reviewed_at',
        'attachments',
        'submission_reference',
    ];

    protected $casts = [
        'reporting_year' => 'integer',
        'submission_data' => 'array',
        'validation_results' => 'array',
        'reviewed_at' => 'datetime',
        'attachments' => 'array',
    ];

    /**
     * Get the supplier
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the user who submitted
     */
    public function submittedByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'submitted_by_user_id');
    }

    /**
     * Get the user who reviewed
     */
    public function reviewedByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by_user_id');
    }
}
