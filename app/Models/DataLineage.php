<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DataLineage extends Model
{
    use HasFactory;

    protected $fillable = [
        'activity_data_id',
        'source_system_type',
        'source_system_name',
        'source_record_id',
        'ingestion_timestamp',
        'transformation_steps',
        'validation_results',
        'data_quality_score',
        'metadata',
        'lineage_hash',
    ];

    protected $casts = [
        'transformation_steps' => 'array',
        'validation_results' => 'array',
        'metadata' => 'array',
        'ingestion_timestamp' => 'datetime',
    ];

    /**
     * Get the activity data
     */
    public function activityData(): BelongsTo
    {
        return $this->belongsTo(ActivityData::class);
    }

    /**
     * Generate lineage hash
     */
    public function generateLineageHash(): string
    {
        $data = [
            'activity_data_id' => $this->activity_data_id,
            'source_system_type' => $this->source_system_type,
            'source_system_name' => $this->source_system_name,
            'source_record_id' => $this->source_record_id,
            'ingestion_timestamp' => $this->ingestion_timestamp->toISOString(),
        ];
        
        return hash('sha256', json_encode($data, JSON_SORT_KEYS));
    }
}
