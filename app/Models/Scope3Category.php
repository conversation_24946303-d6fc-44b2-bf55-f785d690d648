<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Scope3Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_number',
        'name',
        'description',
        'upstream_downstream',
        'calculation_approaches',
        'data_requirements',
        'typical_activities',
        'is_active',
        'guidance_notes',
    ];

    protected $casts = [
        'calculation_approaches' => 'array',
        'data_requirements' => 'array',
        'typical_activities' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get activity data for this category
     */
    public function activityData(): HasMany
    {
        return $this->hasMany(ActivityData::class, 'scope3_category_id');
    }

    /**
     * Get emission factors applicable to this category
     */
    public function emissionFactors(): BelongsToMany
    {
        return $this->belongsToMany(
            GhgProtocolEmissionFactor::class,
            'scope3_category_factors',
            'category_id',
            'factor_id'
        )->withPivot(['applicability_notes', 'priority']);
    }

    /**
     * Get suppliers associated with this category
     */
    public function suppliers(): BelongsToMany
    {
        return $this->belongsToMany(
            Supplier::class,
            'supplier_scope3_categories',
            'category_id',
            'supplier_id'
        )->withPivot(['calculation_method', 'data_quality']);
    }

    /**
     * Scope for upstream categories (1-8)
     */
    public function scopeUpstream($query)
    {
        return $query->where('upstream_downstream', 'upstream');
    }

    /**
     * Scope for downstream categories (9-15)
     */
    public function scopeDownstream($query)
    {
        return $query->where('upstream_downstream', 'downstream');
    }

    /**
     * Scope for active categories
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get calculation approaches for this category
     */
    public function getCalculationApproaches(): array
    {
        return $this->calculation_approaches ?? [];
    }

    /**
     * Check if category supports specific calculation approach
     */
    public function supportsApproach(string $approach): bool
    {
        return in_array($approach, $this->getCalculationApproaches());
    }

    /**
     * Get recommended calculation approach based on data availability
     */
    public function getRecommendedApproach(array $availableData = []): ?string
    {
        $approaches = $this->getCalculationApproaches();
        
        // Priority order: supplier-specific > activity-based > spend-based
        $priorityOrder = [
            'supplier_specific',
            'activity_based',
            'spend_based',
            'hybrid'
        ];

        foreach ($priorityOrder as $approach) {
            if (in_array($approach, $approaches)) {
                // Check if we have the required data for this approach
                if ($this->hasRequiredDataForApproach($approach, $availableData)) {
                    return $approach;
                }
            }
        }

        return $approaches[0] ?? null;
    }

    /**
     * Check if required data is available for calculation approach
     */
    protected function hasRequiredDataForApproach(string $approach, array $availableData): bool
    {
        $requirements = [
            'supplier_specific' => ['supplier_data', 'activity_data'],
            'activity_based' => ['activity_data', 'emission_factors'],
            'spend_based' => ['spend_data', 'spend_factors'],
            'hybrid' => ['partial_data'],
        ];

        $required = $requirements[$approach] ?? [];
        
        foreach ($required as $requirement) {
            if (!isset($availableData[$requirement]) || empty($availableData[$requirement])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get data quality requirements for this category
     */
    public function getDataQualityRequirements(): array
    {
        $requirements = [
            1 => ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'],
            2 => ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'],
            3 => ['primary_data' => 'required', 'secondary_data' => 'not_acceptable'],
            4 => ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'],
            5 => ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'],
            6 => ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'],
            7 => ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'],
            8 => ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'],
            9 => ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'],
            10 => ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'],
            11 => ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'],
            12 => ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'],
            13 => ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'],
            14 => ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'],
            15 => ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'],
        ];

        return $requirements[$this->category_number] ?? ['primary_data' => 'preferred', 'secondary_data' => 'acceptable'];
    }

    /**
     * Get typical emission factors for this category
     */
    public function getTypicalEmissionFactors()
    {
        return $this->emissionFactors()
            ->where('is_active', true)
            ->orderBy('priority', 'desc')
            ->get();
    }

    /**
     * Calculate emissions for this category
     */
    public function calculateEmissions(array $activityData, array $options = []): array
    {
        $approach = $options['approach'] ?? $this->getRecommendedApproach($activityData);
        
        if (!$approach) {
            return [
                'success' => false,
                'error' => 'No suitable calculation approach available',
                'category' => $this->name,
            ];
        }

        switch ($approach) {
            case 'supplier_specific':
                return $this->calculateSupplierSpecific($activityData, $options);
            case 'activity_based':
                return $this->calculateActivityBased($activityData, $options);
            case 'spend_based':
                return $this->calculateSpendBased($activityData, $options);
            case 'hybrid':
                return $this->calculateHybrid($activityData, $options);
            default:
                return [
                    'success' => false,
                    'error' => "Unknown calculation approach: {$approach}",
                    'category' => $this->name,
                ];
        }
    }

    /**
     * Calculate using supplier-specific data
     */
    protected function calculateSupplierSpecific(array $activityData, array $options): array
    {
        // Implementation for supplier-specific calculations
        return [
            'success' => true,
            'approach' => 'supplier_specific',
            'emissions' => 0, // Placeholder
            'category' => $this->name,
            'details' => 'Supplier-specific calculation performed',
        ];
    }

    /**
     * Calculate using activity-based approach
     */
    protected function calculateActivityBased(array $activityData, array $options): array
    {
        // Implementation for activity-based calculations
        return [
            'success' => true,
            'approach' => 'activity_based',
            'emissions' => 0, // Placeholder
            'category' => $this->name,
            'details' => 'Activity-based calculation performed',
        ];
    }

    /**
     * Calculate using spend-based approach
     */
    protected function calculateSpendBased(array $activityData, array $options): array
    {
        // Implementation for spend-based calculations
        return [
            'success' => true,
            'approach' => 'spend_based',
            'emissions' => 0, // Placeholder
            'category' => $this->name,
            'details' => 'Spend-based calculation performed',
        ];
    }

    /**
     * Calculate using hybrid approach
     */
    protected function calculateHybrid(array $activityData, array $options): array
    {
        // Implementation for hybrid calculations
        return [
            'success' => true,
            'approach' => 'hybrid',
            'emissions' => 0, // Placeholder
            'category' => $this->name,
            'details' => 'Hybrid calculation performed',
        ];
    }

    /**
     * Get display name with category number
     */
    public function getDisplayNameAttribute(): string
    {
        return "Category {$this->category_number}: {$this->name}";
    }

    /**
     * Check if this is an upstream category
     */
    public function isUpstream(): bool
    {
        return $this->upstream_downstream === 'upstream';
    }

    /**
     * Check if this is a downstream category
     */
    public function isDownstream(): bool
    {
        return $this->upstream_downstream === 'downstream';
    }
}
