<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GhgProtocolRegion extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'region_type',
        'parent_code',
        'iso_country_code',
        'iso_subdivision_code',
        'latitude',
        'longitude',
        'description',
        'applicable_sectors',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'applicable_sectors' => 'array',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    /**
     * Get the parent region
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(GhgProtocolRegion::class, 'parent_code', 'code');
    }

    /**
     * Get child regions
     */
    public function children(): HasMany
    {
        return $this->hasMany(GhgProtocolRegion::class, 'parent_code', 'code');
    }

    /**
     * Get emission factors for this region
     */
    public function emissionFactors(): HasMany
    {
        return $this->hasMany(GhgProtocolEmissionFactor::class, 'region_id');
    }

    /**
     * Get emission factor variants for this region
     */
    public function emissionFactorVariants(): HasMany
    {
        return $this->hasMany(EmissionFactorVariant::class, 'region_id');
    }

    /**
     * Scope to only return active regions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order and name
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Scope to filter by region type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('region_type', $type);
    }

    /**
     * Scope to filter by country
     */
    public function scopeByCountry($query, $countryCode)
    {
        return $query->where('iso_country_code', $countryCode);
    }

    /**
     * Get all countries
     */
    public function scopeCountries($query)
    {
        return $query->where('region_type', 'country');
    }

    /**
     * Get all states/provinces for a country
     */
    public function scopeSubdivisions($query, $countryCode = null)
    {
        $query = $query->whereIn('region_type', ['state', 'province']);
        
        if ($countryCode) {
            $query->where('iso_country_code', $countryCode);
        }
        
        return $query;
    }

    /**
     * Get all grid regions
     */
    public function scopeGridRegions($query)
    {
        return $query->where('region_type', 'grid_region');
    }

    /**
     * Check if this region applies to a specific sector
     */
    public function appliesToSector($sectorCode): bool
    {
        if (!$this->applicable_sectors) {
            return true; // If no sectors specified, applies to all
        }
        
        return in_array($sectorCode, $this->applicable_sectors);
    }

    /**
     * Get the full hierarchical name (e.g., "United States > California")
     */
    public function getFullNameAttribute(): string
    {
        if ($this->parent) {
            return $this->parent->full_name . ' > ' . $this->name;
        }
        
        return $this->name;
    }

    /**
     * Get all ancestor regions
     */
    public function getAncestors()
    {
        $ancestors = collect();
        $current = $this->parent;
        
        while ($current) {
            $ancestors->prepend($current);
            $current = $current->parent;
        }
        
        return $ancestors;
    }

    /**
     * Get all descendant regions
     */
    public function getDescendants()
    {
        $descendants = collect();
        
        foreach ($this->children as $child) {
            $descendants->push($child);
            $descendants = $descendants->merge($child->getDescendants());
        }
        
        return $descendants;
    }
}
