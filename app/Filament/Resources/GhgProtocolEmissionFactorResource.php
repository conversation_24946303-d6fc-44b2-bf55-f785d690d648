<?php

namespace App\Filament\Resources;

use Filament\Resources\Resource;
use App\Filament\Resources\GhgProtocolEmissionFactorResource\Pages;
use App\Models\GhgProtocolEmissionFactor;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Form;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class GhgProtocolEmissionFactorResource extends Resource
{
    protected static ?string $model = GhgProtocolEmissionFactor::class;
    protected static ?string $navigationIcon = 'heroicon-o-beaker';
    protected static ?string $navigationLabel = 'GHG Protocol Factors';
    protected static ?string $navigationGroup = 'Data Management';
    protected static ?int $navigationSort = 21; // After regular emission factors

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('GhgProtocolEmissionFactor')
                    ->tabs([
                        Tabs\Tab::make('Basic Information')
                            ->schema([
                                Section::make('Core Factor Data')
                                    ->schema([
                                        TextInput::make('code')
                                            ->required()
                                            ->maxLength(100)
                                            ->label('Factor Code')
                                            ->helperText('Unique identifier for this factor')
                                            ->columnSpan(1),

                                        TextInput::make('name')
                                            ->required()
                                            ->maxLength(255)
                                            ->label('Factor Name')
                                            ->columnSpan(1),

                                        Textarea::make('description')
                                            ->rows(2)
                                            ->maxLength(500)
                                            ->label('Description')
                                            ->columnSpan(2),

                                        Select::make('sector_id')
                                            ->relationship('sector', 'display_name')
                                            ->required()
                                            ->searchable()
                                            ->preload()
                                            ->label('GHG Protocol Sector'),

                                        Select::make('activity_id')
                                            ->relationship('activity', 'name')
                                            ->required()
                                            ->searchable()
                                            ->preload()
                                            ->label('Activity'),

                                        Select::make('region_id')
                                            ->relationship('region', 'name')
                                            ->searchable()
                                            ->preload()
                                            ->label('Region')
                                            ->helperText('Leave blank for global factors'),

                                        Select::make('gas_id')
                                            ->relationship('gas', 'name')
                                            ->required()
                                            ->searchable()
                                            ->preload()
                                            ->label('Greenhouse Gas'),
                                    ])
                                    ->columns(2),

                                Section::make('Factor Values')
                                    ->schema([
                                        TextInput::make('factor_value')
                                            ->required()
                                            ->numeric()
                                            ->step(0.0000000001)
                                            ->label('Primary Factor Value'),

                                        TextInput::make('co2_factor')
                                            ->numeric()
                                            ->step(0.0000000001)
                                            ->label('CO2 Factor')
                                            ->helperText('Specific CO2 emission factor'),

                                        TextInput::make('ch4_factor')
                                            ->numeric()
                                            ->step(0.0000000001)
                                            ->label('CH4 Factor')
                                            ->helperText('Specific CH4 emission factor'),

                                        TextInput::make('n2o_factor')
                                            ->numeric()
                                            ->step(0.0000000001)
                                            ->label('N2O Factor')
                                            ->helperText('Specific N2O emission factor'),
                                    ])
                                    ->columns(2),

                                Section::make('Units')
                                    ->schema([
                                        Select::make('input_unit_id')
                                            ->relationship('inputUnit', 'name')
                                            ->required()
                                            ->searchable()
                                            ->preload()
                                            ->label('Input Unit'),

                                        Select::make('output_unit_id')
                                            ->relationship('outputUnit', 'name')
                                            ->required()
                                            ->searchable()
                                            ->preload()
                                            ->label('Output Unit'),
                                    ])
                                    ->columns(2),
                            ]),

                        Tabs\Tab::make('Methodology & Quality')
                            ->schema([
                                Section::make('Calculation Method')
                                    ->schema([
                                        TextInput::make('calculation_method')
                                            ->maxLength(100)
                                            ->label('Calculation Method')
                                            ->helperText('e.g., Energy content basis, Mass basis'),

                                        TextInput::make('methodology_reference')
                                            ->maxLength(500)
                                            ->label('Methodology Reference')
                                            ->helperText('Reference to calculation methodology'),

                                        Select::make('tier_level')
                                            ->options([
                                                1 => 'Tier 1 - Default factors',
                                                2 => 'Tier 2 - Country/region specific',
                                                3 => 'Tier 3 - Facility specific',
                                            ])
                                            ->label('IPCC Tier Level'),

                                        TextInput::make('priority')
                                            ->numeric()
                                            ->label('Priority')
                                            ->helperText('Higher numbers = higher priority')
                                            ->default(500),
                                    ])
                                    ->columns(2),

                                Section::make('Data Quality')
                                    ->schema([
                                        Select::make('data_quality_rating')
                                            ->options([
                                                'High' => 'High Quality',
                                                'Medium' => 'Medium Quality',
                                                'Low' => 'Low Quality',
                                            ])
                                            ->label('Data Quality Rating'),

                                        TextInput::make('uncertainty_percentage')
                                            ->numeric()
                                            ->suffix('%')
                                            ->label('Uncertainty Percentage'),

                                        TextInput::make('uncertainty_lower')
                                            ->numeric()
                                            ->step(0.0001)
                                            ->label('Uncertainty Lower Bound'),

                                        TextInput::make('uncertainty_upper')
                                            ->numeric()
                                            ->step(0.0001)
                                            ->label('Uncertainty Upper Bound'),

                                        Select::make('uncertainty_type')
                                            ->options([
                                                'percentage' => 'Percentage',
                                                'absolute' => 'Absolute',
                                                'range' => 'Range',
                                            ])
                                            ->label('Uncertainty Type'),
                                    ])
                                    ->columns(2),
                            ]),

                        Tabs\Tab::make('Source & Validity')
                            ->schema([
                                Section::make('Data Source')
                                    ->schema([
                                        TextInput::make('data_source')
                                            ->maxLength(255)
                                            ->label('Data Source')
                                            ->helperText('e.g., GHG Protocol, EPA, DEFRA'),

                                        TextInput::make('data_source_detail')
                                            ->maxLength(500)
                                            ->label('Data Source Detail')
                                            ->helperText('Specific table or document reference'),

                                        TextInput::make('reference_link')
                                            ->url()
                                            ->maxLength(500)
                                            ->label('Reference Link'),

                                        Textarea::make('notes')
                                            ->rows(3)
                                            ->label('Notes')
                                            ->columnSpan(2),
                                    ])
                                    ->columns(2),

                                Section::make('Temporal Validity')
                                    ->schema([
                                        TextInput::make('vintage_year')
                                            ->numeric()
                                            ->label('Vintage Year')
                                            ->helperText('Year this data represents'),

                                        DatePicker::make('valid_from')
                                            ->label('Valid From')
                                            ->helperText('When this factor becomes valid'),

                                        DatePicker::make('valid_until')
                                            ->label('Valid Until')
                                            ->helperText('When this factor expires'),
                                    ])
                                    ->columns(3),

                                Section::make('Status')
                                    ->schema([
                                        Toggle::make('is_default')
                                            ->label('Default Factor')
                                            ->helperText('Use as default for this activity/gas combination'),

                                        Toggle::make('is_active')
                                            ->label('Active')
                                            ->helperText('Factor is available for use')
                                            ->default(true),
                                    ])
                                    ->columns(2),
                            ]),

                        Tabs\Tab::make('Technical Parameters')
                            ->schema([
                                Section::make('Chemical Properties')
                                    ->schema([
                                        TextInput::make('carbon_content')
                                            ->numeric()
                                            ->step(0.00001)
                                            ->label('Carbon Content')
                                            ->helperText('Carbon content for carbon content approach'),

                                        TextInput::make('oxidation_factor')
                                            ->numeric()
                                            ->step(0.00001)
                                            ->label('Oxidation Factor')
                                            ->helperText('Fraction of carbon oxidized (0-1)'),

                                        TextInput::make('heating_value')
                                            ->numeric()
                                            ->step(0.01)
                                            ->label('Heating Value'),

                                        Select::make('heating_value_type')
                                            ->options([
                                                'HHV' => 'Higher Heating Value',
                                                'LHV' => 'Lower Heating Value',
                                            ])
                                            ->label('Heating Value Type'),
                                    ])
                                    ->columns(2),

                                Section::make('Metadata')
                                    ->schema([
                                        KeyValue::make('metadata')
                                            ->label('Additional Metadata')
                                            ->helperText('Key-value pairs for additional factor information')
                                            ->columnSpanFull(),
                                    ]),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code')
                    ->label('Code')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('name')
                    ->label('Factor Name')
                    ->searchable()
                    ->sortable()
                    ->limit(50),

                TextColumn::make('sector.display_name')
                    ->label('Sector')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('activity.name')
                    ->label('Activity')
                    ->searchable()
                    ->sortable()
                    ->limit(30),

                TextColumn::make('gas.name')
                    ->label('Gas')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('factor_value')
                    ->label('Factor Value')
                    ->numeric(8)
                    ->sortable(),

                TextColumn::make('inputUnit.symbol')
                    ->label('Input Unit')
                    ->sortable(),

                TextColumn::make('outputUnit.symbol')
                    ->label('Output Unit')
                    ->sortable(),

                TextColumn::make('region.name')
                    ->label('Region')
                    ->searchable()
                    ->sortable()
                    ->toggleable()
                    ->placeholder('Global'),

                TextColumn::make('calculation_method')
                    ->label('Method')
                    ->searchable()
                    ->toggleable()
                    ->limit(20),

                TextColumn::make('data_quality_rating')
                    ->label('Quality')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'High' => 'success',
                        'Medium' => 'warning',
                        'Low' => 'danger',
                        default => 'gray',
                    })
                    ->toggleable(),

                TextColumn::make('tier_level')
                    ->label('Tier')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        '3' => 'success',
                        '2' => 'info',
                        '1' => 'warning',
                        default => 'gray',
                    })
                    ->toggleable(),

                TextColumn::make('vintage_year')
                    ->label('Year')
                    ->sortable()
                    ->toggleable(),

                IconColumn::make('is_default')
                    ->label('Default')
                    ->boolean()
                    ->toggleable(),

                IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->toggleable(),

                TextColumn::make('data_source')
                    ->label('Source')
                    ->searchable()
                    ->toggleable()
                    ->limit(15),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('sector_id')
                    ->relationship('sector', 'display_name')
                    ->label('Sector')
                    ->multiple()
                    ->preload(),

                SelectFilter::make('gas_id')
                    ->relationship('gas', 'name')
                    ->label('Greenhouse Gas')
                    ->multiple()
                    ->preload(),

                SelectFilter::make('region_id')
                    ->relationship('region', 'name')
                    ->label('Region')
                    ->multiple()
                    ->preload(),

                SelectFilter::make('data_quality_rating')
                    ->options([
                        'High' => 'High Quality',
                        'Medium' => 'Medium Quality',
                        'Low' => 'Low Quality',
                    ])
                    ->label('Data Quality'),

                SelectFilter::make('tier_level')
                    ->options([
                        1 => 'Tier 1',
                        2 => 'Tier 2',
                        3 => 'Tier 3',
                    ])
                    ->label('IPCC Tier'),

                Filter::make('is_active')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true))
                    ->label('Active Only')
                    ->default(),

                Filter::make('is_default')
                    ->query(fn (Builder $query): Builder => $query->where('is_default', true))
                    ->label('Default Factors Only'),

                Filter::make('vintage_year')
                    ->form([
                        TextInput::make('from_year')
                            ->numeric()
                            ->label('From Year'),
                        TextInput::make('to_year')
                            ->numeric()
                            ->label('To Year'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from_year'],
                                fn (Builder $query, $year): Builder => $query->where('vintage_year', '>=', $year),
                            )
                            ->when(
                                $data['to_year'],
                                fn (Builder $query, $year): Builder => $query->where('vintage_year', '<=', $year),
                            );
                    })
                    ->label('Vintage Year Range'),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('priority', 'desc')
            ->striped();
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGhgProtocolEmissionFactors::route('/'),
            'create' => Pages\CreateGhgProtocolEmissionFactor::route('/create'),
            'view' => Pages\ViewGhgProtocolEmissionFactor::route('/{record}'),
            'edit' => Pages\EditGhgProtocolEmissionFactor::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::active()->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }
}
