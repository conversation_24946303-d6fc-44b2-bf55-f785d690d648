<?php

namespace App\Filament\Resources\GhgProtocolEmissionFactorResource\Pages;

use App\Filament\Resources\GhgProtocolEmissionFactorResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewGhgProtocolEmissionFactor extends ViewRecord
{
    protected static string $resource = GhgProtocolEmissionFactorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
