<?php

namespace App\Filament\Resources\GhgProtocolEmissionFactorResource\Pages;

use App\Filament\Resources\GhgProtocolEmissionFactorResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditGhgProtocolEmissionFactor extends EditRecord
{
    protected static string $resource = GhgProtocolEmissionFactorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
