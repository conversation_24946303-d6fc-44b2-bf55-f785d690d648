<?php

namespace App\Filament\Resources\GhgProtocolEmissionFactorResource\Pages;

use App\Filament\Resources\GhgProtocolEmissionFactorResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListGhgProtocolEmissionFactors extends ListRecords
{
    protected static string $resource = GhgProtocolEmissionFactorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
