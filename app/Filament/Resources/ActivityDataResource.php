<?php

namespace App\Filament\Resources;

use App\Services\EmissionCalculationService;
use App\Services\EmissionFactorLookupService;
use Filament\Resources\Resource;
use App\Filament\Resources\ActivityDataResource\Pages;
use App\Models\ActivityData;
use App\Models\Emission;
use App\Models\EmissionFactor;
use App\Models\GhgProtocolEmissionFactor;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\Facility;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use App\Filament\Actions\ImportAction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class ActivityDataResource extends Resource
{
    protected static ?string $model = ActivityData::class;
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';
    protected static ?string $navigationLabel = 'Activity Data';
    protected static ?string $navigationGroup = 'Data Management';
    protected static ?int $navigationSort = 10; // First in Data Management

    const SCOPE_OPTIONS = [
        '1' => 'Scope 1 (Direct)',
        '2' => 'Scope 2 (Indirect - Energy)',
        '3' => 'Scope 3 (Indirect - Value Chain)',
    ];

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('ActivityData')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Organization & Source')
                            ->schema([
                                Forms\Components\Select::make('organization_id')
                                    ->label('Organization')
                                    ->options(\App\Models\Organization::pluck('name', 'id'))
                                    ->default(fn ($record) => $record ? $record->organization_id : Auth::user()->organization_id ?? null)
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->live()
                                    ->afterStateUpdated(fn (Forms\Set $set) => $set('facility_id', null)),

                                Forms\Components\Select::make('facility_id')
                                    ->label('Facility')
                                    ->options(fn ($get) => $get('organization_id') ? Facility::forOrganization($get('organization_id'))->pluck('name', 'id') : Facility::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required(),

                                Forms\Components\Select::make('source_id')
                                    ->label('Emission Source')
                                    ->relationship('source', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->live()
                                    ->afterStateUpdated(fn (Forms\Set $set) => $set('emission_factor_id', null)),

                                Forms\Components\Select::make('scope')
                                    ->label('GHG Scope')
                                    ->options(self::SCOPE_OPTIONS)
                                    ->required()
                                    ->afterStateHydrated(fn ($component, $state, $record) => !$state && $record && $record->source ? $component->state($record->source->scope ?? null) : null),

                                Forms\Components\DatePicker::make('date_start')
                                    ->label('Activity Period Start')
                                    ->required()
                                    ->default(now()->startOfMonth()),
                                    //->rules(['before_or_equal:date_end']),

                                Forms\Components\DatePicker::make('date_end')
                                    ->label('Activity Period End')
                                    ->required()
                                    ->default(now()->endOfMonth())
                                    ->rules(['after_or_equal:date_start']),
                            ])
                            ->columns(2),

                        Forms\Components\Tabs\Tab::make('Measurement Data')
                            ->schema([
                                Forms\Components\TextInput::make('quantity')
                                    ->required()
                                    ->numeric()
                                    ->step(0.01)
                                    ->label('Quantity')
                                    ->live(),

                                Forms\Components\Select::make('activity_unit_id')
                                    ->relationship('unit', 'name')
                                    ->required()
                                    ->searchable()
                                    ->preload()
                                    ->label('Unit')
                                    ->live()
                                    ->afterStateUpdated(fn (Forms\Set $set) => $set('emission_factor_id', null)),

                                Forms\Components\Select::make('emission_factor_id')
                                    ->relationship('emissionFactor', 'name', fn ($query, Forms\Get $get) => $query->where('source_id', $get('source_id'))->where('input_unit_id', $get('activity_unit_id')))
                                    ->getOptionLabelFromRecordUsing(fn ($record) => "{$record->name}: {$record->factor_value} {$record->unit->symbol}")
                                    ->searchable()
                                    ->preload()
                                    ->label('Emission Factor')
                                    ->required()
                                    ->helperText('Ensure emission factor matches emission source and unit.')
                                    ->live()
                                    ->afterStateUpdated(fn (Forms\Get $get, Forms\Set $set) => $set('calculated_emissions', EmissionCalculationService::calculate($get('quantity'), $get('emission_factor_id')))),

                                Forms\Components\Placeholder::make('calculated_emissions')
                                    ->label('Calculated Emissions')
                                    ->content(function (Forms\Get $get) {
                                        if (!$get('quantity')) {
                                            return 'Enter quantity to see calculated emissions';
                                        }

                                        // Create a temporary ActivityData object for calculation
                                        $tempActivityData = new ActivityData([
                                            'quantity' => $get('quantity'),
                                            'emission_factor_id' => $get('emission_factor_id'),
                                            'ghg_protocol_activity_id' => $get('ghg_protocol_activity_id'),
                                            'ghg_protocol_sector_id' => $get('ghg_protocol_sector_id'),
                                            'use_ghg_protocol' => $get('use_ghg_protocol'),
                                            'date_recorded' => now(),
                                        ]);

                                        if ($get('use_ghg_protocol') && $get('ghg_protocol_activity_id')) {
                                            return 'GHG Protocol calculation will be performed on save';
                                        } elseif ($get('emission_factor_id')) {
                                            $emissions = EmissionCalculationService::calculate($get('quantity'), $get('emission_factor_id'));
                                            return $emissions ? number_format($emissions, 4) . ' tCO₂e' : 'Unable to calculate';
                                        }

                                        return 'Select emission factor or configure GHG Protocol to calculate emissions';
                                    }),

                                Forms\Components\Select::make('material_type')
                                    ->label('Fuel or Material Type')
                                    ->options([
                                        'Synthetic Fertilizer' => 'Synthetic Fertilizer',
                                        'Organic Fertilizer' => 'Organic Fertilizer',
                                        'Natural Gas' => 'Natural Gas',
                                        'Electricity' => 'Electricity',
                                    ])
                                    ->required(),
                            ])
                            ->columns(2),

                        Forms\Components\Tabs\Tab::make('GHG Protocol Factors')
                            ->schema([
                                Forms\Components\Section::make('Enhanced Factor Selection')
                                    ->description('Use GHG Protocol standardized emission factors for more accurate calculations')
                                    ->schema([
                                        Forms\Components\Select::make('ghg_protocol_sector_id')
                                            ->label('GHG Protocol Sector')
                                            ->relationship('ghgProtocolSector', 'display_name')
                                            ->searchable()
                                            ->preload()
                                            ->live()
                                            ->afterStateUpdated(fn (Forms\Set $set) => $set('ghg_protocol_activity_id', null)),

                                        Forms\Components\Select::make('ghg_protocol_activity_id')
                                            ->label('GHG Protocol Activity')
                                            ->options(fn (Forms\Get $get) => $get('ghg_protocol_sector_id')
                                                ? GhgProtocolActivity::where('sector_id', $get('ghg_protocol_sector_id'))->pluck('name', 'id')
                                                : [])
                                            ->searchable()
                                            ->live()
                                            ->visible(fn (Forms\Get $get) => filled($get('ghg_protocol_sector_id'))),

                                        Forms\Components\Placeholder::make('ghg_protocol_factors_preview')
                                            ->label('Available GHG Protocol Factors')
                                            ->content(function (Forms\Get $get) {
                                                if (!$get('ghg_protocol_activity_id')) {
                                                    return 'Select an activity to see available factors';
                                                }

                                                $factors = GhgProtocolEmissionFactor::where('activity_id', $get('ghg_protocol_activity_id'))
                                                    ->active()
                                                    ->with(['gas', 'inputUnit', 'outputUnit'])
                                                    ->get();

                                                if ($factors->isEmpty()) {
                                                    return 'No factors available for this activity';
                                                }

                                                $preview = $factors->map(function ($factor) {
                                                    return "• {$factor->gas->name}: {$factor->factor_value} {$factor->inputUnit->symbol} → {$factor->outputUnit->symbol}";
                                                })->join("\n");

                                                return $preview;
                                            })
                                            ->visible(fn (Forms\Get $get) => filled($get('ghg_protocol_activity_id')))
                                            ->columnSpanFull(),

                                        Forms\Components\Toggle::make('use_ghg_protocol')
                                            ->label('Use GHG Protocol Factors')
                                            ->helperText('Enable to use standardized GHG Protocol factors instead of custom factors')
                                            ->live()
                                            ->afterStateUpdated(function (Forms\Set $set, $state) {
                                                if ($state) {
                                                    $set('emission_factor_id', null);
                                                }
                                            }),
                                    ])
                                    ->columns(2),
                            ]),

                        Forms\Components\Tabs\Tab::make('Documentation')
                            ->schema([
                                Forms\Components\FileUpload::make('evidence')
                                    ->label('Supporting Documentation')
                                    ->helperText('Upload documents supporting activity data (e.g., invoices, meter readings).')
                                    //->required()
                                    ->disk('public')
                                    ->directory('activity-evidence')
                                    ->storeFileNamesIn('evidence_file_names')
                                    ->acceptedFileTypes(['application/pdf', 'image/*', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'])
                                    ->maxSize(10240)
                                    ->visibility('public'),

                                Forms\Components\Textarea::make('notes')
                                    ->maxLength(500)
                                    ->columnSpanFull(),
                            ]),
                    ])
                    ->columnSpanFull(),

                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\Toggle::make('auto_calculate_emissions')
                            ->label('Automatically calculate and save emissions')
                            ->helperText('Automatically create/update emissions record based on this activity data.')
                            ->default(true),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                // Ensure organization relation is explicitly loaded
                return $query->with([
                    'organization' => function($q) {
                        $q->select('id', 'name');
                    },
                    'facility',
                    'source'
                ]);
            })
            ->columns([
                // Use direct access to organization name with explicit fallback
                Tables\Columns\TextColumn::make('organization.name')
                    ->label('Organization')
                    ->getStateUsing(function ($record) {
                        return $record->organization ? $record->organization->name : 'Unknown';
                    })
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: false),

                Tables\Columns\TextColumn::make('date_recorded')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('source.name')
                    ->label('Source')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('facility.name')
                    ->label('Facility')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('quantity')
                    ->numeric(2)
                    ->sortable(),

                Tables\Columns\TextColumn::make('unit.name')
                    ->label('Unit')
                    ->sortable(),

                // Show calculated emissions based on emission factor
                Tables\Columns\TextColumn::make('calculated_emissions')
                    ->label('Emissions')
                    ->getStateUsing(function (ActivityData $record): string {
                        if (!$record->emissionFactor) {
                            return 'N/A';
                        }

                        $emissions = $record->quantity * $record->emissionFactor->factor_value;
                        return number_format($emissions, 4) . ' tCO₂e';
                    })
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->join('emission_factors', 'activity_data.emission_factor_id', '=', 'emission_factors.id')
                            ->orderByRaw('activity_data.quantity * emission_factors.factor_value ' . $direction)
                            ->select('activity_data.*');
                    }),

                Tables\Columns\TextColumn::make('emissionFactor.name')
                    ->label('Emission Factor')
                    ->toggleable(),

                Tables\Columns\IconColumn::make('has_emissions')
                    ->label('Emissions Recorded')
                    ->boolean()
                    ->getStateUsing(function (ActivityData $record): bool {
                        // Check if the emissions table has activity_data_id column
                        try {
                            return $record->emissions()->exists();
                        } catch (\Exception $e) {
                            // If the column doesn't exist, assume no emissions
                            return false;
                        }
                    })
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle'),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('organization_id')
                    ->label('Organization')
                    ->options(function () {
                        return \App\Models\Organization::pluck('name', 'id');
                    })
                    ->multiple()
                    ->preload(),

                Tables\Filters\SelectFilter::make('facility_organization')
                    ->label('Facility Organization')
                    ->options(function () {
                        return \App\Models\Organization::pluck('name', 'id');
                    })
                    ->query(function (Builder $query, array $data) {
                        if (empty($data['values'])) {
                            return $query;
                        }

                        return $query->whereHas('facility', function ($facilityQuery) use ($data) {
                            $facilityQuery->whereHas('organizationalUnit', function ($orgUnitQuery) use ($data) {
                                $orgUnitQuery->whereIn('organization_id', $data['values']);
                            });
                        });
                    })
                    ->multiple()
                    ->preload(),

                Tables\Filters\SelectFilter::make('facility_id')
                    ->relationship('facility', 'name')
                    ->multiple()
                    ->preload()
                    ->label('Facility'),

                Tables\Filters\SelectFilter::make('emission_source_id')
                    ->relationship('source', 'name')
                    ->multiple()
                    ->preload()
                    ->label('Emission Source'),

                Tables\Filters\Filter::make('date_recorded')
                    ->form([
                        Forms\Components\DatePicker::make('from'),
                        Forms\Components\DatePicker::make('until'),
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when(
                                $data['from'],
                                fn (Builder $query) => $query->whereDate('date_recorded', '>=', $data['from']),
                            )
                            ->when(
                                $data['until'],
                                fn (Builder $query) => $query->whereDate('date_recorded', '<=', $data['until']),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($data['from'] ?? null) {
                            $indicators['from'] = 'From ' . $data['from'];
                        }

                        if ($data['until'] ?? null) {
                            $indicators['until'] = 'Until ' . $data['until'];
                        }

                        return $indicators;
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),

                    Tables\Actions\BulkAction::make('bulkCalculateEmissions')
                        ->label('Calculate Emissions')
                        ->icon('heroicon-o-calculator')
                        ->action(function (Collection $records): void {
                            foreach ($records as $record) {
                                try {
                                    if (!$record->emissionFactor || $record->emissions()->exists()) {
                                        continue;
                                    }

                                    $emissions = EmissionCalculationService::calculate($record->quantity, $record->emission_factor_id);

                                    Emission::create([
                                        'facility_id' => $record->facility_id,
                                        'organization_id' => $record->organization_id,
                                        'emission_source_id' => $record->source_id,
                                        'activity_data_id' => $record->id,
                                        'emission_factor_id' => $record->emission_factor_id,
                                        'reporting_period' => $record->date_recorded,
                                        'scope' => $record->source ? $record->source->scope : '3',
                                        'emissions_value' => $emissions,
                                        'calculation_method' => 'activity_data',
                                        'emission_unit_id' => 1, // Default unit ID
                                    ]);
                                } catch (\Exception $e) {
                                    // Log error or handle gracefully
                                }
                            }
                        })
                        ->deselectRecordsAfterCompletion(),

                    Tables\Actions\BulkAction::make('exportCSV')
                        ->label('Export CSV')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->action(function (Collection $records) {
                            // Placeholder for CSV export logic
                        }),
                ]),
            ])
            ->defaultSort('date_recorded', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListActivityData::route('/'),
            'create' => Pages\CreateActivityData::route('/create'),
            'edit' => Pages\EditActivityData::route('/{record}/edit'),
        ];
    }
}