<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class UserQuickActionsWidget extends Widget
{
    protected static string $view = 'filament.widgets.user-quick-actions';
    
    protected int | string | array $columnSpan = 'full';

    public function getQuickActions(): array
    {
        return [
            [
                'title' => 'Add Emissions Data',
                'description' => 'Record your carbon-generating activities',
                'icon' => '📝',
                'url' => \App\Filament\Pages\AddDataPage::getUrl(),
                'color' => 'blue',
            ],
            [
                'title' => 'View My Report',
                'description' => 'Generate and download reports',
                'icon' => '📊',
                'url' => \App\Filament\Pages\SimpleReportsPage::getUrl(),
                'color' => 'green',
            ],
            [
                'title' => 'Update Progress',
                'description' => 'Check your goals and achievements',
                'icon' => '🎯',
                'url' => \App\Filament\Pages\MyProgressPage::getUrl(),
                'color' => 'purple',
            ],
            [
                'title' => 'Get Help',
                'description' => 'Access guides and support',
                'icon' => '❓',
                'url' => \App\Filament\Pages\UserGuide::getUrl(),
                'color' => 'yellow',
            ],
        ];
    }

    public function getRecentActivity(): array
    {
        return [
            [
                'icon' => '🚗',
                'title' => 'Business Travel',
                'description' => '250 miles • 0.12 tCO₂e',
                'date' => '2 days ago',
            ],
            [
                'icon' => '⚡',
                'title' => 'Office Energy Use',
                'description' => '1,250 kWh • 0.45 tCO₂e',
                'date' => '1 week ago',
            ],
            [
                'icon' => '✈️',
                'title' => 'Air Travel',
                'description' => 'NYC to LA • 1.2 tCO₂e',
                'date' => '2 weeks ago',
            ],
        ];
    }
}
