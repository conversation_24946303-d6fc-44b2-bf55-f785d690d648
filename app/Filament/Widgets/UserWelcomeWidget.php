<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class UserWelcomeWidget extends Widget
{
    protected static string $view = 'filament.widgets.user-welcome';
    
    protected int | string | array $columnSpan = 'full';

    public function getUserData(): array
    {
        $user = Auth::user();
        
        return [
            'name' => $user->name ?? 'User',
            'current_month' => now()->format('F Y'),
            'monthly_impact' => $this->getMonthlyImpact(),
            'improvement_percentage' => $this->getImprovementPercentage(),
            'streak_days' => $this->getStreakDays(),
        ];
    }

    protected function getMonthlyImpact(): float
    {
        // Simplified calculation - in production, this would use real data
        return round(rand(100, 500) / 10, 1);
    }

    protected function getImprovementPercentage(): int
    {
        return rand(5, 25);
    }

    protected function getStreakDays(): int
    {
        return rand(3, 14);
    }
}
