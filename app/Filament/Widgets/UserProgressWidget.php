<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class UserProgressWidget extends Widget
{
    protected static string $view = 'filament.widgets.user-progress';
    
    protected int | string | array $columnSpan = 'full';

    public function getProgressData(): array
    {
        return [
            'annual_target' => 50.0,
            'current_progress' => 33.5,
            'progress_percentage' => 67,
            'monthly_data' => $this->getMonthlyData(),
        ];
    }

    protected function getMonthlyData(): array
    {
        return [
            'Jan' => ['target' => 4.0, 'actual' => 4.2, 'percentage' => 105],
            'Feb' => ['target' => 4.0, 'actual' => 3.8, 'percentage' => 95],
            'Mar' => ['target' => 4.0, 'actual' => 4.5, 'percentage' => 112],
            'Apr' => ['target' => 4.0, 'actual' => 3.2, 'percentage' => 80],
            'May' => ['target' => 4.0, 'actual' => 0, 'percentage' => 0],
        ];
    }
}
