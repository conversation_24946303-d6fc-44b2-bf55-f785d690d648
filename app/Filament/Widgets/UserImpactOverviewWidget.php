<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class UserImpactOverviewWidget extends Widget
{
    protected static string $view = 'filament.widgets.user-impact-overview';
    
    protected int | string | array $columnSpan = 'full';

    public function getImpactData(): array
    {
        $user = Auth::user();
        
        return [
            'monthly_impact' => $this->getMonthlyImpact(),
            'improvement_percentage' => $this->getImprovementPercentage(),
            'goal_progress' => $this->getGoalProgress(),
            'pending_tasks' => $this->getPendingTasksCount(),
        ];
    }

    protected function getMonthlyImpact(): float
    {
        // Simplified calculation - in production, this would use real data
        return round(rand(400, 600) / 10, 1);
    }

    protected function getImprovementPercentage(): int
    {
        return rand(8, 15);
    }

    protected function getGoalProgress(): int
    {
        return rand(60, 80);
    }

    protected function getPendingTasksCount(): int
    {
        return rand(2, 5);
    }
}
