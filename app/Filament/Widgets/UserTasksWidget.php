<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class UserTasksWidget extends Widget
{
    protected static string $view = 'filament.widgets.user-tasks';
    
    protected int | string | array $columnSpan = 'full';

    public function getTasksData(): array
    {
        return [
            'pending_tasks' => $this->getPendingTasks(),
            'task_summary' => $this->getTaskSummary(),
        ];
    }

    protected function getPendingTasks(): array
    {
        return [
            [
                'id' => 1,
                'title' => 'Submit Q1 Energy Usage Data',
                'description' => 'Enter electricity and heating data for January-March period',
                'due_date' => '2024-04-15',
                'priority' => 'high',
                'category' => 'Data Entry',
            ],
            [
                'id' => 2,
                'title' => 'Review Carbon Offset Proposals',
                'description' => 'Evaluate three carbon offset project proposals for approval',
                'due_date' => '2024-04-20',
                'priority' => 'medium',
                'category' => 'Review',
            ],
            [
                'id' => 3,
                'title' => 'Update Business Travel Log',
                'description' => 'Add recent client visits and conference attendance',
                'due_date' => '2024-04-18',
                'priority' => 'medium',
                'category' => 'Data Entry',
            ],
        ];
    }

    protected function getTaskSummary(): array
    {
        return [
            'total_pending' => 3,
            'total_overdue' => 1,
            'completion_rate' => 85,
        ];
    }

    public function markTaskComplete(int $taskId): void
    {
        \Filament\Notifications\Notification::make()
            ->title('✅ Task Completed!')
            ->body('Great job! Your task has been marked as complete.')
            ->success()
            ->send();
    }
}
