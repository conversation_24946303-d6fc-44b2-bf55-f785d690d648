<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;

class MyTasksPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';
    
    protected static string $view = 'filament.pages.my-tasks';
    
    protected static ?string $title = 'My Tasks';
    
    protected static ?string $navigationLabel = 'My Tasks';
    
    protected static ?int $navigationSort = 4;

    public function getTasksData(): array
    {
        return [
            'pending_tasks' => $this->getPendingTasks(),
            'completed_tasks' => $this->getCompletedTasks(),
            'overdue_tasks' => $this->getOverdueTasks(),
            'task_summary' => $this->getTaskSummary(),
        ];
    }

    protected function getPendingTasks(): array
    {
        return [
            [
                'id' => 1,
                'title' => 'Submit Q1 Energy Usage Data',
                'description' => 'Enter electricity and heating data for January-March period',
                'due_date' => '2024-04-15',
                'priority' => 'high',
                'category' => 'Data Entry',
                'estimated_time' => '15 minutes',
                'assigned_by' => '<PERSON>',
                'status' => 'pending',
            ],
            [
                'id' => 2,
                'title' => 'Review Carbon Offset Proposals',
                'description' => 'Evaluate three carbon offset project proposals for approval',
                'due_date' => '2024-04-20',
                'priority' => 'medium',
                'category' => 'Review',
                'estimated_time' => '30 minutes',
                'assigned_by' => 'Mike Chen',
                'status' => 'pending',
            ],
            [
                'id' => 3,
                'title' => 'Update Business Travel Log',
                'description' => 'Add recent client visits and conference attendance',
                'due_date' => '2024-04-18',
                'priority' => 'medium',
                'category' => 'Data Entry',
                'estimated_time' => '10 minutes',
                'assigned_by' => 'System',
                'status' => 'pending',
            ],
        ];
    }

    protected function getCompletedTasks(): array
    {
        return [
            [
                'id' => 4,
                'title' => 'Complete Sustainability Training',
                'description' => 'Finish online carbon management certification course',
                'completed_date' => '2024-04-10',
                'category' => 'Training',
                'status' => 'completed',
            ],
            [
                'id' => 5,
                'title' => 'Submit March Emissions Report',
                'description' => 'Monthly emissions summary for facility operations',
                'completed_date' => '2024-04-05',
                'category' => 'Reporting',
                'status' => 'completed',
            ],
        ];
    }

    protected function getOverdueTasks(): array
    {
        return [
            [
                'id' => 6,
                'title' => 'Equipment Efficiency Audit',
                'description' => 'Assess energy efficiency of manufacturing equipment',
                'due_date' => '2024-04-08',
                'priority' => 'high',
                'category' => 'Audit',
                'days_overdue' => 5,
                'status' => 'overdue',
            ],
        ];
    }

    protected function getTaskSummary(): array
    {
        return [
            'total_pending' => 3,
            'total_overdue' => 1,
            'total_completed_this_month' => 2,
            'completion_rate' => 85, // percentage
        ];
    }

    public function markTaskComplete(int $taskId): void
    {
        // In a real application, this would update the database
        // For now, we'll just show a success notification
        
        \Filament\Notifications\Notification::make()
            ->title('✅ Task Completed!')
            ->body('Great job! Your task has been marked as complete.')
            ->success()
            ->send();
    }

    public function requestExtension(int $taskId): void
    {
        \Filament\Notifications\Notification::make()
            ->title('📅 Extension Requested')
            ->body('Your extension request has been sent to your supervisor for approval.')
            ->info()
            ->send();
    }
}
