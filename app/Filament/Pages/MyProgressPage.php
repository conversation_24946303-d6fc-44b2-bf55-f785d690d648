<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;

class MyProgressPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar-square';
    
    protected static string $view = 'filament.pages.my-progress';
    
    protected static ?string $title = 'My Progress';
    
    protected static ?string $navigationLabel = 'My Progress';
    
    protected static ?int $navigationSort = 3;

    public function getProgressData(): array
    {
        $user = Auth::user();
        
        return [
            'annual_target' => 50.0, // tCO2e reduction target
            'current_progress' => 33.5, // tCO2e saved so far
            'progress_percentage' => 67,
            'monthly_data' => $this->getMonthlyProgressData(),
            'achievements' => $this->getUserAchievements(),
            'upcoming_milestones' => $this->getUpcomingMilestones(),
            'comparison_data' => $this->getComparisonData(),
        ];
    }

    protected function getMonthlyProgressData(): array
    {
        return [
            'Jan' => ['target' => 4.0, 'actual' => 4.2, 'percentage' => 105],
            'Feb' => ['target' => 4.0, 'actual' => 3.8, 'percentage' => 95],
            'Mar' => ['target' => 4.0, 'actual' => 4.5, 'percentage' => 112],
            'Apr' => ['target' => 4.0, 'actual' => 3.2, 'percentage' => 80],
            'May' => ['target' => 4.0, 'actual' => 0, 'percentage' => 0], // Current month
        ];
    }

    protected function getUserAchievements(): array
    {
        return [
            [
                'icon' => '🏆',
                'title' => 'Carbon Champion',
                'description' => 'Exceeded monthly target 3 times',
                'earned_date' => '2024-03-15',
                'points' => 100,
            ],
            [
                'icon' => '🌱',
                'title' => 'Green Streak',
                'description' => '14 days of consistent data entry',
                'earned_date' => '2024-04-01',
                'points' => 50,
            ],
            [
                'icon' => '📊',
                'title' => 'Data Detective',
                'description' => 'Submitted 25+ data entries',
                'earned_date' => '2024-02-28',
                'points' => 75,
            ],
        ];
    }

    protected function getUpcomingMilestones(): array
    {
        return [
            [
                'title' => 'Halfway Hero',
                'description' => 'Reach 50% of annual target',
                'current' => 67,
                'target' => 50,
                'status' => 'completed',
            ],
            [
                'title' => 'Three Quarter Champion',
                'description' => 'Reach 75% of annual target',
                'current' => 67,
                'target' => 75,
                'status' => 'in_progress',
            ],
            [
                'title' => 'Carbon Hero',
                'description' => 'Complete 100% of annual target',
                'current' => 67,
                'target' => 100,
                'status' => 'upcoming',
            ],
        ];
    }

    protected function getComparisonData(): array
    {
        return [
            'vs_last_year' => [
                'percentage' => 15,
                'direction' => 'improvement',
                'message' => 'You\'re doing 15% better than last year!',
            ],
            'vs_team_average' => [
                'percentage' => 8,
                'direction' => 'improvement',
                'message' => 'You\'re 8% above team average',
            ],
            'vs_company_average' => [
                'percentage' => 12,
                'direction' => 'improvement',
                'message' => 'You\'re 12% above company average',
            ],
        ];
    }
}
