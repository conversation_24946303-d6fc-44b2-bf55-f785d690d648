<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;

class SimpleReportsPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';
    
    protected static string $view = 'filament.pages.simple-reports';
    
    protected static ?string $title = 'My Reports';
    
    protected static ?string $navigationLabel = 'Reports';
    
    protected static ?int $navigationSort = 5;

    public function getReportsData(): array
    {
        return [
            'quick_reports' => $this->getQuickReports(),
            'recent_reports' => $this->getRecentReports(),
            'report_templates' => $this->getReportTemplates(),
        ];
    }

    protected function getQuickReports(): array
    {
        return [
            [
                'id' => 'monthly_summary',
                'title' => 'Monthly Carbon Summary',
                'description' => 'Your carbon footprint for the current month',
                'icon' => '📊',
                'estimated_time' => '2 minutes',
                'last_generated' => '2024-04-10',
                'type' => 'summary',
            ],
            [
                'id' => 'quarterly_progress',
                'title' => 'Quarterly Progress Report',
                'description' => 'Progress towards your quarterly carbon reduction goals',
                'icon' => '📈',
                'estimated_time' => '3 minutes',
                'last_generated' => '2024-04-01',
                'type' => 'progress',
            ],
            [
                'id' => 'activity_breakdown',
                'title' => 'Activity Breakdown',
                'description' => 'Detailed breakdown of your carbon-generating activities',
                'icon' => '🔍',
                'estimated_time' => '2 minutes',
                'last_generated' => '2024-04-08',
                'type' => 'detailed',
            ],
            [
                'id' => 'comparison_report',
                'title' => 'Peer Comparison',
                'description' => 'See how your performance compares to your colleagues',
                'icon' => '⚖️',
                'estimated_time' => '1 minute',
                'last_generated' => '2024-04-05',
                'type' => 'comparison',
            ],
        ];
    }

    protected function getRecentReports(): array
    {
        return [
            [
                'title' => 'March 2024 Carbon Summary',
                'generated_date' => '2024-04-01',
                'type' => 'Monthly Summary',
                'file_size' => '2.3 MB',
                'download_url' => '#',
            ],
            [
                'title' => 'Q1 2024 Progress Report',
                'generated_date' => '2024-04-01',
                'type' => 'Quarterly Progress',
                'file_size' => '1.8 MB',
                'download_url' => '#',
            ],
            [
                'title' => 'February 2024 Activity Breakdown',
                'generated_date' => '2024-03-01',
                'type' => 'Activity Breakdown',
                'file_size' => '3.1 MB',
                'download_url' => '#',
            ],
        ];
    }

    protected function getReportTemplates(): array
    {
        return [
            [
                'name' => 'Executive Summary',
                'description' => 'High-level overview for leadership',
                'icon' => '👔',
                'pages' => 2,
            ],
            [
                'name' => 'Detailed Analysis',
                'description' => 'Comprehensive data analysis',
                'icon' => '📋',
                'pages' => 8,
            ],
            [
                'name' => 'Compliance Report',
                'description' => 'Regulatory compliance documentation',
                'icon' => '⚖️',
                'pages' => 5,
            ],
        ];
    }

    public function generateReport(string $reportId): void
    {
        // In a real application, this would generate the actual report
        \Filament\Notifications\Notification::make()
            ->title('📊 Report Generation Started')
            ->body('Your report is being generated. You\'ll receive a notification when it\'s ready for download.')
            ->info()
            ->send();
    }

    public function downloadReport(string $reportId): void
    {
        // In a real application, this would trigger the download
        \Filament\Notifications\Notification::make()
            ->title('📥 Download Started')
            ->body('Your report download has started.')
            ->success()
            ->send();
    }
}
