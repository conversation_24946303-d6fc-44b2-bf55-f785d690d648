<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Placeholder;
use Filament\Notifications\Notification;
use Illuminate\Support\HtmlString;
use App\Services\UserContextService;
use App\Models\GhgProtocolSector;
use App\Models\GhgProtocolActivity;
use App\Models\GhgProtocolEmissionFactor;
use Illuminate\Support\Facades\Auth;

class AddDataPage extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-plus-circle';

    protected static string $view = 'filament.pages.add-data';

    protected static ?string $title = 'Add Emissions Data';

    protected static ?string $navigationLabel = 'Add Data';

    protected static ?int $navigationSort = 2;

    public ?array $data = [];

    public bool $advancedMode = false;

    protected UserContextService $userContextService;

    public function mount(): void
    {
        $this->userContextService = app(UserContextService::class);

        // Determine if user should see advanced mode by default
        $user = Auth::user();
        $this->advancedMode = $this->userContextService->shouldShowAdvancedFeatures($user);

        // Pre-fill form with user context and smart defaults
        $smartDefaults = $this->userContextService->getSmartDefaults($user);
        $this->form->fill($smartDefaults);
    }

    public function getTitle(): string
    {
        return $this->advancedMode ? 'Advanced Emissions Data Entry' : 'Add Emissions Data';
    }

    public function getSubheading(): ?string
    {
        $user = Auth::user();
        if ($this->advancedMode) {
            return 'Complete GHG Protocol methodology with technical parameters';
        }

        $facilityName = $user->facility?->name ?? 'your facility';
        return "Quick data entry for {$facilityName}";
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Wizard::make([
                    Wizard\Step::make('Activity Type')
                        ->description('What activity would you like to record?')
                        ->schema([
                            Section::make()
                                ->schema([
                                    Grid::make(2)
                                        ->schema([
                                            $this->getActivityCard('business_travel', '🚗', 'Business Travel', 'Car trips, taxi rides, rideshare'),
                                            $this->getActivityCard('air_travel', '✈️', 'Air Travel', 'Flights for business or work'),
                                            $this->getActivityCard('energy_use', '⚡', 'Energy Use', 'Electricity, heating, cooling'),
                                            $this->getActivityCard('manufacturing', '🏭', 'Manufacturing', 'Production processes, equipment'),
                                            $this->getActivityCard('shipping', '📦', 'Shipping & Freight', 'Package delivery, freight transport'),
                                            $this->getActivityCard('other', '📋', 'Other Activity', 'Custom or miscellaneous emissions'),
                                        ])
                                ])
                        ]),

                    Wizard\Step::make('Activity Details')
                        ->description('Tell us more about this activity')
                        ->schema([
                            Section::make()
                                ->schema([
                                    Grid::make(2)
                                        ->schema([
                                            TextInput::make('activity_description')
                                                ->label('Activity Description')
                                                ->placeholder('e.g., Trip to client meeting')
                                                ->required(),

                                            DatePicker::make('activity_date')
                                                ->label('Date')
                                                ->default(now())
                                                ->required(),
                                        ]),

                                    $this->getActivitySpecificFields(),
                                ])
                        ]),

                    Wizard\Step::make('Review & Submit')
                        ->description('Review your data and submit')
                        ->schema([
                            Section::make('Review Your Entry')
                                ->schema([
                                    Grid::make(1)
                                        ->schema([
                                            \Filament\Forms\Components\Placeholder::make('review')
                                                ->content(function (callable $get) {
                                                    $activityType = $get('activity_type');
                                                    $description = $get('activity_description');
                                                    $date = $get('activity_date');

                                                    $estimatedEmissions = $this->calculateEstimatedEmissions([
                                                    'activity_type' => $get('activity_type'),
                                                    'distance' => $get('distance'),
                                                    'energy_amount' => $get('energy_amount'),
                                                    'weight' => $get('weight'),
                                                ]);

                                                    return new HtmlString("
                                                        <div class='bg-gray-50 dark:bg-gray-800 rounded-lg p-4'>
                                                            <h3 class='font-semibold text-lg mb-3'>📋 Summary</h3>
                                                            <div class='space-y-2'>
                                                                <div><strong>Activity:</strong> {$this->getActivityTypeLabel($activityType)}</div>
                                                                <div><strong>Description:</strong> {$description}</div>
                                                                <div><strong>Date:</strong> {$date}</div>
                                                                <div class='text-lg font-semibold text-green-600 dark:text-green-400 mt-4'>
                                                                    <strong>Estimated Emissions:</strong> {$estimatedEmissions} tCO₂e
                                                                </div>
                                                            </div>
                                                        </div>
                                                    ");
                                                }),
                                        ])
                                ])
                        ])
                ])
                ->submitAction(\Filament\Forms\Components\Actions\Action::make('submit')
                    ->label('Submit Data')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->submit('submit'))
                ->skippable()
            ])
            ->statePath('data');
    }

    protected function getActivityCard(string $value, string $icon, string $title, string $description): Select
    {
        return Select::make('activity_type')
            ->hiddenLabel()
            ->options([
                $value => new HtmlString("
                    <div class='flex items-center p-4 border-2 border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors cursor-pointer'>
                        <div class='text-3xl mr-4'>{$icon}</div>
                        <div>
                            <div class='font-semibold text-gray-900 dark:text-white'>{$title}</div>
                            <div class='text-sm text-gray-600 dark:text-gray-400'>{$description}</div>
                        </div>
                    </div>
                ")
            ])
            ->required();
    }

    protected function getActivitySpecificFields(): Grid
    {
        return Grid::make(2)
            ->schema([
                TextInput::make('distance')
                    ->label('Distance')
                    ->suffix('miles')
                    ->numeric()
                    ->visible(fn (callable $get) => in_array($get('activity_type'), ['business_travel', 'air_travel'])),

                Select::make('vehicle_type')
                    ->label('Vehicle Type')
                    ->options([
                        'car_gasoline' => 'Car (Gasoline)',
                        'car_hybrid' => 'Car (Hybrid)',
                        'car_electric' => 'Car (Electric)',
                        'taxi' => 'Taxi/Rideshare',
                        'bus' => 'Bus',
                        'train' => 'Train',
                    ])
                    ->visible(fn (callable $get) => $get('activity_type') === 'business_travel'),

                Select::make('flight_type')
                    ->label('Flight Type')
                    ->options([
                        'domestic_short' => 'Domestic Short Haul (<500 miles)',
                        'domestic_long' => 'Domestic Long Haul (>500 miles)',
                        'international' => 'International',
                    ])
                    ->visible(fn (callable $get) => $get('activity_type') === 'air_travel'),

                TextInput::make('energy_amount')
                    ->label('Energy Amount')
                    ->suffix('kWh')
                    ->numeric()
                    ->visible(fn (callable $get) => $get('activity_type') === 'energy_use'),

                Select::make('energy_type')
                    ->label('Energy Type')
                    ->options([
                        'electricity' => 'Electricity',
                        'natural_gas' => 'Natural Gas',
                        'heating_oil' => 'Heating Oil',
                        'propane' => 'Propane',
                    ])
                    ->visible(fn (callable $get) => $get('activity_type') === 'energy_use'),

                TextInput::make('weight')
                    ->label('Weight/Volume')
                    ->suffix('lbs')
                    ->numeric()
                    ->visible(fn (callable $get) => in_array($get('activity_type'), ['shipping', 'manufacturing'])),

                Textarea::make('notes')
                    ->label('Additional Notes')
                    ->placeholder('Any additional details about this activity...')
                    ->columnSpanFull(),
            ]);
    }

    protected function getActivityTypeLabel(?string $type): string
    {
        return match($type) {
            'business_travel' => '🚗 Business Travel',
            'air_travel' => '✈️ Air Travel',
            'energy_use' => '⚡ Energy Use',
            'manufacturing' => '🏭 Manufacturing',
            'shipping' => '📦 Shipping & Freight',
            'other' => '📋 Other Activity',
            default => 'Unknown Activity',
        };
    }

    protected function calculateEstimatedEmissions(array $data): string
    {
        // Simplified calculation - in production, this would use real emission factors
        $emissions = match($data['activity_type'] ?? null) {
            'business_travel' => ($data['distance'] ?? 100) * 0.0004,
            'air_travel' => ($data['distance'] ?? 500) * 0.0002,
            'energy_use' => ($data['energy_amount'] ?? 1000) * 0.0005,
            'manufacturing' => ($data['weight'] ?? 1000) * 0.001,
            'shipping' => ($data['weight'] ?? 100) * 0.0008,
            default => 0.1,
        };

        return number_format($emissions, 3);
    }

    public function submit(): void
    {
        $data = $this->form->getState();

        // Here you would save the data to the database
        // For now, we'll just show a success notification
        // TODO: Implement actual data saving: EmissionRecord::create($data);
        // Data contains: activity_type, activity_description, activity_date, etc.

        Notification::make()
            ->title('✅ Data Added Successfully!')
            ->body('Your emissions data has been recorded and will be included in your carbon footprint calculations.')
            ->success()
            ->send();

        // Reset the form
        $this->form->fill();

        // Redirect to dashboard
        $this->redirect(UserDashboard::getUrl());
    }
}
