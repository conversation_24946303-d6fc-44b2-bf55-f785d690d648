<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\UserImpactOverviewWidget;
use App\Filament\Widgets\UserQuickActionsWidget;
use App\Filament\Widgets\UserProgressWidget;
use App\Filament\Widgets\UserTasksWidget;
use App\Filament\Widgets\UserWelcomeWidget;
use Filament\Pages\Page;
use Filament\Support\Facades\FilamentView;

class UserDashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-home';
    
    protected static string $view = 'filament.pages.user-dashboard';
    
    protected static ?string $title = 'My Carbon Dashboard';
    
    protected static ?string $navigationLabel = 'Dashboard';
    
    protected static ?int $navigationSort = 1;
    
    protected static ?string $slug = 'dashboard';

    public function getTitle(): string
    {
        return 'My Carbon Dashboard';
    }

    protected function getHeaderWidgets(): array
    {
        return [
            UserWelcomeWidget::class,
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            UserImpactOverviewWidget::class,
            UserProgressWidget::class,
            UserTasksWidget::class,
            UserQuickActionsWidget::class,
        ];
    }

    public function getWidgetData(): array
    {
        return [
            'user' => auth()->user(),
            'current_month' => now()->format('F Y'),
            'organization' => auth()->user()->organization ?? null,
        ];
    }
}
