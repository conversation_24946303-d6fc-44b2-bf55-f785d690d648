<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\UserImpactOverviewWidget;
use App\Filament\Widgets\UserQuickActionsWidget;
use App\Filament\Widgets\UserProgressWidget;
use App\Filament\Widgets\UserTasksWidget;
use App\Filament\Widgets\UserWelcomeWidget;
use App\Services\UserContextService;
use App\Services\DashboardService;
use Filament\Pages\Page;
use Filament\Support\Facades\FilamentView;
use Illuminate\Support\Facades\Auth;

class UserDashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static string $view = 'filament.pages.user-dashboard';

    protected static ?string $title = 'My Carbon Dashboard';

    protected static ?string $navigationLabel = 'Dashboard';

    protected static ?int $navigationSort = 1;

    protected static ?string $slug = 'dashboard';

    protected UserContextService $userContextService;
    protected DashboardService $dashboardService;

    public function boot(): void
    {
        $this->userContextService = app(UserContextService::class);
        $this->dashboardService = app(DashboardService::class);
    }

    public function getTitle(): string
    {
        $user = Auth::user();
        $interfaceType = $this->userContextService->getUserInterfaceType($user);

        return match ($interfaceType) {
            'executive' => 'Executive Dashboard',
            'advanced' => 'Advanced Carbon Management',
            default => 'My Carbon Dashboard',
        };
    }

    public function getSubheading(): ?string
    {
        $user = Auth::user();

        $parts = array_filter([
            $user->facility?->name,
            $user->department,
            $user->organization?->name,
        ]);

        return implode(' • ', $parts);
    }

    protected function getHeaderWidgets(): array
    {
        $user = Auth::user();
        $interfaceType = $this->userContextService->getUserInterfaceType($user);

        return match ($interfaceType) {
            'executive' => [
                // Executive widgets would go here
                UserWelcomeWidget::class,
            ],
            'advanced' => [
                UserWelcomeWidget::class,
            ],
            default => [
                UserWelcomeWidget::class,
            ],
        };
    }

    protected function getFooterWidgets(): array
    {
        $user = Auth::user();
        $interfaceType = $this->userContextService->getUserInterfaceType($user);

        return match ($interfaceType) {
            'executive' => [
                // Executive-specific widgets
                UserImpactOverviewWidget::class,
                UserProgressWidget::class,
            ],
            'advanced' => [
                // Advanced user widgets with technical details
                UserImpactOverviewWidget::class,
                UserProgressWidget::class,
                UserTasksWidget::class,
                UserQuickActionsWidget::class,
            ],
            default => [
                // Simplified user widgets
                UserImpactOverviewWidget::class,
                UserProgressWidget::class,
                UserTasksWidget::class,
                UserQuickActionsWidget::class,
            ],
        };
    }

    public function getWidgetData(): array
    {
        $user = Auth::user();
        $dashboardData = $this->dashboardService->getDashboardData($user);

        return array_merge($dashboardData, [
            'user' => $user,
            'current_month' => now()->format('F Y'),
            'organization' => $user->organization,
            'facility' => $user->facility,
            'interface_type' => $dashboardData['interface_type'],
        ]);
    }

    /**
     * Get dashboard configuration for the current user
     */
    public function getDashboardConfig(): array
    {
        $user = Auth::user();
        return $this->userContextService->getDashboardConfig($user);
    }

    /**
     * Check if user should see advanced features
     */
    public function shouldShowAdvancedFeatures(): bool
    {
        $user = Auth::user();
        return $this->userContextService->shouldShowAdvancedFeatures($user);
    }

    /**
     * Get user's organizational context
     */
    public function getOrganizationalContext(): array
    {
        $user = Auth::user();
        return $this->userContextService->getOrganizationalContext($user);
    }
}
