<?php

namespace App\Contracts;

interface ModuleInterface
{
    /**
     * Get module information
     */
    public function getModuleInfo(): array;

    /**
     * Activate the module
     */
    public function activate(): array;

    /**
     * Deactivate the module
     */
    public function deactivate(): array;

    /**
     * Check if module is compatible with current system
     */
    public function isCompatible(): bool;

    /**
     * Get module configuration schema
     */
    public function getConfigurationSchema(): array;

    /**
     * Get default configuration
     */
    public function getDefaultConfiguration(): array;

    /**
     * Validate configuration
     */
    public function validateConfiguration(array $config): array;

    /**
     * Install module (create tables, seed data, etc.)
     */
    public function install(): array;

    /**
     * Uninstall module (remove tables, clean data, etc.)
     */
    public function uninstall(): array;

    /**
     * Update module to new version
     */
    public function update(string $fromVersion, string $toVersion): array;

    /**
     * Get module status
     */
    public function getStatus(): array;
}
