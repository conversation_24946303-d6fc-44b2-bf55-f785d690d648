<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ERPIntegrationService;
use App\Services\IoTSensorService;
use App\Services\SupplyChainService;
use App\Services\DataLakeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ExternalDataController extends Controller
{
    protected ERPIntegrationService $erpService;
    protected IoTSensorService $iotService;
    protected SupplyChainService $supplyChainService;
    protected DataLakeService $dataLakeService;

    public function __construct(
        ERPIntegrationService $erpService,
        IoTSensorService $iotService,
        SupplyChainService $supplyChainService,
        DataLakeService $dataLakeService
    ) {
        $this->erpService = $erpService;
        $this->iotService = $iotService;
        $this->supplyChainService = $supplyChainService;
        $this->dataLakeService = $dataLakeService;
    }

    /**
     * Handle ERP data webhook
     */
    public function handleERPData(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'system_type' => 'required|string|in:sap,oracle',
            'facility_id' => 'required|string',
            'data' => 'required|array',
            'timestamp' => 'required|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 400);
        }

        try {
            // Store raw data in data lake
            $dataId = $this->dataLakeService->storeRawData('erp_webhook', $request->input('data'), [
                'system_type' => $request->input('system_type'),
                'facility_id' => $request->input('facility_id'),
                'webhook_timestamp' => $request->input('timestamp'),
                'source_ip' => $request->ip(),
            ]);

            // Process the data based on system type
            $result = $this->processERPWebhookData($request->all());

            Log::info('ERP webhook data processed', [
                'data_id' => $dataId,
                'system_type' => $request->input('system_type'),
                'facility_id' => $request->input('facility_id'),
            ]);

            return response()->json([
                'success' => true,
                'data_id' => $dataId,
                'processed_records' => $result['processed_records'] ?? 0,
                'message' => 'Data received and processed successfully',
            ]);

        } catch (\Exception $e) {
            Log::error('ERP webhook processing failed: ' . $e->getMessage(), [
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to process ERP data',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Handle IoT sensor data webhook
     */
    public function handleSensorData(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'sensor_id' => 'required|string',
            'data' => 'required|array',
            'timestamp' => 'required|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 400);
        }

        try {
            $sensorId = $request->input('sensor_id');
            $data = $request->input('data');

            // Ingest sensor data
            $success = $this->iotService->ingestSensorData($sensorId, $data);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'sensor_id' => $sensorId,
                    'message' => 'Sensor data ingested successfully',
                ]);
            }

            return response()->json([
                'success' => false,
                'error' => 'Failed to ingest sensor data',
            ], 500);

        } catch (\Exception $e) {
            Log::error('Sensor webhook processing failed: ' . $e->getMessage(), [
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to process sensor data',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Handle supplier data webhook
     */
    public function handleSupplierData(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'supplier_id' => 'required|string',
            'portal_type' => 'required|string',
            'data' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 400);
        }

        try {
            $supplierId = $request->input('supplier_id');
            $data = $request->input('data');

            // Validate Scope 3 data
            $validation = $this->supplyChainService->validateScope3Data($data);

            if (!$validation['valid']) {
                return response()->json([
                    'success' => false,
                    'errors' => $validation['errors'],
                    'warnings' => $validation['warnings'],
                ], 400);
            }

            // Import validated data
            $result = $this->supplyChainService->importScope3Data($validation['processed_data'], $supplierId);

            return response()->json([
                'success' => true,
                'supplier_id' => $supplierId,
                'imported_records' => $result['total_imported'],
                'errors' => $result['total_errors'],
                'message' => 'Supplier data processed successfully',
            ]);

        } catch (\Exception $e) {
            Log::error('Supplier webhook processing failed: ' . $e->getMessage(), [
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to process supplier data',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Bulk data upload endpoint
     */
    public function bulkUpload(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'source_type' => 'required|string|in:erp,iot,supplier,manual',
            'data' => 'required|array',
            'metadata' => 'array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 400);
        }

        try {
            $sourceType = $request->input('source_type');
            $data = $request->input('data');
            $metadata = $request->input('metadata', []);

            // Store in data lake
            $dataId = $this->dataLakeService->storeRawData($sourceType . '_bulk', $data, array_merge($metadata, [
                'upload_type' => 'bulk',
                'record_count' => count($data),
                'uploaded_by' => auth()->user()->id ?? 'api',
            ]));

            // Process based on source type
            $processedCount = $this->processBulkData($sourceType, $data, $metadata);

            return response()->json([
                'success' => true,
                'data_id' => $dataId,
                'total_records' => count($data),
                'processed_records' => $processedCount,
                'message' => 'Bulk data uploaded and processed successfully',
            ]);

        } catch (\Exception $e) {
            Log::error('Bulk upload processing failed: ' . $e->getMessage(), [
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to process bulk upload',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get data lake statistics
     */
    public function getDataLakeStats(): JsonResponse
    {
        try {
            $stats = $this->dataLakeService->getStatistics();

            return response()->json([
                'success' => true,
                'statistics' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve statistics',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test external system connections
     */
    public function testConnections(): JsonResponse
    {
        try {
            $results = [
                'erp' => [
                    'sap' => $this->erpService->testConnection('sap'),
                    'oracle' => $this->erpService->testConnection('oracle'),
                ],
                'iot_sensors' => $this->getIoTSensorStatus(),
                'suppliers' => $this->getSupplierConnectionStatus(),
            ];

            return response()->json([
                'success' => true,
                'connections' => $results,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to test connections',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Process ERP webhook data
     */
    protected function processERPWebhookData(array $data): array
    {
        // Implementation depends on specific ERP data format
        // This is a placeholder for processing logic
        return ['processed_records' => count($data['data'] ?? [])];
    }

    /**
     * Process bulk data based on source type
     */
    protected function processBulkData(string $sourceType, array $data, array $metadata): int
    {
        $processedCount = 0;

        foreach ($data as $record) {
            try {
                switch ($sourceType) {
                    case 'supplier':
                        if (isset($metadata['supplier_id'])) {
                            $validation = $this->supplyChainService->validateScope3Data([$record]);
                            if ($validation['valid']) {
                                $this->supplyChainService->importScope3Data($validation['processed_data'], $metadata['supplier_id']);
                                $processedCount++;
                            }
                        }
                        break;

                    case 'iot':
                        if (isset($metadata['sensor_id'])) {
                            if ($this->iotService->ingestSensorData($metadata['sensor_id'], $record)) {
                                $processedCount++;
                            }
                        }
                        break;

                    case 'erp':
                        // Process ERP data
                        $processedCount++;
                        break;

                    case 'manual':
                        // Process manual data entry
                        $processedCount++;
                        break;
                }
            } catch (\Exception $e) {
                Log::error("Failed to process bulk record: " . $e->getMessage(), ['record' => $record]);
            }
        }

        return $processedCount;
    }

    /**
     * Get IoT sensor status
     */
    protected function getIoTSensorStatus(): array
    {
        $sensors = $this->iotService->listSensors();
        $status = ['total' => $sensors->count(), 'online' => 0, 'offline' => 0];

        foreach ($sensors as $sensor) {
            $sensorStatus = $this->iotService->getSensorStatus($sensor->id);
            if ($sensorStatus['status'] === 'online') {
                $status['online']++;
            } else {
                $status['offline']++;
            }
        }

        return $status;
    }

    /**
     * Get supplier connection status
     */
    protected function getSupplierConnectionStatus(): array
    {
        // This would query the supplier_connections table
        // Placeholder implementation
        return ['total' => 0, 'active' => 0, 'inactive' => 0];
    }
}
