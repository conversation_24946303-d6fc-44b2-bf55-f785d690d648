<?php

namespace App\Modules;

use App\Contracts\ModuleInterface;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

abstract class BaseModule implements ModuleInterface
{
    protected string $moduleName;
    protected string $moduleVersion;
    protected string $moduleDescription;
    protected array $moduleDependencies = [];
    protected bool $isCoreModule = false;
    protected array $requiredPermissions = [];
    protected array $providedServices = [];

    /**
     * Get module information
     */
    public function getModuleInfo(): array
    {
        return [
            'name' => $this->moduleName,
            'version' => $this->moduleVersion,
            'description' => $this->moduleDescription,
            'dependencies' => $this->moduleDependencies,
            'is_core' => $this->isCoreModule,
            'required_permissions' => $this->requiredPermissions,
            'provided_services' => $this->providedServices,
            'author' => $this->getAuthor(),
            'license' => $this->getLicense(),
            'compatibility' => $this->getCompatibilityInfo(),
        ];
    }

    /**
     * Activate the module
     */
    public function activate(): array
    {
        try {
            Log::info("Activating module: {$this->moduleName}");

            // Check compatibility
            if (!$this->isCompatible()) {
                throw new \Exception("Module is not compatible with current system");
            }

            // Install if not already installed
            if (!$this->isInstalled()) {
                $installResult = $this->install();
                if (!$installResult['success']) {
                    throw new \Exception("Module installation failed: " . $installResult['error']);
                }
            }

            // Register services
            $this->registerServices();

            // Register permissions
            $this->registerPermissions();

            // Run activation hooks
            $this->onActivate();

            Log::info("Module activated successfully: {$this->moduleName}");

            return [
                'success' => true,
                'message' => 'Module activated successfully',
                'module' => $this->moduleName,
                'services_registered' => count($this->providedServices),
                'permissions_registered' => count($this->requiredPermissions),
            ];

        } catch (\Exception $e) {
            Log::error("Module activation failed: {$this->moduleName}", [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Deactivate the module
     */
    public function deactivate(): array
    {
        try {
            Log::info("Deactivating module: {$this->moduleName}");

            // Run deactivation hooks
            $this->onDeactivate();

            // Unregister services
            $this->unregisterServices();

            Log::info("Module deactivated successfully: {$this->moduleName}");

            return [
                'success' => true,
                'message' => 'Module deactivated successfully',
                'module' => $this->moduleName,
            ];

        } catch (\Exception $e) {
            Log::error("Module deactivation failed: {$this->moduleName}", [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check if module is compatible with current system
     */
    public function isCompatible(): bool
    {
        // Check Laravel version
        $laravelVersion = app()->version();
        $requiredLaravelVersion = $this->getRequiredLaravelVersion();
        
        if ($requiredLaravelVersion && version_compare($laravelVersion, $requiredLaravelVersion, '<')) {
            return false;
        }

        // Check PHP version
        $phpVersion = PHP_VERSION;
        $requiredPhpVersion = $this->getRequiredPhpVersion();
        
        if ($requiredPhpVersion && version_compare($phpVersion, $requiredPhpVersion, '<')) {
            return false;
        }

        // Check dependencies
        foreach ($this->moduleDependencies as $dependency) {
            if (!$this->isDependencyMet($dependency)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get module configuration schema
     */
    public function getConfigurationSchema(): array
    {
        return [
            'type' => 'object',
            'properties' => [
                'enabled' => [
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Enable/disable module functionality',
                ],
                'debug_mode' => [
                    'type' => 'boolean',
                    'default' => false,
                    'description' => 'Enable debug logging for this module',
                ],
            ],
            'required' => ['enabled'],
        ];
    }

    /**
     * Get default configuration
     */
    public function getDefaultConfiguration(): array
    {
        return [
            'enabled' => true,
            'debug_mode' => false,
            'auto_update' => false,
            'cache_enabled' => true,
            'cache_ttl' => 3600,
        ];
    }

    /**
     * Validate configuration
     */
    public function validateConfiguration(array $config): array
    {
        $errors = [];
        $schema = $this->getConfigurationSchema();

        // Basic validation - can be extended with JSON Schema validator
        foreach ($schema['required'] ?? [] as $requiredField) {
            if (!isset($config[$requiredField])) {
                $errors[] = "Required field missing: {$requiredField}";
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Install module
     */
    public function install(): array
    {
        try {
            Log::info("Installing module: {$this->moduleName}");

            // Create database tables
            $this->createTables();

            // Seed initial data
            $this->seedData();

            // Create configuration
            $this->createConfiguration();

            Log::info("Module installed successfully: {$this->moduleName}");

            return [
                'success' => true,
                'message' => 'Module installed successfully',
            ];

        } catch (\Exception $e) {
            Log::error("Module installation failed: {$this->moduleName}", [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Uninstall module
     */
    public function uninstall(): array
    {
        try {
            Log::info("Uninstalling module: {$this->moduleName}");

            // Remove configuration
            $this->removeConfiguration();

            // Clean data
            $this->cleanData();

            // Drop database tables
            $this->dropTables();

            Log::info("Module uninstalled successfully: {$this->moduleName}");

            return [
                'success' => true,
                'message' => 'Module uninstalled successfully',
            ];

        } catch (\Exception $e) {
            Log::error("Module uninstallation failed: {$this->moduleName}", [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Update module
     */
    public function update(string $fromVersion, string $toVersion): array
    {
        try {
            Log::info("Updating module: {$this->moduleName} from {$fromVersion} to {$toVersion}");

            // Run version-specific migrations
            $this->runMigrations($fromVersion, $toVersion);

            // Update configuration if needed
            $this->updateConfiguration($fromVersion, $toVersion);

            Log::info("Module updated successfully: {$this->moduleName}");

            return [
                'success' => true,
                'message' => 'Module updated successfully',
                'from_version' => $fromVersion,
                'to_version' => $toVersion,
            ];

        } catch (\Exception $e) {
            Log::error("Module update failed: {$this->moduleName}", [
                'from_version' => $fromVersion,
                'to_version' => $toVersion,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get module status
     */
    public function getStatus(): array
    {
        return [
            'module' => $this->moduleName,
            'version' => $this->moduleVersion,
            'installed' => $this->isInstalled(),
            'compatible' => $this->isCompatible(),
            'tables_exist' => $this->checkTablesExist(),
            'configuration_valid' => $this->isConfigurationValid(),
            'dependencies_met' => $this->checkDependencies(),
            'last_check' => now(),
        ];
    }

    // Abstract methods to be implemented by concrete modules
    abstract protected function createTables(): void;
    abstract protected function dropTables(): void;
    abstract protected function seedData(): void;
    abstract protected function cleanData(): void;

    // Hook methods that can be overridden
    protected function onActivate(): void {}
    protected function onDeactivate(): void {}
    protected function registerServices(): void {}
    protected function unregisterServices(): void {}
    protected function registerPermissions(): void {}

    // Helper methods
    protected function getAuthor(): string
    {
        return 'Carbon Management System';
    }

    protected function getLicense(): string
    {
        return 'MIT';
    }

    protected function getCompatibilityInfo(): array
    {
        return [
            'laravel_version' => $this->getRequiredLaravelVersion(),
            'php_version' => $this->getRequiredPhpVersion(),
        ];
    }

    protected function getRequiredLaravelVersion(): ?string
    {
        return '11.0';
    }

    protected function getRequiredPhpVersion(): ?string
    {
        return '8.2';
    }

    protected function isDependencyMet(string $dependency): bool
    {
        // Check if dependency module is installed and active
        return true; // Simplified for now
    }

    protected function isInstalled(): bool
    {
        return $this->checkTablesExist();
    }

    protected function checkTablesExist(): bool
    {
        $tables = $this->getRequiredTables();
        
        foreach ($tables as $table) {
            if (!Schema::hasTable($table)) {
                return false;
            }
        }
        
        return true;
    }

    protected function getRequiredTables(): array
    {
        return [];
    }

    protected function isConfigurationValid(): bool
    {
        $config = $this->getModuleConfiguration();
        $validation = $this->validateConfiguration($config);
        
        return $validation['valid'];
    }

    protected function getModuleConfiguration(): array
    {
        return app(\App\Services\ModuleManagerService::class)
            ->getModuleConfiguration($this->moduleName);
    }

    protected function checkDependencies(): bool
    {
        foreach ($this->moduleDependencies as $dependency) {
            if (!$this->isDependencyMet($dependency)) {
                return false;
            }
        }
        
        return true;
    }

    protected function createConfiguration(): void
    {
        // Override in concrete modules if needed
    }

    protected function removeConfiguration(): void
    {
        // Override in concrete modules if needed
    }

    protected function runMigrations(string $fromVersion, string $toVersion): void
    {
        // Override in concrete modules if needed
    }

    protected function updateConfiguration(string $fromVersion, string $toVersion): void
    {
        // Override in concrete modules if needed
    }
}
