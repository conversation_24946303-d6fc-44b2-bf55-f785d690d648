<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;

// Database configuration
$capsule = new Capsule;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => 'localhost',
    'database' => 'carbon_management',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

// 2014 US Regional Electricity Emission Factors Data
$data_2014 = [
    ['ASCC Alaska Grid', 926.50, 46.60, 7.20],
    ['ASCC Miscellaneous', 680.50, 36.10, 6.00],
    ['ERCOT All', 1142.80, 81.80, 11.60],
    ['FRCC All', 1075.20, 87.80, 12.10],
    ['HICC Miscellaneous', 940.80, 95.30, 15.20],
    ['HICC Oahu', 1479.40, 159.40, 24.50],
    ['MRO East', 1663.80, 191.20, 28.20],
    ['MRO West', 1365.10, 161.40, 23.30],
    ['NPCC Long Island', 1196.20, 132.40, 17.20],
    ['NPCC New England', 570.90, 96.00, 12.80],
    ['NPCC NYC/Westchester', 665.50, 24.40, 3.00],
    ['NPCC Upstate NY', 365.70, 30.70, 4.10],
    ['RFC East', 829.40, 73.90, 11.20],
    ['RFC Michigan', 1531.50, 170.10, 24.50],
    ['RFC West', 1380.90, 150.20, 22.00],
    ['SERC Midwest', 1772.00, 208.60, 30.40],
    ['SERC Mississippi Valley', 1022.00, 78.60, 11.20],
    ['SERC South', 1143.80, 103.70, 15.30],
    ['SERC Tennessee Valley', 1336.30, 138.60, 20.20],
    ['SERC Virginia/Carolina', 856.60, 95.70, 13.80],
    ['SPP North', 1575.00, 173.80, 25.20],
    ['SPP South', 1475.90, 135.40, 19.70],
    ['WECC California', 568.60, 33.10, 4.00],
    ['WECC Northwest', 907.00, 97.80, 14.20],
    ['WECC Rockies', 1737.70, 178.20, 25.80],
    ['WECC Southwest', 875.60, 66.40, 9.30]
];

echo "Starting import of 2014 US Regional Electricity Emission Factors...\n";

try {
    // Start transaction
    Capsule::beginTransaction();
    
    $imported_count = 0;
    
    foreach ($data_2014 as $row) {
        [$region_name, $co2_rate, $ch4_rate, $n2o_rate] = $row;
        
        // Insert emission factor record
        $emission_factor_id = Capsule::table('emission_factors')->insertGetId([
            'name' => "US Electricity - {$region_name} (2014)",
            'category' => 'Electricity',
            'scope' => 'Scope 2',
            'source' => 'EPA eGRID',
            'year' => 2014,
            'region' => 'United States',
            'unit' => 'lb/MWh',
            'methodology' => 'eGRID subregion annual output emission rates',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Insert CO2 emission factor
        Capsule::table('emission_factor_values')->insert([
            'emission_factor_id' => $emission_factor_id,
            'gas_type' => 'CO2',
            'value' => $co2_rate,
            'unit' => 'lb/MWh',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Insert CH4 emission factor
        Capsule::table('emission_factor_values')->insert([
            'emission_factor_id' => $emission_factor_id,
            'gas_type' => 'CH4',
            'value' => $ch4_rate,
            'unit' => 'lb/MWh',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Insert N2O emission factor
        Capsule::table('emission_factor_values')->insert([
            'emission_factor_id' => $emission_factor_id,
            'gas_type' => 'N2O',
            'value' => $n2o_rate,
            'unit' => 'lb/MWh',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Add metadata for eGRID subregion
        Capsule::table('emission_factor_metadata')->insert([
            'emission_factor_id' => $emission_factor_id,
            'key' => 'egrid_subregion',
            'value' => $region_name,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Add metadata for data source details
        Capsule::table('emission_factor_metadata')->insert([
            'emission_factor_id' => $emission_factor_id,
            'key' => 'data_source_detail',
            'value' => 'EPA eGRID 2014 - Year 2014 US Regional Electricity Emission Factors for CO2, CH4 and N2O',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        $imported_count++;
        echo "Imported: {$region_name}\n";
    }
    
    // Commit transaction
    Capsule::commit();
    
    echo "\nSuccessfully imported {$imported_count} emission factors for 2014 US Regional Electricity.\n";
    echo "Total records created:\n";
    echo "- Emission factors: {$imported_count}\n";
    echo "- Emission factor values: " . ($imported_count * 3) . " (CO2, CH4, N2O for each region)\n";
    echo "- Metadata records: " . ($imported_count * 2) . "\n";
    
} catch (Exception $e) {
    // Rollback transaction on error
    Capsule::rollback();
    echo "Error importing data: " . $e->getMessage() . "\n";
    exit(1);
}

function now() {
    return date('Y-m-d H:i:s');
}

echo "\nImport completed successfully!\n";
